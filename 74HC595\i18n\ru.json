{"toolbox_name": "Сдвиговый регистр", "74hc595_create": {"message0": "Инициализировать 74HC595 %1 количество %2 данные:%3  такт:%4 защёлка:%5", "args0": [null, null, {"options": "${board.digitalPins}"}, {"options": "${board.digitalPins}"}, {"options": "${board.digitalPins}"}]}, "74hc595_set": {"message0": "74HC595 %1 установить пин %2 в %3", "args0": [null, {"options": "${board.digitalPins}"}, {"options": [["HIGH", "HIGH"], ["LOW", "LOW"]]}]}, "74hc595_setAll": {"message0": "74HC595 %1 установить все пины в %2", "args0": [null, {"options": [["HIGH", "High"], ["LOW", "Low"]]}]}, "74hc595_setAllBin": {"message0": "74HC595%1 установить уровни выхода с помощью массива %2[]", "args0": [null, null]}, "74hc595_getstate": {"message0": "74HC595%1 получить состояние выхода %2", "args0": [null, null]}}