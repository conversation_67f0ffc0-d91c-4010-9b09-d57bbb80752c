{"kind": "category", "name": "Text", "icon": "fa-light fa-input-text", "contents": [{"kind": "block", "type": "char"}, {"kind": "block", "type": "text"}, {"kind": "block", "type": "string_add_string", "inputs": {"STRING1": {"shadow": {"type": "text", "fields": {"TEXT": "hello"}}}, "STRING2": {"shadow": {"type": "text", "fields": {"TEXT": "world"}}}}}, {"kind": "block", "type": "string_charAt", "inputs": {"STRING": {"shadow": {"type": "text", "fields": {"TEXT": "hello"}}}, "NUM": {"shadow": {"type": "math_number", "fields": {"NUM": 1}}}}}, {"kind": "block", "type": "string_to_something", "inputs": {"TEXT": {"shadow": {"type": "text", "fields": {"TEXT": "123"}}}}}, {"kind": "block", "type": "string_length", "inputs": {"STRING": {"shadow": {"type": "text", "fields": {"TEXT": "world"}}}}}, {"kind": "block", "type": "string_indexOf", "inputs": {"STRING1": {"shadow": {"type": "text", "fields": {"TEXT": "hello, world"}}}, "STRING2": {"shadow": {"type": "text", "fields": {"TEXT": "hello"}}}}}, {"kind": "block", "type": "string_substring", "inputs": {"STRING": {"shadow": {"type": "text", "fields": {"TEXT": "apple"}}}, "START_INDEX": {"shadow": {"type": "math_number", "fields": {"NUM": 1}}}, "LAST_INDEX": {"shadow": {"type": "math_number", "fields": {"NUM": 3}}}}}, {"kind": "block", "type": "string_find_str", "inputs": {"STRING1": {"shadow": {"type": "text", "fields": {"TEXT": "apple"}}}, "STRING2": {"shadow": {"type": "text", "fields": {"TEXT": "ap"}}}}}, {"kind": "block", "type": "string_startsWith", "inputs": {"TEXT": {"shadow": {"type": "text", "fields": {"TEXT": "abc"}}}, "PREFIX": {"shadow": {"type": "text", "fields": {"TEXT": ""}}}}}, {"kind": "block", "type": "string_endsWith", "inputs": {"TEXT": {"shadow": {"type": "text", "fields": {"TEXT": "abc"}}}, "SUFFIX": {"shadow": {"type": "text", "fields": {"TEXT": ""}}}}}, {"kind": "block", "type": "number_to"}, {"kind": "block", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"kind": "block", "type": "number_to_string", "inputs": {"NUM": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}}}, {"kind": "block", "type": "text_join"}, {"kind": "block", "type": "text_append", "inputs": {"TEXT": {"shadow": {"type": "text"}}}}, {"kind": "block", "type": "text_length", "inputs": {"VALUE": {"shadow": {"type": "text", "fields": {"TEXT": "abc"}}}}}, {"kind": "block", "type": "text_isEmpty", "inputs": {"VALUE": {"shadow": {"type": "text", "fields": {"TEXT": ""}}}}}, {"kind": "block", "type": "text_indexOf", "inputs": {"VALUE": {"block": {"type": "variables_get", "fields": {"VAR": "text"}}}, "FIND": {"shadow": {"type": "text", "fields": {"TEXT": "abc"}}}}}, {"kind": "block", "type": "text_charAt", "inputs": {"VALUE": {"block": {"type": "variables_get", "fields": {"VAR": "text"}}}}}, {"kind": "block", "type": "tt_getSubstring", "inputs": {"STRING": {"block": {"type": "variables_get", "fields": {"VAR": "text"}}}}}, {"kind": "block", "type": "text_changeCase", "inputs": {"TEXT": {"shadow": {"type": "text", "fields": {"TEXT": "abc"}}}}}, {"kind": "block", "type": "text_trim", "inputs": {"TEXT": {"shadow": {"type": "text", "fields": {"TEXT": "abc"}}}}}, {"kind": "block", "type": "text_count", "inputs": {"SUB": {"shadow": {"type": "text"}}, "TEXT": {"shadow": {"type": "text"}}}}, {"kind": "block", "type": "text_replace", "inputs": {"FROM": {"shadow": {"type": "text"}}, "TO": {"shadow": {"type": "text"}}, "TEXT": {"shadow": {"type": "text"}}}}, {"kind": "block", "type": "text_reverse", "inputs": {"TEXT": {"shadow": {"type": "text"}}}}]}