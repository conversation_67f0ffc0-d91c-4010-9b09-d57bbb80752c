[{"type": "shtc3_init", "message0": "初始化 SHTC3 温湿度传感器", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"type": "shtc3_read_temperature", "message0": "SHTC3 读取温度 (℃)", "inputsInline": true, "output": "Number", "colour": "#4CAF50"}, {"type": "shtc3_read_humidity", "message0": "SHTC3 读取湿度 (%)", "inputsInline": true, "output": "Number", "colour": "#4CAF50"}, {"type": "shtc3_read_both", "message0": "SHTC3 同时读取温湿度", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"type": "shtc3_is_connected", "message0": "SHTC3 传感器连接正常", "inputsInline": true, "output": "Boolean", "colour": "#4CAF50"}, {"type": "shtc3_sleep", "message0": "SHTC3 进入 %1 模式", "args0": [{"type": "field_dropdown", "name": "MODE", "options": [["睡眠", "sleep"], ["唤醒", "wakeup"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"type": "shtc3_set_power_mode", "message0": "SHTC3 设置 %1 功耗模式", "args0": [{"type": "field_dropdown", "name": "POWER_MODE", "options": [["正常", "normal"], ["低功耗", "lowpower"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}]