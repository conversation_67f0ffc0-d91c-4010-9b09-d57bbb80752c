# 库开发  

## 开发调试方法  

### 打开开发者模式
通过`菜单 > 设置 > 开发者模式`，打开开发者模式。此时blockly工作区左下角的`库管理`按钮右侧会出现🔁图标，该图标用于修改库源码后，手动重新加载库。

### 安装库
你可以先将库模板或自己创建的库，放到计算机任意位置，如`d:\lib-test`。
然后在aily blockly中打开项目，再打开终端，输入：
```
npm i d:\lib-test
```
即可为该项目安装上此库。

### 重新加载库
点击 `项目管理` 右侧的🔁图标，即可重新加载项目，加载后，左侧菜单即会显示新的库。

### 修改库
你可以直接修改项目目录下`node_modules\@aily-project\<库名称>`，然后在aily blockly重新加载测试；
也可以修改`d:\lib-test`下的原始库，然后再次安装库测试。

## 提交库 
你可以先fork本项目到个人仓库。然后将你新建的库放置到其中，再提交Pull requests。
联系 奈何col合并，或者等待项目其他管理员合并。  
合并完成后，即会推送到npm仓库，此后所有用户均可使用你开发库。

## 多语言支持  
可以在库目录下添加i18n目录为库提供多语言支持：
```
library-name  
 |- block.json             // aily blockly block文件
 |- generator.js           // aily blockly generator文件
 |- toolbox.json           // aily blockly toolbox文件
 |- package.json           // npm包管理文件
 |- src.7z                 // Arduino库源文件，请使用7z极限压缩后放在库中
 |- readme.md              // 说明文件，如果使用了开源库，请进行说明
 |- i18n                   // 多语言支持
     |- en.json            // 英文
     |- zh_cn.json         // 简体中文
     |- zh_tw.json         // 繁体中文
     |- xxxx.json          // 其他语言
```