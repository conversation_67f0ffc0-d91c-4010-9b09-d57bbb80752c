{"name": "@aily-project/lib-r4-can", "nickname": "R4 CAN总线通信库", "author": "<PERSON><PERSON><PERSON><PERSON>", "description": "用于Arduino UNO R4的CAN总线通信库，支持CAN消息的发送和接收", "version": "1.0.0", "compatibility": {"core": ["renesas_uno:minima", "renesas_uno:unor4wifi"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "can", "communication", "bus", "can_begin", "can_read", "can_write", "can_filter"], "scripts": {}, "dependencies": {}, "devDependencies": {}}