{"toolbox_name": "Mathématiques", "math_number": {"message0": "%1"}, "math_arithmetic": {"message0": "%1 %2 %3", "args0": [null, {"options": [["Additionner", "ADD"], ["Soustraire", "MINUS"], ["Multiplier", "MULTIPLY"], ["Diviser", "DIVIDE"], ["<PERSON><PERSON><PERSON>", "MODULO"], ["Puissance", "POWER"]]}, null]}, "math_single": {"message0": "%1 %2", "args0": [{"options": [["<PERSON><PERSON>", "ROOT"], ["<PERSON><PERSON> absolue", "ABS"], ["Négatif", "NEG"], ["ln", "LN"], ["log10", "LOG10"], ["e^", "EXP"], ["10^", "POW10"]]}, null]}, "math_trig": {"message0": "%1 %2", "args0": [{"options": [["Sinus", "SIN"], ["<PERSON><PERSON><PERSON>", "COS"], ["Tangente", "TAN"], ["<PERSON><PERSON><PERSON>", "ASIN"], ["<PERSON><PERSON><PERSON>", "ACOS"], ["Arctangente", "ATAN"]]}, null]}, "math_constant": {"message0": "%1", "args0": [{"options": [["π", "PI"], ["e", "E"], ["Ratio <PERSON>", "GOLDEN_RATIO"], ["sqrt(2)", "SQRT2"], ["sqrt(1/2)", "SQRT1_2"], ["∞", "INFINITY"]]}]}, "math_number_property": {"message0": "%1 %2", "args0": [null, {"options": [["est pair", "EVEN"], ["est impair", "ODD"], ["est premier", "PRIME"], ["est entier", "WHOLE"], ["est positif", "POSITIVE"], ["est négatif", "NEGATIVE"], ["est divisible par", "DIVISIBLE_BY"]]}]}, "math_change": {"message0": "changer %1 de %2"}, "math_round": {"message0": "%1 %2", "args0": [{"options": [["<PERSON><PERSON><PERSON><PERSON>", "ROUND"], ["Arrondir à la hausse", "ROUNDUP"], ["Arrondir à la baisse", "ROUNDDOWN"]]}, null]}, "math_on_list": {"message0": "%1 %2", "args0": [{"options": [["Somme de la liste", "SUM"], ["<PERSON>e", "MIN"], ["<PERSON> liste", "MAX"], ["Moyenne de la liste", "AVERAGE"], ["<PERSON><PERSON><PERSON><PERSON>", "MEDIAN"], ["Mode de la liste", "MODE"], ["Écart type de la liste", "STD_DEV"], ["Élément aléatoire de la liste", "RANDOM"]]}, null]}, "math_modulo": {"message0": "reste de %1 ÷ %2"}, "math_constrain": {"message0": "contraindre %1 entre %2 et %3"}, "math_random_int": {"message0": "entier aléatoire de %1 à %2"}, "math_random_float": {"message0": "fraction aléatoire"}, "math_atan2": {"message0": "atan2 du point (x: %1, y: %2)"}, "math_round_to_decimal": {"message0": "arrondir %1 à %2 décimales"}, "math_bitwise_not": {"message0": "~ %1"}, "map_to": {"message0": "Mapper %1 de [%2,%3] à [%4,%5]"}, "constrain": {"message0": "Contraindre %1 entre (min) %2 et (max) %3"}}