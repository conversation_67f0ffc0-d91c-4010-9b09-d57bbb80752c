{"toolbox_name": "منطق", "controls_if": {"message0": "🔀إذا %1", "message1": "ثم قم بتنفيذ %1"}, "controls_ifelse": {"message0": "🔀إذا %1", "message1": "قم بتنفيذ %1", "message2": "وإلا %1"}, "logic_compare": {"message0": "%1 %2 %3", "args0": [null, {"options": [["==", "EQ"], ["! =", "NEQ"], ["<", "LT"], [">", "GT"], [">=", "GTE"], ["<=", "LTE"]]}, null]}, "logic_operation": {"message0": "%1 %2 %3", "args0": [null, {"options": [["و", "AND"], ["أو", "OR"]]}, null]}, "logic_negate": {"message0": "ليس %1"}, "logic_boolean": {"message0": "%1", "args0": [{"options": [["صح", "true"], ["خطأ", "false"]]}]}, "logic_ternary": {"message0": "تأكيد %1", "message1": "إذا كان صحيحًا %1", "message2": "إذا كان خاطئًا %1"}}