[{"type": "gd3800_init", "message0": "MP3模块初始化 RX%1 TX%2", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#ff0000", "args0": [{"type": "input_value", "name": "PIN_TX"}, {"type": "input_value", "name": "PIN_RX"}]}, {"type": "gd3800_set", "message0": "MP3模块 设置为%1", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#ff0000", "args0": [{"type": "field_dropdown", "name": "GD3800_SETSTATE", "options": [["播放", "play"], ["暂停", "pause"], ["下一曲", "next"], ["上一曲", "prev"], ["音量加", "volumeUp"], ["音量减", "volumeDn"]]}]}, {"type": "gd3800_play", "message0": "MP3模块 播放第%1首歌", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#ff0000", "args0": [{"type": "input_value", "name": "MUSICNUM"}]}, {"type": "gd3800_cyclemode", "message0": "MP3模块 设置为%1", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#ff0000", "args0": [{"type": "field_dropdown", "name": "GD3800_CYMODE", "options": [["全部循环", "MP3_LOOP_ALL"], ["文件内循环", "MP3_LOOP_FOLDER"], ["单曲循环", "MP3_LOOP_ONE"], ["随机播放", "MP3_LOOP_RAM"]]}]}, {"type": "gd3800_equalizer", "message0": "MP3模块 音效设置为%1", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#ff0000", "args0": [{"type": "field_dropdown", "name": "GD3800_EQMODE", "options": [["普通", "MP3_EQ_NORMAL"], ["流行", "MP3_EQ_POP"], ["摇杆", "MP3_EQ_ROCK"], ["爵士", "MP3_EQ_JAZZ"], ["古典", "MP3_EQ_CLASSIC"], ["低音", "MP3_EQ_BASS"]]}]}, {"type": "gd3800_setvolume", "message0": "MP3模块 音量设置为%1", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#ff0000", "args0": [{"type": "input_value", "name": "SETVOLUME"}]}]