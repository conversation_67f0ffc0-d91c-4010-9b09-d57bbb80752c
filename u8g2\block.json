[{"type": "u8g2_begin", "message0": "初始化显示屏 型号 %1 分辨率 %2 通信 %3", "args0": [{"type": "field_dropdown", "name": "TYPE", "options": [["SSD1306", "SSD1306"], ["SSD1309", "SSD1309"], ["SH1106", "SH1106"], ["SH1107", "SH1107"], ["ST7920", "ST7920"]]}, {"type": "field_dropdown", "name": "RESOLUTION", "options": [["128x64", "128X64_NONAME_F"], ["128x32", "128X32_NONAME_F"], ["128x128", "128X128_NONAME_F"]]}, {"type": "field_dropdown", "name": "PROTOCOL", "options": [["I2C", "_HW_I2C"], ["3W_SPI", "_3W_HW_SPI"], ["4W_SPI", "_4W_HW_SPI"], ["软件 I2C", "_SW_I2C"], ["软件 3W_SPI", "_3W_SW_SPI"], ["软件 4W_SPI", "_4W_SW_SPI"]]}], "extensions": ["u8g2_begin_dynamic_inputs"], "previousStatement": null, "nextStatement": null, "colour": "#4DB6AC", "tooltip": "初始化U8G2显示设备", "inputsInline": true}, {"type": "u8g2_clear", "message0": "显示屏清屏", "previousStatement": null, "nextStatement": null, "colour": "#4DB6AC", "tooltip": "清空显示缓冲区内容", "inputsInline": true}, {"type": "u8g2_draw_pixel", "message0": "绘制点 坐标 x%1 y%2", "args0": [{"type": "input_value", "name": "X", "check": "Number"}, {"type": "input_value", "name": "Y", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#4DB6AC", "tooltip": "绘制点", "inputsInline": true}, {"type": "u8g2_draw_line", "message0": "绘制线段 从 x%1 y%2 到 x%3 y%4", "args0": [{"type": "input_value", "name": "X1", "check": "Number"}, {"type": "input_value", "name": "Y1", "check": "Number"}, {"type": "input_value", "name": "X2", "check": "Number"}, {"type": "input_value", "name": "Y2", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#4DB6AC", "tooltip": "绘制直线", "inputsInline": true}, {"type": "u8g2_draw_rectangle", "message0": "绘制矩形 坐标 x%1 y%2 宽%3 高%4 填充%5", "args0": [{"type": "input_value", "name": "X", "check": "Number"}, {"type": "input_value", "name": "Y", "check": "Number"}, {"type": "input_value", "name": "WIDTH", "check": "Number"}, {"type": "input_value", "name": "HEIGHT", "check": "Number"}, {"type": "field_checkbox", "name": "FILL", "checked": false}], "previousStatement": null, "nextStatement": null, "colour": "#4DB6AC", "tooltip": "绘制矩形，可选择是否填充", "inputsInline": true}, {"type": "u8g2_draw_circle", "message0": "绘制圆形 圆心 x%1 y%2 半径%3 填充%4", "args0": [{"type": "input_value", "name": "X", "check": "Number"}, {"type": "input_value", "name": "Y", "check": "Number"}, {"type": "input_value", "name": "RADIUS", "check": "Number"}, {"type": "field_checkbox", "name": "FILL", "checked": false}], "previousStatement": null, "nextStatement": null, "colour": "#4DB6AC", "tooltip": "绘制圆形，可选择是否填充", "inputsInline": true}, {"type": "u8g2_draw_str", "message0": "显示文本 坐标 x%1 y%2 文本%3", "args0": [{"type": "input_value", "name": "X", "check": "Number"}, {"type": "input_value", "name": "Y", "check": "Number"}, {"type": "input_value", "name": "TEXT", "check": "String"}], "previousStatement": null, "nextStatement": null, "colour": "#4DB6AC", "tooltip": "简化版文本显示（自带清屏和刷新）", "inputsInline": true}, {"type": "u8g2_draw_bitmap", "message0": "显示图像 坐标 x%1 y%2 图像 %3", "args0": [{"type": "input_value", "name": "X", "check": "Number"}, {"type": "input_value", "name": "Y", "check": "Number"}, {"type": "input_value", "name": "BITMAP", "check": "Bitmap"}], "previousStatement": null, "nextStatement": null, "colour": "#4DB6AC", "tooltip": "显示图像", "inputsInline": true}, {"inputsInline": true, "message0": "图像 %1", "type": "u8g2_bitmap", "args0": [{"type": "field_bitmap_u8g2", "name": "CUSTOM_BITMAP", "width": 128, "height": 64}], "output": "Bitmap", "colour": "#4CAF66"}]