{"name": "@aily-project/lib-espui", "nickname": "ESPUI网页界面库", "author": "ericoding", "description": "基于ESPUI的ESP32/ESP8266网页界面库，支持创建按钮、滑条、开关等Web UI控件", "version": "1.0.0", "compatibility": {"core": ["esp32:esp32", "esp8266:esp8266"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "espui", "esp32", "esp8266", "web", "ui", "interface", "gui", "button", "slider", "switch", "label", "text", "number", "graph", "gauge", "control", "wifi", "http", "webserver", "async", "espui_begin", "espui_label", "espui_button", "espui_switcher", "espui_slider", "espui_text", "espui_number", "espui_graph", "espui_gauge", "espui_update_control", "espui_update_label", "espui_on_event"], "dependencies": {"ESPUI": "^2.2.4", "ESP Async WebServer": "^1.2.3", "ArduinoJson": "^6.21.3"}, "scripts": {}, "devDependencies": {}}