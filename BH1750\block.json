[{"type": "bh1750_init_with_wire", "message0": "初始化光照传感器 %1\n模式 %2\n地址 %3\n使用 %4", "args0": [{"type": "field_input", "name": "VAR", "text": "lightMeter"}, {"type": "field_dropdown", "name": "MODE", "options": [["连续高分辨率模式 (1lux, 120ms)", "CONTINUOUS_HIGH_RES_MODE"], ["连续高分辨率模式2 (0.5lux, 120ms)", "CONTINUOUS_HIGH_RES_MODE_2"], ["连续低分辨率模式 (4lux, 16ms)", "CONTINUOUS_LOW_RES_MODE"], ["单次高分辨率模式 (1lux, 120ms)", "ONE_TIME_HIGH_RES_MODE"], ["单次高分辨率模式2 (0.5lux, 120ms)", "ONE_TIME_HIGH_RES_MODE_2"], ["单次低分辨率模式 (4lux, 16ms)", "ONE_TIME_LOW_RES_MODE"], ["未配置模式 (UNCONFIGURED)", "UNCONFIGURED"]]}, {"type": "field_dropdown", "name": "ADDRESS", "options": [["0x23 (ADDR低电平)", "0x23"], ["0x5C (ADDR高电平)", "0x5C"]]}, {"type": "field_dropdown", "name": "WIRE", "options": "${board.i2c}"}], "previousStatement": null, "nextStatement": null, "inputsInline": false, "colour": 120, "tooltip": "初始化BH1750光照传感器，指定I2C地址和使用的I2C总线", "extensions": ["bh1750_init_with_wire_pin_info"]}, {"type": "bh1750_read_light_level", "message0": "读取光照传感器 %1 的光照值", "args0": [{"type": "field_variable", "name": "VAR", "variable": "lightMeter", "variableTypes": ["BH1750"], "defaultType": "BH1750"}], "output": "Number", "colour": 120, "tooltip": "读取BH1750传感器的光照强度(lux)"}]