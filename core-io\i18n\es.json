{"toolbox_name": "I/O Pines", "io_pinmode": {"message0": "Establecer el modo del pin %1 a %2"}, "io_digitalwrite": {"message0": "Salir señal digital %2 en el pin %1"}, "io_digitalread": {"message0": "Leer señal digital del pin %1"}, "io_analogwrite": {"message0": "Salir señal PWM %2 en el pin %1"}, "io_analogread": {"message0": "Leer señal analógica del pin %1"}, "io_pin_digi": {"message0": "Pin digital %1"}, "io_pin_adc": {"message0": "Pin analógico %1"}, "io_pin_pwm": {"message0": "Pin PWM %1"}, "io_mode": {"message0": "Modo de pin %1"}, "io_state": {"message0": "Nivel %1"}}