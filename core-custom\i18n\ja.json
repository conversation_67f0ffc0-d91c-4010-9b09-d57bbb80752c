{"toolbox_name": "カスタムコード", "custom_code": {"message0": "カスタムコード %1"}, "custom_macro": {"message0": "マクロ定義 %1 を %2 とする"}, "custom_library": {"message0": "ライブラリ %1 をインクルード"}, "custom_variable": {"message0": "変数定義 型 %1 名前 %2 初期値 %3", "args0": [{"options": [["整数型", "int"], ["長整数型", "long*"], ["浮動小数点型", "float"], ["倍精度浮動小数点型", "double"], ["符号なし整数型", "unsigned char"], ["符号なし長整数型", "unsigned char"], ["ブール型", "bool"], ["文字型", "char"], ["文字列型", "string"]]}, null, null]}, "custom_function": {"message0": "関数定義 %1 %2 戻り値型 %3 引数リスト %4 %5 関数本体 %6", "args0": [null, null, {"options": [["整数型", "int"], ["長整数型", "long*"], ["浮動小数点型", "float"], ["倍精度浮動小数点型", "double"], ["符号なし整数型", "unsigned char"], ["符号なし長整数型", "unsigned char"], ["ブール型", "bool"], ["文字型", "char"], ["文字列型", "string"]]}, null, null, null]}}