{"toolbox_name": "IICMotorDriver", "iicmd_init": {"message0": "Inicializar driver do motor"}, "iicmd_dirinit": {"message0": "Inicializar direção de referência do motor Motor 1%1 Motor 2%2 Motor 3%3 Motor 4%4", "args0": [{"options": [["DIR1", "DIRP"], ["DIR2", "DIRN"]]}, {"options": [["DIR1", "DIRP"], ["DIR2", "DIRN"]]}, {"options": [["DIR1", "DIRP"], ["DIR2", "DIRN"]]}, {"options": [["DIR1", "DIRP"], ["DIR2", "DIRN"]]}]}, "iicmd_stop": {"message0": "Parar %1", "args0": [{"options": [["Motor 1", "M1"], ["Motor 2", "M2"], ["Motor 3", "M3"], ["Motor 4", "M4"], ["Todos os motores", "MALL"]]}]}, "iicmd_runone": {"message0": "Definir velocidade do motor %1 para %2", "args0": [{"options": [["Motor 1", "M1"], ["Motor 2", "M2"], ["Motor 3", "M3"], ["Motor 4", "M4"]]}, null]}, "iicmd_runall": {"message0": "Definir velocidade de todos os motores %1", "args0": [null]}, "iicmd_runall2": {"message0": "Definir velocidade de todos os motores Motor 1%1 Motor 2%2 Motor 3%3 Motor 4%4", "args0": [null, null, null, null]}, "iicmd_digitout": {"message0": "Porta de controle %1 saída %2", "args0": [{"options": [["S1", "S1"], ["S2", "S2"], ["S3", "S3"], ["S4", "S4"]]}, {"options": [["ALTO", "HIGH"], ["BAIXO", "LOW"]]}]}, "iicmd_servo": {"message0": "Porta do servo %1 girar para (0-180) %2°", "args0": [{"options": [["S1", "S1"], ["S2", "S2"], ["S3", "S3"], ["S4", "S4"]]}, null]}}