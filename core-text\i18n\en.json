{"toolbox_name": "Text", "string_add_string": {"message0": "String(%1) + String(%2)"}, "string_charAt": {"message0": "%1 's character at %2"}, "string_length": {"message0": "Character count of %1"}, "string_indexOf": {"message0": "Does %1 contain %2?"}, "string_substring": {"message0": "%1 get characters from %2 %3 to %4 %5", "args0": [null, {"options": [["the", "0"], ["the last", "1"]]}, null, {"options": [["the", "0"], ["the last", "1"]]}, null]}, "string_find_str": {"message0": "Find %1 in %2 at %3 position", "args0": [null, null, {"options": [["First", "indexOf"], ["Last", "lastIndexOf"]]}]}, "string_to": {"message0": "Convert string %1 to %2", "args0": [null, {"options": [["integer", "toInt"], ["float", "toFloat"]]}]}, "number_to": {"message0": "Convert number %1 to ASCII string"}, "toascii": {"message0": "Convert character %1 to ASCII value"}, "number_to_string": {"message0": "Convert number %1 to string"}, "text": {"message0": "%1"}, "text_join": {"message0": ""}, "text_create_join_container": {"message0": "%{BKY_TEXT_CREATE_JOIN_TITLE_JOIN} %1 %2"}, "text_create_join_item": {"message0": "%{BKY_TEXT_CREATE_JOIN_ITEM_TITLE_ITEM}"}, "text_append": {"message0": "Append text %2 after %1"}, "text_length": {"message0": "Length of %1"}, "text_isEmpty": {"message0": "%1 is empty"}, "text_indexOf": {"message0": "In text %1 %2 %3", "args0": [null, {"options": [["find first occurrence of text", "FIRST"], ["find last occurrence of text", "LAST"]]}, null]}, "text_charAt": {"message0": "In text %1 %2", "args0": [null, {"options": [["Get character #", "FROM_START"], ["Get character from end #", "FROM_END"], ["Get first character", "FIRST"], ["Get last character", "LAST"], ["Get random character", "RANDOM"]]}]}, "tt_getSubstring": {"message0": "get substring in text %1", "message1": "%1 %2", "args1": [{"options": [["from # character", "FROM_START"], ["from # from end", "FROM_END"], ["from first character", "FIRST"]]}, null], "message2": "%1 %2", "args2": [{"options": [["to # character", "FROM_START"], ["to # from end", "FROM_END"], ["to last character", "LAST"]]}, null]}, "text_changeCase": {"message0": "%1 %2", "args0": [{"options": [["change to uppercase", "UPPERCASE"], ["change to lowercase", "LOWERCASE"], ["change to title case", "TITLECASE"]]}, null]}, "text_trim": {"message0": "%1 trim %2", "args0": [{"options": [["trim spaces from both ends", "BOTH"], ["trim spaces from left", "LEFT"], ["trim spaces from right", "RIGHT"]]}, null]}, "text_count": {"message0": "Count occurrence of %1 in %2"}, "text_replace": {"message0": "Replace %2 with %3 in %1"}, "text_reverse": {"message0": "Reverse text %1"}}