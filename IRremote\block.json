[{"inputsInline": true, "message0": "红外接收 快速测试  引脚 %1", "type": "QuickTesting", "args0": [{"type": "field_dropdown", "name": "IRPIN", "options": "${board.digitalPins}"}], "previousStatement": null, "nextStatement": null, "colour": "#ff5722"}, {"inputsInline": true, "message0": "红外接收初始化 引脚 %1", "type": "irrecv_begin_in", "args0": [{"type": "field_dropdown", "name": "IRPININ", "options": "${board.digitalPins}"}], "previousStatement": null, "nextStatement": null, "colour": "#ff5722"}, {"inputsInline": true, "message0": "红外发射初始化 引脚 %1", "type": "irrecv_begin_out", "args0": [{"type": "field_dropdown", "name": "IRPINOUT", "options": "${board.digitalPins}"}], "previousStatement": null, "nextStatement": null, "colour": "#ff5722"}, {"type": "irrecv_datain", "message0": "接收到红外数据%1", "args0": [{"type": "input_statement", "name": "IRDATA"}], "previousStatement": null, "nextStatement": null, "colour": "#ff5722"}, {"inputsInline": true, "message0": "接收到的红外数据", "type": "irrecv_irdata", "output": "Number", "colour": "#ff5722"}, {"inputsInline": true, "message0": "红外发射 地址0x%1 数据0x%2", "type": "irrecv_irsend", "args0": [{"type": "input_value", "name": "IRADDRESS"}, {"type": "input_value", "name": "IROUTDATA"}], "previousStatement": null, "nextStatement": null, "colour": "#ff5722"}]