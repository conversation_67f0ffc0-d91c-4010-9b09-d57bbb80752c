{"toolbox_name": "Pinos de I/O", "io_pinmode": {"message0": "Definir modo do pino %1 para %2"}, "io_digitalwrite": {"message0": "Sinal digital de saída %2 no pino %1"}, "io_digitalread": {"message0": "Ler sinal digital do pino %1"}, "io_analogwrite": {"message0": "Sinal PWM de saída %2 no pino %1"}, "io_analogread": {"message0": "Ler sinal analógico do pino %1"}, "io_pin_digi": {"message0": "Pino digital %1"}, "io_pin_adc": {"message0": "Pino analógico %1"}, "io_pin_pwm": {"message0": "Pino PWM %1"}, "io_mode": {"message0": "Modo de pino %1"}, "io_state": {"message0": "Nível %1"}}