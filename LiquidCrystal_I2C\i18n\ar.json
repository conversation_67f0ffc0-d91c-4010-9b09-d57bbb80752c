{"toolbox_name": "LCD1602 I2C", "lcd_i2c_init": {"message0": "تهيئة شاشة LCD I2C العنوان %1 عدد الأعمدة %2 عدد الصفوف %3", "args0": [[["0x27", "0x27"], ["0x26", "0x26"], ["0x25", "0x25"], ["0x24", "0x24"], ["0x23", "0x23"], ["0x22", "0x22"], ["0x21", "0x21"], ["0x20", "0x20"]], null, null]}, "lcd_i2c_clear": {"message0": "مس<PERSON> LCD"}, "lcd_i2c_set_cursor": {"message0": "تعيين مؤشر LCD إلى العمود %1 الصف %2"}, "lcd_i2c_print": {"message0": "إخراج إلى LCD %1"}, "lcd_i2c_print_position": {"message0": "إخراج إلى LCD في العمود %1 الصف %2 النص %3"}, "lcd_i2c_backlight_on": {"message0": "تشغيل إضاءة خلفية LCD"}, "lcd_i2c_backlight_off": {"message0": "إيقاف إضاءة خلفية LCD"}, "lcd_i2c_custom_char": {"message0": "حرف مخصص %1 رقم %2", "args0": [null, ["0", "0"], ["1", "1"], ["2", "2"], ["3", "3"], ["4", "4"], ["5", "5"], ["6", "6"], ["7", "7"]]}}