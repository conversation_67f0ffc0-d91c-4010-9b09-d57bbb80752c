{"toolbox_name": "Logic", "controls_if": {"message0": "🔀If %1", "message1": "Then do %1"}, "controls_ifelse": {"message0": "🔀If %1", "message1": "Do %1", "message2": "Else %1"}, "logic_compare": {"message0": "%1 %2 %3", "args0": [null, {"options": [["==", "EQ"], ["!=", "NEQ"], ["<", "LT"], [">", "GT"], [">=", "GTE"], ["<=", "LTE"]]}, null]}, "logic_operation": {"message0": "%1 %2 %3", "args0": [null, {"options": [["AND", "AND"], ["OR", "OR"]]}, null]}, "logic_negate": {"message0": "NOT %1"}, "logic_boolean": {"message0": "%1", "args0": [{"options": [["true", "true"], ["false", "false"]]}]}, "logic_ternary": {"message0": "Assert %1", "message1": "If true %1", "message2": "If false %1"}}