[{"inputsInline": true, "message0": "初始化LCD I2C显示器 地址%1 列数%2 行数%3", "type": "lcd_i2c_init", "args0": [{"type": "field_dropdown", "name": "ADDRESS", "options": [["0x27", "0x27"], ["0x26", "0x26"], ["0x25", "0x25"], ["0x24", "0x24"], ["0x23", "0x23"], ["0x22", "0x22"], ["0x21", "0x21"], ["0x20", "0x20"]]}, {"type": "field_number", "name": "COLS", "value": 16}, {"type": "field_number", "name": "ROWS", "value": 2}], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"inputsInline": true, "message0": "清空LCD显示", "type": "lcd_i2c_clear", "args0": [], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"inputsInline": true, "message0": "设置LCD光标到列%1 行%2", "type": "lcd_i2c_set_cursor", "args0": [{"type": "input_value", "name": "COL", "value": 0}, {"type": "input_value", "name": "ROW", "value": 0}], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"inputsInline": true, "message0": "LCD输出 %1", "type": "lcd_i2c_print", "args0": [{"type": "input_value", "name": "TEXT", "value": "\"\""}], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"inputsInline": true, "message0": "LCD在列%1 行%2 输出%3", "type": "lcd_i2c_print_position", "args0": [{"type": "input_value", "name": "COL", "value": 0}, {"type": "input_value", "name": "ROW", "value": 0}, {"type": "input_value", "name": "TEXT", "value": "\"\""}], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"inputsInline": true, "message0": "打开LCD背光", "type": "lcd_i2c_backlight_on", "args0": [], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"inputsInline": true, "message0": "关闭LCD背光", "type": "lcd_i2c_backlight_off", "args0": [], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"inputsInline": true, "message0": "自定义字符 %1 编号 %2", "type": "lcd_i2c_custom_char", "args0": [{"type": "field_bitmap", "name": "CUSTOM_CHAR", "width": 5, "height": 8}, {"type": "field_dropdown", "name": "CHAR_INDEX", "options": [["0", "0"], ["1", "1"], ["2", "2"], ["3", "3"], ["4", "4"], ["5", "5"], ["6", "6"], ["7", "7"]]}], "output": "Number", "colour": "#4CAF66"}]