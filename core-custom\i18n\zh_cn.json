{"toolbox_name": "自定义代码", "custom_code": {"message0": "自定义代码 %1"}, "custom_macro": {"message0": "宏定义 %1 为 %2"}, "custom_library": {"message0": "引用库 %1"}, "custom_variable": {"message0": "定义变量 类型 %1 名称 %2 初始值 %3", "args0": [{"options": [["整型", "int"], ["长整型", "long*"], ["浮点型", "float"], ["双精度浮点型", "double"], ["无符号整型", "unsigned char"], ["无符号长整型", "unsigned char"], ["布尔型", "bool"], ["字符型", "char"], ["字符串型", "string"]]}, null, null]}, "custom_function": {"message0": "函数定义 %1 %2 返回类型 %3 参数列表 %4 %5 函数体 %6", "args0": [null, null, {"options": [["整型", "int"], ["长整型", "long*"], ["浮点型", "float"], ["双精度浮点型", "double"], ["无符号整型", "unsigned char"], ["无符号长整型", "unsigned char"], ["布尔型", "bool"], ["字符型", "char"], ["字符串型", "string"]]}, null, null, null]}}