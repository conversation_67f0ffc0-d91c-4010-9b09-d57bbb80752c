# aily blockly库开发 

## 库结构
aily blockly库基本遵循google blockly库结构(当前使用版本为blockly 11)，使用npm包管理形式管理库的版本及相关必要信息。一个aily blockly库的结构如下：
```
library-name  
 |- block.json             // aily blockly block文件
 |- generator.js           // aily blockly generator文件
 |- toolbox.json           // aily blockly toolbox文件
 |- package.json           // npm包管理文件
 |- src.7z                 // Arduino库源文件，请使用7z极限压缩后放在库中
 |- readme.md              // 说明文件，如果使用了开源库，请进行说明
```

## block.json
aily blockly json在原始的blockly json上进行了扩展。这里以servo lib（舵机库）为例进行说明，舵机库json如下：  
```json
[
    {
        "inputsInline": true,
        "message0": "舵机%1移动到%2",
        "type": "servo_write",
        "args0": [
            {
                "type": "field_variable",
                "name": "OBJECT",
                "variable": "servo1",
                "variableTypes": [
                    "Servo"
                ],
                "defaultType": "Servo"
            },
            {
                "type": "field_number",
                "name": "ANGLE",
                "value": 0
            }
        ],
        "previousStatement": null,
        "nextStatement": null,
        "colour": "#03a9f4"
    },
    {
        "inputsInline": true,
        "message0": "舵机%1连接到引脚%2",
        "type": "servo_attach",
        "args0": [
            {
                "type": "field_variable",
                "name": "OBJECT",
                "variable": "servo1",
                "variableTypes": [
                    "Servo"
                ],
                "defaultType": "Servo"
            },
            {
                "type": "field_dropdown",
                "name": "PIN1",
                "options": "${board.digitalPins}"
            }
        ],
        "previousStatement": null,
        "nextStatement": null,
        "colour": "#03a9f4"
    }
]
```

### block配置  
json文件中`blocks数组`即是具体的block配置，可以参照[blockly官方文档](https://developers.google.com/blockly/guides/create-custom-blocks/define-blocks)了解相关配置。  
单一block示例如下：  

```json
{
    "inputsInline": true,              // block样式，true输入项目为一行显示，flase为多行显示
    "message0": "舵机%1连接到引脚%2",   // block信息，定义该block上显示的内容
    "type": "servo_attach",           // block类型，该参数必须是唯一的
    "previousStatement": null,         // 前置连接，配置该block之前的block类型  
    "nextStatement": null,             // 后置连接，配置该block之后的block类型  
    "colour": "#03a9f4",               // block颜色
    "args0": [                         // 输入项
        {
            "type": "field_variable",
            "name": "OBJECT",
            "variable": "servo1",
            "variableTypes": [
                "Servo"
            ],
            "defaultType": "Servo"
        },
        {
            "type": "field_dropdown",
            "name": "PIN1",
            "options": "${board.digitalPins}"
        }
    ]
}
```



## generator.js 
generator.js负责将block转换成代码，每一个block都有一个对应的generator函数。对于Arduino库，generator函数编写形式可参考：
```javascript
Arduino.forBlock['servo_attach'] = function(block, generator) {
  // 实现代码参考blockly官方文档
  return ''
};

Arduino.forBlock['servo_write'] = function(block, generator) {
  // 实现代码参考blockly官方文档
  return ''
};

```

### 添加附加代码
在generator.js中block对应的代码使用retuen返回，对于大多数Arduino出了block对应代码外，还需要添加额外的附加代码。如调用servo库需要添加库引用，并创建一个Servo对象：
```c++
#include <Servo.h>
Servo myservo;
```
此时可在generator.js对应的函数中添加如下语句用以在程序其他位置添加库引用和新建对象语句：
```javascript
Arduino.forBlock['servo_attach'] = function(block, generator) {
  generator.addLibrary('#include <Servo.h>','#include <Servo.h>');
  generator.addVariable('Servo myservo','Servo myservo');
  // 实现代码参考blockly官方文档
  return ''
};
```

可用的函数有：
```javascript
addMacro(tag, code);
addLibrary(tag, code);
addVariable(tag, code);
addObject(tag, code);
addFunction(tag, code);
addSetupBegin(tag, code);
addSetup(tag, code); //系统默认，不建议库使用
addSetupEnd(tag, code);
addLoopBegin(tag, code);
addLoop(tag, code); //系统默认，不建议库使用
addLoopEnd(tag, code);
```
其中tag为避免重复添加代码的标签，code是实际插入的代码。
使用这些函数，可以将对应的代码分别加入到程序的以下位置：

```c++
// [宏定义 macro] 使用generator.addMacro可向该部分添加代码
#defined PIN 3
// [库引用 library] 使用generator.addLibrary可向该部分添加代码
#include <Servo.h>
// [变量 variable] 使用generator.addVariable可向其中添加代码
static const int servoPin = 4;1
// [对象 object] 使用generator.addObject可向该部分添加代码
Servo servo1;
// [函数 function] 使用generator.addFunction可向该部分添加代码

void setup() {
// [初始化 setup Being] 使用generator.addSetupBegin可向该部分添加代码
    servo1.attach(servoPin);
    
// [初始化 setup] 使用generator.addSetup可向该部分添加代码(系统默认，不建议库使用)
    Serial.begin(115200);

// [初始化 setup End] 使用generator.addSetupEnd可向该部分添加代码

}

void loop() {
// [主程序 loop Begin] 使用generator.addLoopBegin可向该部分添加代码

// [主程序 loop] 使用generator.addLoop可向该部分添加代码(系统默认，不建议库使用)
    for(int posDegrees = 0; posDegrees <= 180; posDegrees++) {
        servo1.write(posDegrees);
        Serial.println(posDegrees);
        delay(20);
    }

    for(int posDegrees = 180; posDegrees >= 0; posDegrees--) {
        servo1.write(posDegrees);
        Serial.println(posDegrees);
        delay(20);
    }

// [主程序 loop End] 使用generator.addLoopEnd可向该部分添加代码

}
```

### 注意事项
如果在generator.js需要创建函数，请不要创建全局函数，使用如下方式将函数定义到Arduino中，如：
```js
Arduino.newFunction = function(value) {
  return 'ok';
};
```

## toolbox.json 
Toolbox是呈现在blockly界面左侧的block菜单，示例如下：
```json
{
    "kind": "category",
    "name": "Servo",
    "contents": [
        {
            "kind": "block",
            "type": "servo_attach"
        },
        {
            "kind": "block",
            "type": "servo_write"
        }
    ]
}
```

## package.json
版本控制文件，采用npm包管理,示例如下：
```json
{
    "name": "@aily-project/lib-servo",  // 库的包名以 @aily-project/lib- 开头
    "nickname":"舵机驱动库",             // 用于在库管理器中显示的名称
    "author": "aily Project",
    "description": "舵机控制支持库，支持Arduino UNO、MEGA、ESP8266、ESP32等开发板", // 简短的介绍，不超过50字
    "version": "0.0.1",
    "compatibility": {                  // 兼容性说明，支持的核心和电压
        "core": [                       // 如果为[]，则为通用库，兼容所有开发板
            "arduino:avr",
            "esp32:esp32",
            "esp32:esp32c3",
            "esp32:esp32s3",
            "renesas_uno:minima",
            "renesas_uno:unor4wifi"
        ],
        "voltage": [3.3,5]
    },
    "keywords": [                       // keywords可以辅助搜索库，建议其中添加分类名、函数名等
        "aily",
        "blockly",
        "servo",
        "servo_attach",
        "servo_write",
    ],
    "scripts": {},
    "dependencies": {},                // 该库依赖了其他库
    "devDependencies": {}
}
```

### 包名命名规范
库以@aily-project/lib-xxxx形式命名，如@aily-project/lib-servo。
如果库是大部分开发板通用的库，我将其称为核心库，以@aily-project/lib-core-xxxx形式命名，如@aily-project/lib-core-io。

## 优化/简化方案
在没有歧义的前提下，尽可能简化库的调用，让用户通过尽量少操作，就能正常使用库，如：
0. toolbox中如果有输入变量，可以用使用影子block填充；还可以提供一些常用block搭配组合，组合成程序段，让用户直接使用。
1. 一些引脚功能需要初始化，如调用`digitalWrite(5,HIGH)`时，对应的初始化则为`pinMode(5,OUTPUT)`。推荐block库在调用digitalWrite、digitalRead相关块时，可以自动向程序setup部分添加对应的pinMode代码。
2. 一些库需要初始化，如servo库，需要使用`myservo.attach(9)`，指定舵机连接的引脚，然后再使用`myservo.write(val)`控制舵机转动角度。这个库建议在保留原本的attach、write块的同时，再提供一个简化的block，允许用户直接指定某引脚上的舵机转动到多少角度。
3. 对于IIC设备，如果IIC地址只有一个，则block中不显示其IIC地址；如果IIC地址有多个，则应该使用field_dropdown，提供菜单让用户选择地址，且默认地址应该放到菜单第一位。
4. 当Arduino库函数的参数为回调函数时，如onebutton库中`button.attachDoubleClick(doubleClick)`,doubleClick为回调函数，则应该创建一个主体为doubleClick函数block，并在generator.js中向程序的setup部分中添加代码`button.attachDoubleClick(doubleClick)`，向程序loop部分添加`button.tick()`。

## 扩展编写  
允许block使用扩展(extension),使用方法遵循blockly定义：
```json
    {
        "type": "blinker_init_wifi",
        "message0": "初始化Blinker WiFi模式 %1",
        "args0": [
            {
                "type": "field_dropdown",
                "name": "MODE",
                "options": [
                    [
                        "手动配网",
                        "手动配网"
                    ],
                    [
                        "EspTouch V2",
                        "EspTouchV2"
                    ]
                ]
            }
        ],
        "extensions": ["blinker_init_wifi_extension"],
        "previousStatement": null,
        "nextStatement": null,
        "colour": "#03A9F4",
        "inputsInline": false
    },
```
可以直接在generator.js中定义扩展。注册前请添加检查语句，避免扩展重复加载，如：  
```js
// 避免重复加载
if (Blockly.Extensions.isRegistered('blinker_init_wifi_extension')) {
  Blockly.Extensions.unregister('blinker_init_wifi_extension');
}

Blockly.Extensions.register('blinker_init_wifi_extension', function() {
// 实现代码
})
```

## 在库中获取当前开发板配置  
### block.json中获取
block.json文件中，可以通过${board.name}获取到当前加载的`board.json`配置。
如serial_begin block中：
```json
{
"inputsInline": true,
"message0": "初始化串口%1 设置波特率为%2",
"type": "serial_begin",
"colour": "#48c2c4",
"args0": [
    {
    "type": "field_dropdown",
    "name": "SERIAL",
    "options": "${board.serialPort}"
    },
    {
    "type": "field_dropdown",
    "name": "SPEED",
    "options": "${board.serialSpeed}"
    }
],
"previousStatement": null,
"nextStatement": null
}
```
通过`${board.serialPort}`、`${board.serialSpeed}`获取到了开发板可用的串口、及波特率配置。库在加载后，会自动替换成：
```json
{
    "inputsInline": true,
    "message0": "初始化串口%1 设置波特率为%2",
    "type": "serial_begin",
    "colour": "#48c2c4",
    "args0": [
        {
        "type": "field_dropdown",
        "name": "SERIAL",
        "options": [
            ["Serial","Serial"]
        ]
        },
        {
        "type": "field_dropdown",
        "name": "SPEED",
        "options": [
            ["1200","1200"],["9600","9600"],["14400","14400"],["19200","19200"],["38400","38400"],["57600","57600"],["115200","115200"]
        ]
        }
    ],
    "previousStatement": null,
    "nextStatement": null
}
```

### generator.js中获取
generator.js文件中，可以通过window['boardConfig']获取到当前加载的`board.json`配置。
可以通过以下方法，判断开发板配置，然后加载不同的内容
```js
// 判断开发板核心
if (window['boardConfig'].core.indexOf('esp32') > -1) {
}
// 判断开发板名称
if (window['boardConfig'].name.indexOf('ai-vox') > -1) {
}
```





