{"toolbox_name": "I/O-Pins", "io_pinmode": {"message0": "Setze Pin %1 Modus auf %2"}, "io_digitalwrite": {"message0": "Gebe digitales Signal %2 auf Pin %1 aus"}, "io_digitalread": {"message0": "Lese digitales Signal von Pin %1"}, "io_analogwrite": {"message0": "Gebe PWM-Signal %2 auf Pin %1 aus"}, "io_analogread": {"message0": "Lese analoges Signal von Pin %1"}, "io_pin_digi": {"message0": "Digitaler Pin %1"}, "io_pin_adc": {"message0": "Analoger <PERSON> %1"}, "io_pin_pwm": {"message0": "PWM-Pin %1"}, "io_mode": {"message0": "Pin-Modus %1"}, "io_state": {"message0": "%1 Pegel"}}