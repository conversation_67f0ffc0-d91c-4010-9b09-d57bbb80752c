[{"type": "freertos_task_create", "message0": "创建任务 函数名 %1 栈大小 %2 优先级 %3", "args0": [{"type": "field_input", "name": "TASK_NAME", "text": "TaskBlink"}, {"type": "field_number", "name": "STACK_SIZE", "value": 128, "min": 64, "max": 2048}, {"type": "field_number", "name": "PRIORITY", "value": 1, "min": 0, "max": 3}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 120, "tooltip": "创建一个FreeRTOS任务"}, {"type": "freertos_task_function", "message0": "任务函数 %1 %2 %3", "args0": [{"type": "field_input", "name": "TASK_NAME", "text": "TaskBlink"}, {"type": "input_dummy"}, {"type": "input_statement", "name": "TASK_CODE"}], "inputsInline": false, "colour": 120, "tooltip": "定义任务函数的代码"}, {"type": "freertos_task_delay", "message0": "任务延时 %1 毫秒", "args0": [{"type": "field_number", "name": "DELAY_MS", "value": 1000, "min": 1}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 120, "tooltip": "FreeRTOS任务延时"}, {"type": "freertos_task_suspend", "message0": "挂起任务 %1", "args0": [{"type": "field_input", "name": "TASK_HANDLE", "text": "TaskBlink_Handler"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 120, "tooltip": "挂起指定的任务"}, {"type": "freertos_task_resume", "message0": "恢复任务 %1", "args0": [{"type": "field_input", "name": "TASK_HANDLE", "text": "TaskBlink_Handler"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 120, "tooltip": "恢复指定的任务"}, {"type": "freertos_task_delete", "message0": "删除任务 %1", "args0": [{"type": "field_dropdown", "name": "TASK_HANDLE", "options": [["当前任务", "NULL"], ["指定任务", "HANDLE"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 120, "tooltip": "删除任务"}, {"type": "freertos_queue_create", "message0": "创建队列 %1 长度 %2 数据类型 %3", "args0": [{"type": "field_input", "name": "QUEUE_NAME", "text": "myQueue"}, {"type": "field_number", "name": "QUEUE_LENGTH", "value": 10, "min": 1}, {"type": "field_dropdown", "name": "DATA_TYPE", "options": [["整数", "int"], ["字符串", "String"], ["结构体", "struct"], ["数组", "array"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 180, "tooltip": "创建FreeRTOS队列"}, {"type": "freertos_queue_send", "message0": "向队列 %1 发送数据 %2", "args0": [{"type": "field_input", "name": "QUEUE_NAME", "text": "myQueue"}, {"type": "input_value", "name": "DATA"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 180, "tooltip": "向队列发送数据"}, {"type": "freertos_queue_receive", "message0": "从队列 %1 接收数据到 %2", "args0": [{"type": "field_input", "name": "QUEUE_NAME", "text": "myQueue"}, {"type": "field_input", "name": "VARIABLE", "text": "receivedData"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 180, "tooltip": "从队列接收数据"}, {"type": "freertos_semaphore_create", "message0": "创建 %1 信号量 %2", "args0": [{"type": "field_dropdown", "name": "SEMAPHORE_TYPE", "options": [["二进制", "Binary"], ["互斥锁", "Mutex"], ["计数", "Counting"]]}, {"type": "field_input", "name": "SEMAPHORE_NAME", "text": "mySemaphore"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 240, "tooltip": "创建信号量"}, {"type": "freertos_semaphore_take", "message0": "获取信号量 %1 等待时间 %2 毫秒", "args0": [{"type": "field_input", "name": "SEMAPHORE_NAME", "text": "mySemaphore"}, {"type": "field_number", "name": "WAIT_TIME", "value": 1000, "min": 0}], "inputsInline": true, "output": "Boolean", "colour": 240, "tooltip": "尝试获取信号量，返回是否成功"}, {"type": "freertos_semaphore_give", "message0": "释放信号量 %1", "args0": [{"type": "field_input", "name": "SEMAPHORE_NAME", "text": "mySemaphore"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 240, "tooltip": "释放信号量"}, {"type": "freertos_interrupt_handler", "message0": "中断处理函数 引脚 %1 触发模式 %2 %3 %4", "args0": [{"type": "field_number", "name": "PIN", "value": 2, "min": 0, "max": 21}, {"type": "field_dropdown", "name": "MODE", "options": [["低电平", "LOW"], ["上升沿", "RISING"], ["下降沿", "FALLING"], ["变化", "CHANGE"]]}, {"type": "input_dummy"}, {"type": "input_statement", "name": "INTERRUPT_CODE"}], "inputsInline": false, "colour": 300, "tooltip": "设置中断处理函数"}, {"type": "freertos_task_notification_send", "message0": "向任务 %1 发送通知", "args0": [{"type": "field_input", "name": "TASK_HANDLE", "text": "TaskHandle"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 300, "tooltip": "向任务发送通知"}, {"type": "freertos_task_notification_wait", "message0": "等待任务通知", "inputsInline": true, "output": "Boolean", "colour": 300, "tooltip": "等待任务通知"}, {"type": "freertos_get_tick_count", "message0": "获取系统滴答计数", "inputsInline": true, "output": "Number", "colour": 60, "tooltip": "获取系统滴答计数"}, {"type": "freertos_get_task_name", "message0": "获取任务名称 %1", "args0": [{"type": "field_dropdown", "name": "TASK_HANDLE", "options": [["当前任务", "NULL"], ["指定任务", "HANDLE"]]}], "inputsInline": true, "output": "String", "colour": 60, "tooltip": "获取任务名称"}, {"type": "freertos_get_free_heap_size", "message0": "获取空闲堆内存大小", "inputsInline": true, "output": "Number", "colour": 60, "tooltip": "获取空闲堆内存大小"}]