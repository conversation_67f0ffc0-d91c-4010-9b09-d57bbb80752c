{"toolbox_name": "حَلْقة", "arduino_setup": {"message0": "▶️تكوين %1"}, "arduino_loop": {"message0": "🔁حَلْقة %1"}, "controls_repeat_ext": {"message0": "كرر %1 مرة", "message1": "افعل %1"}, "controls_repeat": {"message0": "كرر %1 مرة", "message1": "افعل %1"}, "controls_whileUntil": {"message0": "%1 %2", "args0": [{"options": [["كرر طالما أن الشرط مستوفى", "WHILE"], ["كرر حتى يتم استيفاء الشرط", "UNTIL"]]}], "message1": "افعل %1"}, "controls_for": {"message0": "تعيين المتغير %1 من %2 إلى %3، زيادة بمقدار %4", "message1": "قم بتشغيل %1"}, "controls_flow_statements": {"message0": "%1", "args0": [{"options": [["إنهاء الحلقة", "BREAK"], ["استمر إلى التكرار التالي", "CONTINUE"]]}]}, "controls_whileForever": {"message0": "🔁 تكرار دائم %1"}}