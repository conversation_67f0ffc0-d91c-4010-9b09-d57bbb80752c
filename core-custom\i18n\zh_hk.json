{"toolbox_name": "自訂代碼", "custom_code": {"message0": "自訂代碼 %1"}, "custom_macro": {"message0": "宏定義 %1 為 %2"}, "custom_library": {"message0": "引用庫 %1"}, "custom_variable": {"message0": "定義變量 類型 %1 名稱 %2 初始值 %3", "args0": [{"options": [["整型", "int"], ["長整型", "long*"], ["浮點型", "float"], ["雙精度浮點型", "double"], ["無符號整型", "unsigned char"], ["無符號長整型", "unsigned char"], ["布林型", "bool"], ["字符型", "char"], ["字串型", "string"]]}, null, null]}, "custom_function": {"message0": "函數定義 %1 %2 返回類型 %3 參數列表 %4 %5 函數體 %6", "args0": [null, null, {"options": [["整型", "int"], ["長整型", "long*"], ["浮點型", "float"], ["雙精度浮點型", "double"], ["無符號整型", "unsigned char"], ["無符號長整型", "unsigned char"], ["布林型", "bool"], ["字符型", "char"], ["字串型", "string"]]}, null, null, null]}}