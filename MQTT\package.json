{"name": "@aily-project/lib-mqtt", "nickname": "MQTT通信", "author": "aily Project", "description": "基于PubSubClient的MQTT支持库，适用于Arduino UNO R4 WiFi、ESP32等开发板", "version": "0.0.1", "compatibility": {"core": ["esp32:esp32", "renesas_uno:unor4wifi"], "voltage": [3.3, 5]}, "keywords": ["aily", "iot", "mqtt", "ethernet", "wifi", "PubSubClient", "SRAM", "network", "物联网"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "i3water"}