{"name": "@aily-project/lib-adafruit-shtc3", "nickname": "SHTC3温湿度传感器", "author": "Adafruit", "description": "SHTC3数字温湿度传感器库，支持I2C通信，低功耗模式，适用于精确温湿度检测", "version": "1.0.0", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "sensor", "temperature", "humidity", "SHTC3", "I2C", "digital", "precision", "温度", "湿度", "传感器", "数字", "精密", "shtc3_init", "shtc3_read_temperature", "shtc3_read_humidity", "shtc3_read_both", "shtc3_is_connected", "shtc3_sleep", "shtc3_set_power_mode"], "scripts": {}, "dependencies": {}, "devDependencies": {}}