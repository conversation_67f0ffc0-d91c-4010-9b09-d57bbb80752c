{"name": "@aily-project/lib-esp32-ble-mouse", "nickname": "BLE蓝牙鼠标", "author": "aily Project", "description": "适用于ESP32的BLE鼠标库，支持鼠标移动、点击、滚轮等操作", "version": "1.0.0", "compatibility": {"core": ["esp32:esp32c3", "esp32:esp32s3"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "esp32", "ble", "bluetooth", "mouse", "hid", "ble_mouse_init", "ble_mouse_move", "ble_mouse_click", "ble_mouse_scroll", "ble_keyboard_press", "ble_keyboard_release", "ble_keyboard_write", "ble_gamepad_press", "ble_gamepad_release", "ble_gamepad_joystick"], "scripts": {}, "dependencies": {}, "devDependencies": {}}