[{"type": "sht4x_init", "message0": "初始化SHT4x传感器 地址 %1", "args0": [{"type": "field_dropdown", "name": "ADDRESS", "options": [["0x44 (默认)", "0x44"], ["0x45", "0x45"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"type": "sht4x_read_temperature", "message0": "SHT4x读取温度 (℃)", "inputsInline": true, "output": "Number", "colour": "#4CAF50"}, {"type": "sht4x_read_humidity", "message0": "SHT4x读取湿度 (%)", "inputsInline": true, "output": "Number", "colour": "#4CAF50"}, {"type": "sht4x_read_both", "message0": "SHT4x读取温湿度", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"type": "sht4x_get_last_temperature", "message0": "获取最后读取的温度 (℃)", "inputsInline": true, "output": "Number", "colour": "#4CAF50"}, {"type": "sht4x_get_last_humidity", "message0": "获取最后读取的湿度 (%)", "inputsInline": true, "output": "Number", "colour": "#4CAF50"}, {"type": "sht4x_set_precision", "message0": "设置SHT4x精度 %1", "args0": [{"type": "field_dropdown", "name": "PRECISION", "options": [["高精度", "SHT4X_HIGH_PRECISION"], ["中等精度", "SHT4X_MED_PRECISION"], ["低精度", "SHT4X_LOW_PRECISION"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"type": "sht4x_set_heater", "message0": "设置SHT4x加热器 %1", "args0": [{"type": "field_dropdown", "name": "HEATER", "options": [["不加热", "SHT4X_NO_HEATER"], ["高温1秒", "SHT4X_HIGH_HEATER_1S"], ["高温0.1秒", "SHT4X_HIGH_HEATER_100MS"], ["中温1秒", "SHT4X_MED_HEATER_1S"], ["中温0.1秒", "SHT4X_MED_HEATER_100MS"], ["低温1秒", "SHT4X_LOW_HEATER_1S"], ["低温0.1秒", "SHT4X_LOW_HEATER_100MS"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"type": "sht4x_read_serial", "message0": "读取SHT4x序列号", "inputsInline": true, "output": "Number", "colour": "#4CAF50"}, {"type": "sht4x_reset", "message0": "重置SHT4x传感器", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"type": "sht4x_simple_read", "message0": "简单读取SHT4x %1 地址 %2", "args0": [{"type": "field_dropdown", "name": "TYPE", "options": [["温度", "temperature"], ["湿度", "humidity"]]}, {"type": "field_dropdown", "name": "ADDRESS", "options": [["0x44 (默认)", "0x44"], ["0x45", "0x45"]]}], "inputsInline": true, "output": "Number", "colour": "#4CAF50"}]