{"toolbox_name": "Serial", "serial_begin": {"message0": "Initialize Serial%1 with baud rate %2"}, "serial_available": {"message0": "Serial%1 buffer has data"}, "serial_read": {"message0": "Read Serial%1 data %2", "args0": [null, {"options": [["read", "read"], ["peek", "peek"], ["parse integer", "parseInt"], ["parse float", "parseFloat"]]}]}, "serial_print": {"message0": "Serial%1 outputs %2"}, "serial_println": {"message0": "Serial%1 outputs %2 with newline"}, "serial_write": {"message0": "Serial%1 outputs raw data %2"}, "serial_read_string": {"message0": "Read Serial%1 string"}}