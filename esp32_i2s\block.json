[{"type": "esp32_sd_init", "message0": "初始化SD卡 CS:%1 SCK:%2 MOSI:%3 MISO:%4", "args0": [{"type": "input_value", "name": "CS_PIN", "check": "Number"}, {"type": "input_value", "name": "SCK_PIN", "check": "Number"}, {"type": "input_value", "name": "MOSI_PIN", "check": "Number"}, {"type": "input_value", "name": "MISO_PIN", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50", "inputsInline": true}, {"type": "esp32_i2s_init_and_begin", "message0": "初始化I2S麦克风 SCK:%1 WS:%2 SD:%3 采样率%4Hz 缓冲区大小%5", "args0": [{"type": "input_value", "name": "SCK_PIN", "check": "Number"}, {"type": "input_value", "name": "WS_PIN", "check": "Number"}, {"type": "input_value", "name": "SD_PIN", "check": "Number"}, {"type": "field_dropdown", "name": "SAMPLE_RATE", "options": [["8000", "8000"], ["16000", "16000"], ["22050", "22050"], ["44100", "44100"]]}, {"type": "field_dropdown", "name": "BUFFER_SIZE", "options": [["512", "512"], ["1024", "1024"], ["2048", "2048"], ["4096", "4096"]]}], "previousStatement": null, "nextStatement": null, "colour": "#FF9800", "inputsInline": true}, {"type": "esp32_i2s_read_samples", "message0": "I2S麦克风%1读取音频样本", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}], "previousStatement": null, "nextStatement": null, "colour": "#FF9800", "inputsInline": true}, {"type": "esp32_i2s_get_level", "message0": "获取I2S麦克风%1的%2", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}, {"type": "field_dropdown", "name": "LEVEL_TYPE", "options": [["平均电平", "average"], ["峰值电平", "peak"], ["RMS电平", "rms"], ["分贝值", "decibels"], ["百分比", "percentage"]]}], "output": "Number", "colour": "#FF9800", "inputsInline": true}, {"type": "esp32_i2s_sound_detected", "message0": "I2S麦克风%1检测到声音(阈值%2)", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}, {"type": "input_value", "name": "THRESHOLD"}], "output": "Boolean", "colour": "#FF9800", "inputsInline": true}, {"type": "esp32_i2s_get_quality", "message0": "获取I2S麦克风%1音质等级", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}], "output": "String", "colour": "#FF9800", "inputsInline": true}, {"type": "esp32_i2s_get_freq_energy", "message0": "获取I2S麦克风%1的%2频段能量", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}, {"type": "field_dropdown", "name": "FREQ_BAND", "options": [["低频", "low"], ["中频", "mid"], ["高频", "high"]]}], "output": "Number", "colour": "#FF9800", "inputsInline": true}, {"type": "esp32_i2s_calibrate", "message0": "校准I2S麦克风%1噪声基准", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}], "previousStatement": null, "nextStatement": null, "colour": "#FF9800", "inputsInline": true}, {"type": "esp32_i2s_get_snr", "message0": "获取I2S麦克风%1信噪比", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}], "output": "Number", "colour": "#FF9800", "inputsInline": true}, {"type": "esp32_i2s_start_recording", "message0": "I2S麦克风%1开始录制 文件名%2 时长%3秒", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}, {"type": "input_value", "name": "FILENAME", "check": "String"}, {"type": "input_value", "name": "DURATION", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#2196F3", "inputsInline": true}, {"type": "esp32_i2s_stop_recording", "message0": "I2S麦克风%1停止录制", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}], "previousStatement": null, "nextStatement": null, "colour": "#2196F3", "inputsInline": true}, {"type": "esp32_i2s_update_recording", "message0": "I2S麦克风%1更新录制状态", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}], "previousStatement": null, "nextStatement": null, "colour": "#2196F3", "inputsInline": true}, {"type": "esp32_i2s_is_recording", "message0": "I2S麦克风%1正在录制", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}], "output": "Boolean", "colour": "#2196F3", "inputsInline": true}, {"type": "esp32_i2s_get_recording_status", "message0": "获取I2S麦克风%1录制状态", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}], "output": "String", "colour": "#2196F3", "inputsInline": true}, {"type": "esp32_i2s_get_recorded_time", "message0": "获取I2S麦克风%1已录制时长", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}], "output": "Number", "colour": "#2196F3", "inputsInline": true}, {"type": "esp32_i2s_list_files", "message0": "I2S麦克风%1列出SD卡音频文件", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}], "previousStatement": null, "nextStatement": null, "colour": "#2196F3", "inputsInline": true}, {"type": "esp32_i2s_delete_file", "message0": "I2S麦克风%1删除音频文件%2", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}, {"type": "input_value", "name": "FILENAME", "check": "String"}], "previousStatement": null, "nextStatement": null, "colour": "#2196F3", "inputsInline": true}, {"type": "esp32_i2s_init_speaker", "message0": "初始化I2S功放 BCLK:%1 LRC:%2 DIN:%3", "args0": [{"type": "input_value", "name": "BCLK_PIN", "check": "Number"}, {"type": "input_value", "name": "LRC_PIN", "check": "Number"}, {"type": "input_value", "name": "DIN_PIN", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#9C27B0", "inputsInline": true}, {"type": "esp32_i2s_play_wav_file", "message0": "I2S功放%1播放WAV文件%2", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}, {"type": "input_value", "name": "FILENAME", "check": "String"}], "previousStatement": null, "nextStatement": null, "colour": "#9C27B0", "inputsInline": true}, {"type": "esp32_i2s_stop_playback", "message0": "I2S功放%1停止播放", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}], "previousStatement": null, "nextStatement": null, "colour": "#9C27B0", "inputsInline": true}, {"type": "esp32_i2s_update_playback", "message0": "I2S功放%1更新播放状态", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}], "previousStatement": null, "nextStatement": null, "colour": "#9C27B0", "inputsInline": true}, {"type": "esp32_i2s_play_tone", "message0": "I2S功放%1播放音调 频率%2Hz 时长%3毫秒", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}, {"type": "input_value", "name": "FREQUENCY", "check": "Number"}, {"type": "input_value", "name": "DURATION", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#673AB7", "inputsInline": true}, {"type": "esp32_i2s_play_note_name", "message0": "I2S功放%1播放音符%2 时长%3毫秒", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}, {"type": "field_dropdown", "name": "NOTE_NAME", "options": [["C4 (Do)", "C4"], ["D4 (Re)", "D4"], ["E4 (Mi)", "E4"], ["F4 (Fa)", "F4"], ["G4 (So)", "G4"], ["A4 (La)", "A4"], ["B4 (Si)", "B4"], ["C5 (<PERSON>)", "C5"]]}, {"type": "input_value", "name": "DURATION", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#673AB7", "inputsInline": true}, {"type": "esp32_i2s_play_twinkle_star", "message0": "I2S功放%1播放小星星", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}], "previousStatement": null, "nextStatement": null, "colour": "#673AB7", "inputsInline": true}, {"type": "esp32_i2s_play_beep", "message0": "I2S功放%1播放蜂鸣 时长%2毫秒", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}, {"type": "input_value", "name": "DURATION", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#673AB7", "inputsInline": true}, {"type": "esp32_i2s_update_melody", "message0": "I2S功放%1更新旋律播放", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}], "previousStatement": null, "nextStatement": null, "colour": "#673AB7", "inputsInline": true}, {"type": "esp32_i2s_set_volume", "message0": "I2S功放%1设置音量%2", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}, {"type": "input_value", "name": "VOLUME", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#E91E63", "inputsInline": true}, {"type": "esp32_i2s_is_playing", "message0": "I2S功放%1正在播放WAV", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}], "output": "Boolean", "colour": "#E91E63", "inputsInline": true}, {"type": "esp32_i2s_is_melody_playing", "message0": "I2S功放%1正在播放旋律", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}], "output": "Boolean", "colour": "#E91E63", "inputsInline": true}, {"type": "esp32_i2s_update_all", "message0": "I2S音频功放%1更新所有状态", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "microphone", "variableTypes": ["EspI2S"], "defaultType": "EspI2S"}], "previousStatement": null, "nextStatement": null, "colour": "#607D8B", "inputsInline": true}]