{"toolbox_name": "Texto", "string_add_string": {"message0": "String(%1) + String(%2)"}, "string_charAt": {"message0": "Caractere na posição %2 de %1"}, "string_length": {"message0": "Número de caracteres de %1"}, "string_indexOf": {"message0": "%1 contém %2?"}, "string_substring": {"message0": "%1 obter caracteres de %2 %3 até %4 %5", "args0": [null, {"options": [["o", "0"], ["o último", "1"]]}, null, {"options": [["o", "0"], ["o último", "1"]]}, null]}, "string_find_str": {"message0": "Encontrar %1 em %2 na posição %3", "args0": [null, null, {"options": [["<PERSON><PERSON>", "indexOf"], ["Última", "lastIndexOf"]]}]}, "string_to": {"message0": "Converter string %1 para %2", "args0": [null, {"options": [["inteiro", "toInt"], ["decimal", "toFloat"]]}]}, "number_to": {"message0": "Converter número %1 para string ASCII"}, "toascii": {"message0": "Converter caractere %1 para valor ASCII"}, "number_to_string": {"message0": "Converter número %1 para string"}, "text": {"message0": "%1"}, "text_join": {"message0": ""}, "text_create_join_container": {"message0": "%{BKY_TEXT_CREATE_JOIN_TITLE_JOIN} %1 %2"}, "text_create_join_item": {"message0": "%{BKY_TEXT_CREATE_JOIN_ITEM_TITLE_ITEM}"}, "text_append": {"message0": "Anexar texto %2 após %1"}, "text_length": {"message0": "Tamanho de %1"}, "text_isEmpty": {"message0": "%1 está vazio"}, "text_indexOf": {"message0": "No texto %1 %2 %3", "args0": [null, {"options": [["procurar primeira ocorrência do texto", "FIRST"], ["procurar última ocorrência do texto", "LAST"]]}, null]}, "text_charAt": {"message0": "No texto %1 %2", "args0": [null, {"options": [["Obter caractere #", "FROM_START"], ["Obter caractere da última posição #", "FROM_END"], ["Obter primeiro caractere", "FIRST"], ["Obter <PERSON><PERSON>imo car<PERSON>ere", "LAST"], ["Obter caractere aleatório", "RANDOM"]]}]}, "tt_getSubstring": {"message0": "obter substring no texto %1", "message1": "%1 %2", "args1": [{"options": [["do caractere #", "FROM_START"], ["do # a partir do final", "FROM_END"], ["do primeiro caractere", "FIRST"]]}, null], "message2": "%1 %2", "args2": [{"options": [["até o caractere #", "FROM_START"], ["até o # a partir do final", "FROM_END"], ["até o último caractere", "LAST"]]}, null]}, "text_changeCase": {"message0": "%1 %2", "args0": [{"options": [["converter para maiúsculas", "UPPERCASE"], ["converter para minúsculas", "LOWERCASE"], ["converter para caixa de título", "TITLECASE"]]}, null]}, "text_trim": {"message0": "%1 aparar %2", "args0": [{"options": [["aparar espaços de ambos os lados", "BOTH"], ["aparar espaços do lado esquerdo", "LEFT"], ["aparar espaços do lado direito", "RIGHT"]]}, null]}, "text_count": {"message0": "Contar ocorrências de %1 em %2"}, "text_replace": {"message0": "Substituir %2 por %3 em %1"}, "text_reverse": {"message0": "Inverter texto %1"}}