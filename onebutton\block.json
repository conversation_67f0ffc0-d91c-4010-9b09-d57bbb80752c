[{"inputsInline": true, "message0": "初始化按钮 在引脚 %1", "type": "onebutton_setup", "args0": [{"type": "field_dropdown", "name": "PIN", "options": "${board.digitalPins}"}], "previousStatement": null, "nextStatement": null, "colour": "#FF5733"}, {"inputsInline": true, "message0": "按钮单击事件 %1", "type": "onebutton_attachClick", "args0": [{"type": "input_statement", "name": "CLICK_FUNC"}], "previousStatement": null, "nextStatement": null, "colour": "#FF5733"}, {"inputsInline": true, "message0": "按钮双击事件 %1", "type": "onebutton_attachDoubleClick", "args0": [{"type": "input_statement", "name": "DOUBLE_CLICK_FUNC"}], "previousStatement": null, "nextStatement": null, "colour": "#FF5733"}, {"inputsInline": true, "message0": "按钮长按开始事件 %1", "type": "onebutton_attachLongPressStart", "args0": [{"type": "input_statement", "name": "LONG_PRESS_START_FUNC"}], "previousStatement": null, "nextStatement": null, "colour": "#FF5733"}, {"inputsInline": true, "message0": "按钮长按停止事件 %1", "type": "onebutton_attachLongPressStop", "args0": [{"type": "input_statement", "name": "LONG_PRESS_STOP_FUNC"}], "previousStatement": null, "nextStatement": null, "colour": "#FF5733"}, {"inputsInline": true, "message0": "按钮长按持续事件 %1", "type": "onebutton_attachDuringLongPress", "args0": [{"type": "input_statement", "name": "DURING_LONG_PRESS_FUNC"}], "previousStatement": null, "nextStatement": null, "colour": "#FF5733"}, {"inputsInline": true, "message0": "检查按钮状态", "type": "onebutton_tick", "previousStatement": null, "nextStatement": null, "colour": "#FF5733"}]