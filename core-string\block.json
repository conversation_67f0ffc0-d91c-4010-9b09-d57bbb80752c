[{"type": "string_create", "message0": "创建字符串变量 %1 值为 %2", "args0": [{"type": "field_variable", "name": "VAR", "variable": "myStr", "variableTypes": ["String"], "defaultType": "String"}, {"type": "input_value", "name": "VALUE", "check": ["String", "Number"]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#9966ff"}, {"type": "string_char_create", "message0": "创建字符变量 %1 值为 %2", "args0": [{"type": "field_variable", "name": "VAR", "variable": "myChar", "variableTypes": ["char"], "defaultType": "char"}, {"type": "input_value", "name": "VALUE", "check": "String"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#9966ff"}, {"type": "string_get", "message0": "字符串 %1", "args0": [{"type": "field_variable", "name": "VAR", "variable": "myStr", "variableTypes": ["String"], "defaultType": "String"}], "output": "String", "colour": "#9966ff"}, {"type": "string_length", "message0": "字符串 %1 的长度", "args0": [{"type": "input_value", "name": "STRING", "check": "String"}], "output": "Number", "colour": "#9966ff"}, {"type": "string_concat", "message0": "连接字符串 %1 和 %2", "args0": [{"type": "input_value", "name": "STRING1", "check": ["String", "Number"]}, {"type": "input_value", "name": "STRING2", "check": ["String", "Number"]}], "inputsInline": true, "output": "String", "colour": "#9966ff"}, {"type": "string_char_at", "message0": "字符串 %1 的第 %2 个字符", "args0": [{"type": "input_value", "name": "STRING", "check": "String"}, {"type": "input_value", "name": "INDEX", "check": "Number"}], "inputsInline": true, "output": "String", "colour": "#9966ff"}, {"type": "string_substring", "message0": "从字符串 %1 中提取从位置 %2 到 %3 的子串", "args0": [{"type": "input_value", "name": "STRING", "check": "String"}, {"type": "input_value", "name": "START", "check": "Number"}, {"type": "input_value", "name": "END", "check": "Number"}], "inputsInline": true, "output": "String", "colour": "#9966ff"}, {"type": "string_index_of", "message0": "在字符串 %1 中查找 %2 的位置", "args0": [{"type": "input_value", "name": "STRING", "check": "String"}, {"type": "input_value", "name": "SEARCH", "check": "String"}], "inputsInline": true, "output": "Number", "colour": "#9966ff"}, {"type": "string_equals", "message0": "字符串 %1 等于 %2", "args0": [{"type": "input_value", "name": "STRING1", "check": "String"}, {"type": "input_value", "name": "STRING2", "check": "String"}], "inputsInline": true, "output": "Boolean", "colour": "#9966ff"}, {"type": "string_to_int", "message0": "将字符串 %1 转换为整数", "args0": [{"type": "input_value", "name": "STRING", "check": "String"}], "output": "Number", "colour": "#9966ff"}, {"type": "string_to_float", "message0": "将字符串 %1 转换为浮点数", "args0": [{"type": "input_value", "name": "STRING", "check": "String"}], "output": "Number", "colour": "#9966ff"}, {"type": "string_replace", "message0": "在字符串 %1 中将 %2 替换为 %3", "args0": [{"type": "input_value", "name": "STRING", "check": "String"}, {"type": "input_value", "name": "FIND", "check": "String"}, {"type": "input_value", "name": "REPLACE", "check": "String"}], "inputsInline": true, "output": "String", "colour": "#9966ff"}, {"type": "string_to_upper", "message0": "将字符串 %1 转为大写", "args0": [{"type": "input_value", "name": "STRING", "check": "String"}], "output": "String", "colour": "#9966ff"}, {"type": "string_to_lower", "message0": "将字符串 %1 转为小写", "args0": [{"type": "input_value", "name": "STRING", "check": "String"}], "output": "String", "colour": "#9966ff"}, {"type": "string_trim", "message0": "去除字符串 %1 两端的空格", "args0": [{"type": "input_value", "name": "STRING", "check": "String"}], "output": "String", "colour": "#9966ff"}, {"type": "string_starts_with", "message0": "字符串 %1 是否以 %2 开头", "args0": [{"type": "input_value", "name": "STRING", "check": "String"}, {"type": "input_value", "name": "PREFIX", "check": "String"}], "inputsInline": true, "output": "Boolean", "colour": "#9966ff"}, {"type": "string_ends_with", "message0": "字符串 %1 是否以 %2 结尾", "args0": [{"type": "input_value", "name": "STRING", "check": "String"}, {"type": "input_value", "name": "SUFFIX", "check": "String"}], "inputsInline": true, "output": "Boolean", "colour": "#9966ff"}, {"type": "string_literal", "message0": "文本 %1", "args0": [{"type": "field_input", "name": "TEXT", "text": "Hello"}], "output": "String", "colour": "#9966ff"}, {"type": "string_char_literal", "message0": "字符 %1", "args0": [{"type": "field_input", "name": "CHAR", "text": "A"}], "output": "String", "colour": "#9966ff"}]