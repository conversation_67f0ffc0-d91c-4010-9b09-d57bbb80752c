{"toolbox_name": "Matemáticas", "math_number": {"message0": "%1"}, "math_arithmetic": {"message0": "%1 %2 %3", "args0": [null, {"options": [["<PERSON><PERSON>", "ADD"], ["<PERSON><PERSON>", "MINUS"], ["Multiplicar", "MULTIPLY"], ["<PERSON><PERSON><PERSON>", "DIVIDE"], ["<PERSON><PERSON><PERSON><PERSON>", "MODULO"], ["Potencia", "POWER"]]}, null]}, "math_single": {"message0": "%1 %2", "args0": [{"options": [["<PERSON><PERSON><PERSON>", "ROOT"], ["Valor absoluto", "ABS"], ["Negativo", "NEG"], ["ln", "LN"], ["log10", "LOG10"], ["e^", "EXP"], ["10^", "POW10"]]}, null]}, "math_trig": {"message0": "%1 %2", "args0": [{"options": [["<PERSON><PERSON>", "SIN"], ["<PERSON><PERSON><PERSON>", "COS"], ["Tangente", "TAN"], ["Arcoseno", "ASIN"], ["Arc<PERSON>ose<PERSON>", "ACOS"], ["Arcotangente", "ATAN"]]}, null]}, "math_constant": {"message0": "%1", "args0": [{"options": [["π", "PI"], ["e", "E"], ["Proporción áurea", "GOLDEN_RATIO"], ["sqrt(2)", "SQRT2"], ["sqrt(1/2)", "SQRT1_2"], ["∞", "INFINITY"]]}]}, "math_number_property": {"message0": "%1 %2", "args0": [null, {"options": [["es par", "EVEN"], ["es impar", "ODD"], ["es primo", "PRIME"], ["es entero", "WHOLE"], ["es positivo", "POSITIVE"], ["es negativo", "NEGATIVE"], ["es divisible por", "DIVISIBLE_BY"]]}]}, "math_change": {"message0": "cambiar %1 por %2"}, "math_round": {"message0": "%1 %2", "args0": [{"options": [["Redondear", "ROUND"], ["Redondear hacia arriba", "ROUNDUP"], ["Redondear hacia abajo", "ROUNDDOWN"]]}, null]}, "math_on_list": {"message0": "%1 %2", "args0": [{"options": [["<PERSON><PERSON> lista", "SUM"], ["<PERSON><PERSON><PERSON>", "MIN"], ["Máximo de la lista", "MAX"], ["Promedio de la lista", "AVERAGE"], ["Mediana de la lista", "MEDIAN"], ["Moda de la lista", "MODE"], ["Desviación estándar de la lista", "STD_DEV"], ["Elemento aleatorio de la lista", "RANDOM"]]}, null]}, "math_modulo": {"message0": "residuo de %1 ÷ %2"}, "math_constrain": {"message0": "limitar %1 entre %2 y %3"}, "math_random_int": {"message0": "número entero aleatorio de %1 a %2"}, "math_random_float": {"message0": "fracción aleatoria"}, "math_atan2": {"message0": "atan2 del punto (x: %1, y: %2)"}, "math_round_to_decimal": {"message0": "redondear %1 a %2 decimales"}, "math_bitwise_not": {"message0": "~ %1"}, "map_to": {"message0": "Mapear %1 de [%2,%3] a [%4,%5]"}, "constrain": {"message0": "Restringir %1 entre (mínimo) %2 y (máximo) %3"}}