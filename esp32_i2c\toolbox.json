{"kind": "category", "name": "ESP32 I2C", "colour": "#FF9800", "contents": [{"kind": "label", "text": "I2C主模式"}, {"kind": "block", "type": "esp32_i2c_begin_simple"}, {"kind": "block", "type": "esp32_i2c_begin", "inputs": {"SDA_PIN": {"shadow": {"type": "math_number", "fields": {"NUM": 21}}}, "SCL_PIN": {"shadow": {"type": "math_number", "fields": {"NUM": 22}}}}}, {"kind": "block", "type": "esp32_i2c_scan_devices"}, {"kind": "sep"}, {"kind": "label", "text": "数据传输（基础）"}, {"kind": "block", "type": "esp32_i2c_begin_transmission", "inputs": {"ADDRESS": {"shadow": {"type": "math_number", "fields": {"NUM": "0x3C"}}}}}, {"kind": "block", "type": "esp32_i2c_write_byte", "inputs": {"DATA": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}}}, {"kind": "block", "type": "esp32_i2c_write_string", "inputs": {"DATA": {"shadow": {"type": "text", "fields": {"TEXT": "Hello"}}}}}, {"kind": "block", "type": "esp32_i2c_end_transmission"}, {"kind": "sep"}, {"kind": "label", "text": "数据接收（基础）"}, {"kind": "block", "type": "esp32_i2c_request_from", "inputs": {"ADDRESS": {"shadow": {"type": "math_number", "fields": {"NUM": "0x3C"}}}, "QUANTITY": {"shadow": {"type": "math_number", "fields": {"NUM": 16}}}}}, {"kind": "block", "type": "esp32_i2c_available"}, {"kind": "block", "type": "esp32_i2c_read"}, {"kind": "sep"}, {"kind": "label", "text": "简化操作"}, {"kind": "block", "type": "esp32_i2c_write_to_device", "inputs": {"DATA": {"shadow": {"type": "text", "fields": {"TEXT": "Hello World"}}}}}, {"kind": "block", "type": "esp32_i2c_read_from_device", "inputs": {"QUANTITY": {"shadow": {"type": "math_number", "fields": {"NUM": 16}}}}}, {"kind": "sep"}, {"kind": "label", "text": "I2C从模式"}, {"kind": "block", "type": "esp32_i2c_slave_begin", "inputs": {"ADDRESS": {"shadow": {"type": "math_number", "fields": {"NUM": "0x55"}}}, "SDA_PIN": {"shadow": {"type": "math_number", "fields": {"NUM": 21}}}, "SCL_PIN": {"shadow": {"type": "math_number", "fields": {"NUM": 22}}}}}, {"kind": "block", "type": "esp32_i2c_on_receive"}, {"kind": "block", "type": "esp32_i2c_on_request"}, {"kind": "block", "type": "esp32_i2c_slave_print", "inputs": {"DATA": {"shadow": {"type": "text", "fields": {"TEXT": "Response"}}}}}]}