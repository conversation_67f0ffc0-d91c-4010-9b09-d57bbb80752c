{"name": "@aily-project/lib-ai-assistant", "nickname": "ai-assistant", "author": "<PERSON>Jumper", "description": "用于小智语音识别后向各种设备通过串口发送控制命令，供主设备识别和执行相应操作,支持arduino、mega2560、esp32s3。", "version": "0.0.1", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp32:esp32s3"], "voltage": [3.3, 5]}, "keywords": ["小智", "AI", "AI控制", "AI—Assistant", "语音识别", "语音控制", "语音助手", "智能家居", "智能设备", "智能语音", "artificial intelligence"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "openjumper"}