{"toolbox_name": "ロジック", "controls_if": {"message0": "🔀もし %1", "message1": "実行する %1"}, "controls_ifelse": {"message0": "🔀もし %1", "message1": "実行する %1", "message2": "そうでなければ %1"}, "logic_compare": {"message0": "%1 %2 %3", "args0": [null, {"options": [["==", "EQ"], ["!=", "NEQ"], ["<", "LT"], [">", "GT"], [">=", "GTE"], ["<=", "LTE"]]}, null]}, "logic_operation": {"message0": "%1 %2 %3", "args0": [null, {"options": [["そして", "AND"], ["または", "OR"]]}, null]}, "logic_negate": {"message0": "ではない %1", "args0": [{"type": "input_value", "name": "BOOL", "check": "Boolean"}]}, "logic_boolean": {"message0": "%1", "args0": [{"options": [["真", "true"], ["偽", "false"]]}]}, "logic_ternary": {"message0": "を主張する %1", "message1": "trueの場合 %1", "message2": "falseの場合 %1"}}