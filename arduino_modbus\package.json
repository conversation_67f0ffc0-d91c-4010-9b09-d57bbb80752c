{"name": "@aily-project/lib-arduino-modbus", "nickname": "Arduino Modbus通信库", "author": "<PERSON><PERSON><PERSON><PERSON>", "description": "Arduino Modbus RTU/TCP客户端和服务器通信库，支持读写coils、寄存器等功能", "version": "0.0.1", "compatibility": {"core": ["arduino:avr", "arduino:samd", "renesas_uno:minima", "renesas_uno:unor4wifi"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "modbus", "rtu", "tcp", "rs485", "communication", "industrial", "coil", "register", "client", "server", "物联网"], "scripts": {}, "dependencies": {}, "devDependencies": {}}