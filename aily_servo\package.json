{"name": "@aily-project/lib-aily_servo", "nickname": "Aily 舵机控制库", "author": "aily Project", "description": "基于Servo库封装的舵机控制支持库，适用于Arduino UNO、MEGA、UNO R4、ESP32等开发板，支持角度控制、脉宽控制等功能", "version": "0.0.1", "compatibility": {"core": ["arduino:avr", "arduino:mega", "renesas_uno:unor4", "renesas_uno:unor4wifi", "esp32:esp32", "esp32:esp32s3", "esp32:esp32c3"], "voltage": [3.3, 5]}, "keywords": ["aily", "servo", "舵机", "PWM", "角度控制", "a<PERSON><PERSON><PERSON>", "esp32"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "i3water", "url": "https://madhephaestus.github.io/ESP32Servo/"}