{"toolbox_name": "LCD1602 I2C", "lcd_i2c_init": {"message0": "Initialize LCD I2C display Address %1 Columns %2 Rows %3", "args0": [[["0x27", "0x27"], ["0x26", "0x26"], ["0x25", "0x25"], ["0x24", "0x24"], ["0x23", "0x23"], ["0x22", "0x22"], ["0x21", "0x21"], ["0x20", "0x20"]], null, null]}, "lcd_i2c_clear": {"message0": "Clear LCD display"}, "lcd_i2c_set_cursor": {"message0": "Set LCD cursor to column %1 row %2"}, "lcd_i2c_print": {"message0": "LCD print %1"}, "lcd_i2c_print_position": {"message0": "LCD print %3 at column %1 row %2"}, "lcd_i2c_backlight_on": {"message0": "Turn on LCD backlight"}, "lcd_i2c_backlight_off": {"message0": "Turn off LCD backlight"}, "lcd_i2c_custom_char": {"message0": "Custom character %1 number %2", "args0": [null, ["0", "0"], ["1", "1"], ["2", "2"], ["3", "3"], ["4", "4"], ["5", "5"], ["6", "6"], ["7", "7"]]}}