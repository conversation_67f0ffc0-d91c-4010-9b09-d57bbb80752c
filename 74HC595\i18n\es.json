{"toolbox_name": "Registro de desplazamiento", "74hc595_create": {"message0": "Inicializar 74HC595 %1 cantidad %2 data:%3  clock:%4 latch:%5", "args0": [null, null, {"options": "${board.digitalPins}"}, {"options": "${board.digitalPins}"}, {"options": "${board.digitalPins}"}]}, "74hc595_set": {"message0": "74HC595 %1 establecer pin %2 a %3", "args0": [null, {"options": "${board.digitalPins}"}, {"options": [["ALTO", "HIGH"], ["BAJO", "LOW"]]}]}, "74hc595_setAll": {"message0": "74HC595 %1 establecer todos los pines a %2", "args0": [null, {"options": [["ALTO", "High"], ["BAJO", "Low"]]}]}, "74hc595_setAllBin": {"message0": "74HC595%1 establecer niveles de salida usando la matriz %2[]", "args0": [null, null]}, "74hc595_getstate": {"message0": "74HC595%1 obtener el estado del pin de salida número %2", "args0": [null, null]}}