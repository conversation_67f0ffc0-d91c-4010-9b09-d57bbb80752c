[{"inputsInline": true, "message0": "初始化风速风向传感器 风速引脚%1 风向引脚%2", "type": "wind_begin", "args0": [{"type": "field_dropdown", "name": "SPEED_PIN", "options": "${board.digitalPins}"}, {"type": "field_dropdown", "name": "DIR_PIN", "options": "${board.analogPins}"}], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"inputsInline": true, "message0": "初始化风速传感器 引脚%1", "type": "wind_begin_speed", "args0": [{"type": "field_dropdown", "name": "SPEED_PIN", "options": "${board.digitalPins}"}], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"inputsInline": true, "message0": "初始化风向传感器 引脚%1", "type": "wind_begin_direction", "args0": [{"type": "field_dropdown", "name": "DIR_PIN", "options": "${board.analogPins}"}], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"inputsInline": true, "message0": "更新风速风向数据", "type": "wind_update", "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"inputsInline": true, "message0": "更新风速数据", "type": "wind_update_speed", "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"inputsInline": true, "message0": "更新风向数据", "type": "wind_update_direction", "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"inputsInline": true, "message0": "获取风速(km/h)", "type": "wind_get_speed_kmh", "output": "Number", "colour": "#2196F3"}, {"inputsInline": true, "message0": "获取风速(mph)", "type": "wind_get_speed_mph", "output": "Number", "colour": "#2196F3"}, {"inputsInline": true, "message0": "获取风向(度)", "type": "wind_get_direction", "output": "Number", "colour": "#2196F3"}, {"inputsInline": true, "message0": "获取风向(字符串)", "type": "wind_get_direction_string", "output": "String", "colour": "#2196F3"}, {"inputsInline": true, "message0": "获取风向ADC值", "type": "wind_get_direction_adc", "output": "Number", "colour": "#2196F3"}, {"inputsInline": true, "message0": "风速风向数据是否准备就绪", "type": "wind_is_data_ready", "output": "Boolean", "colour": "#9C27B0"}, {"inputsInline": true, "message0": "初始化雨量传感器 引脚%1", "type": "rain_init", "args0": [{"type": "field_dropdown", "name": "PIN", "options": "${board.digitalPins}"}], "previousStatement": null, "nextStatement": null, "colour": "#2196F3"}, {"type": "rain_update", "message0": "更新雨量数据", "previousStatement": null, "nextStatement": null, "colour": "#2196F3"}, {"type": "rain_get_total", "message0": "获取累计降雨量 (mm)", "output": "Number", "colour": "#2196F3"}, {"type": "rain_get_hour", "message0": "获取1小时降雨量 (mm)", "output": "Number", "colour": "#2196F3"}, {"type": "rain_get_day", "message0": "获取24小时降雨量 (mm)", "output": "Number", "colour": "#2196F3"}, {"type": "rain_get_ticks", "message0": "获取雨滴计数", "output": "Number", "colour": "#2196F3"}, {"type": "rain_is_data_ready", "message0": "雨量数据准备就绪", "output": "Boolean", "colour": "#2196F3"}]