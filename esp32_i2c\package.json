{"name": "@aily-project/lib-esp32-i2c", "nickname": "ESP32 I2C通信库", "author": "aily Project", "description": "ESP32专用I2C通信支持库，提供主从模式通信、设备扫描等功能，支持自定义引脚配置", "version": "1.0.0", "compatibility": {"core": ["esp32:esp32", "esp32:esp32c3", "esp32:esp32s2", "esp32:esp32s3", "esp32:esp32c6", "esp32:esp32h2"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "esp32", "i2c", "wire", "communication", "master", "slave", "scan", "iic", "两线通信", "esp32_i2c_begin", "esp32_i2c_scan_devices", "esp32_i2c_write_to_device", "esp32_i2c_read_from_device", "esp32_i2c_slave_begin"], "scripts": {}, "dependencies": {}, "devDependencies": {}}