{"name": "@aily-project/lib-esp32-wifimanager", "nickname": "WiFi管理器", "author": "aily Project", "description": "ESP32 WiFiManager库，提供WiFi配置门户和自动连接功能", "version": "1.0.0", "compatibility": {"core": ["esp32:esp32", "esp32:esp32c3", "esp32:esp32s2", "esp32:esp32s3"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "wifi", "w<PERSON><PERSON><PERSON>", "esp32", "esp8266", "autoconnect", "config", "portal", "wireless", "network"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": false}