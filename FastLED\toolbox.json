{"kind": "category", "name": "RGB灯带", "icon": "iconfont icon-ws2812", "contents": [{"kind": "block", "type": "fastled_init"}, {"kind": "block", "type": "fastled_set_pixel", "inputs": {"PIXEL": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}, "COLOR": {"shadow": {"type": "fastled_preset_color", "fields": {"COLOR": "CRGB::Red"}}}}}, {"kind": "block", "type": "fastled_fill_solid", "inputs": {"COLOR": {"shadow": {"type": "fastled_preset_color", "fields": {"COLOR": "CRGB::Red"}}}}}, {"kind": "block", "type": "fastled_clear"}, {"kind": "block", "type": "fastled_brightness", "inputs": {"BRIGHTNESS": {"shadow": {"type": "math_number", "fields": {"NUM": 100}}}}}, {"kind": "separator"}, {"kind": "block", "type": "fastled_preset_color"}, {"kind": "block", "type": "fastled_rgb", "inputs": {"RED": {"shadow": {"type": "math_number", "fields": {"NUM": 255}}}, "GREEN": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}, "BLUE": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}}}, {"kind": "block", "type": "fastled_hsv", "inputs": {"HUE": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}, "SATURATION": {"shadow": {"type": "math_number", "fields": {"NUM": 255}}}, "VALUE": {"shadow": {"type": "math_number", "fields": {"NUM": 255}}}}}, {"kind": "separator"}, {"kind": "block", "type": "fastled_rainbow", "inputs": {"INITIAL_HUE": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}, "DELTA_HUE": {"shadow": {"type": "math_number", "fields": {"NUM": 10}}}}}, {"kind": "block", "type": "fastled_fire_effect", "inputs": {"HEAT": {"shadow": {"type": "math_number", "fields": {"NUM": 120}}}, "COOLING": {"shadow": {"type": "math_number", "fields": {"NUM": 55}}}}}, {"kind": "block", "type": "fastled_meteor", "inputs": {"COLOR": {"shadow": {"type": "fastled_preset_color", "fields": {"COLOUR": "#ff0000"}}}, "SIZE": {"shadow": {"type": "math_number", "fields": {"NUM": 5}}}, "DECAY": {"shadow": {"type": "math_number", "fields": {"NUM": 128}}}, "SPEED": {"shadow": {"type": "math_number", "fields": {"NUM": 1}}}}}, {"kind": "block", "type": "fastled_palette_cycle", "inputs": {"SPEED": {"shadow": {"type": "math_number", "fields": {"NUM": 10}}}}}, {"kind": "block", "type": "fastled_breathing", "inputs": {"COLOR": {"shadow": {"type": "fastled_preset_color", "fields": {"COLOUR": "#0000ff"}}}, "SPEED": {"shadow": {"type": "math_number", "fields": {"NUM": 1}}}}}, {"kind": "block", "type": "fastled_twinkle", "inputs": {"COUNT": {"shadow": {"type": "math_number", "fields": {"NUM": 10}}}, "BACKGROUND": {"shadow": {"type": "fastled_preset_color", "fields": {"COLOUR": "#000000"}}}, "COLOR": {"shadow": {"type": "fastled_preset_color", "fields": {"COLOUR": "#ffffff"}}}}}]}