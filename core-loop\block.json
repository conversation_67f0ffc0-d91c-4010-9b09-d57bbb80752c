[{"type": "arduino_setup", "message0": "▶️初始化 %1", "args0": [{"type": "input_statement", "name": "ARDUINO_SETUP"}], "colour": "#3a3a3a", "tooltip": "arduino_setup", "helpUrl": ""}, {"type": "arduino_loop", "message0": "🔁循环执行 %1", "args0": [{"type": "input_statement", "name": "ARDUINO_LOOP"}], "colour": "#3a3a3a", "tooltip": "arduino_loop", "helpUrl": ""}, {"type": "controls_repeat_ext", "message0": "重复 %1 次", "args0": [{"type": "input_value", "name": "TIMES", "check": "Number"}], "message1": "执行 %1", "args1": [{"type": "input_statement", "name": "DO"}], "previousStatement": null, "nextStatement": null, "style": "loop_blocks", "tooltip": "%{BKY_CONTROLS_REPEAT_TOOLTIP}", "helpUrl": "%{BKY_CONTROLS_REPEAT_HELPURL}"}, {"type": "controls_repeat", "message0": "重复 %1 次", "args0": [{"type": "field_number", "name": "TIMES", "value": 10, "min": 0, "precision": 1}], "message1": "执行 %1", "args1": [{"type": "input_statement", "name": "DO"}], "previousStatement": null, "nextStatement": null, "style": "loop_blocks", "tooltip": "%{BKY_CONTROLS_REPEAT_TOOLTIP}", "helpUrl": "%{BKY_CONTROLS_REPEAT_HELPURL}"}, {"type": "controls_whileUntil", "message0": "%1 %2", "args0": [{"type": "field_dropdown", "name": "MODE", "options": [["当条件满足时重复", "WHILE"], ["重复直到条件满足", "UNTIL"]]}, {"type": "input_value", "name": "BOOL", "check": ["Boolean", "Number"]}], "message1": "执行 %1", "args1": [{"type": "input_statement", "name": "DO"}], "previousStatement": null, "nextStatement": null, "style": "loop_blocks", "helpUrl": "%{BKY_CONTROLS_WHILEUNTIL_HELPURL}", "extensions": ["controls_whileUntil_tooltip"]}, {"type": "controls_for", "message0": "变量 %1 从 %2 到 %3 每次增加 %4", "args0": [{"type": "field_variable", "name": "VAR", "variable": null, "variableTypes": ["int"], "defaultType": "int"}, {"type": "input_value", "name": "FROM", "check": "Number", "align": "RIGHT"}, {"type": "input_value", "name": "TO", "check": "Number", "align": "RIGHT"}, {"type": "input_value", "name": "BY", "check": "Number", "align": "RIGHT"}], "message1": "运行 %1", "args1": [{"type": "input_statement", "name": "DO"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "style": "loop_blocks", "helpUrl": "%{BKY_CONTROLS_FOR_HELPURL}", "extensions": ["contextMenu_newGetVariableBlock", "controls_for_tooltip"]}, {"type": "controls_flow_statements", "message0": "%1", "args0": [{"type": "field_dropdown", "name": "FLOW", "options": [["跳出循环", "BREAK"], ["继续下一轮循环", "CONTINUE"]]}], "previousStatement": null, "style": "loop_blocks", "helpUrl": "%{BKY_CONTROLS_FLOW_STATEMENTS_HELPURL}", "suppressPrefixSuffix": true, "extensions": []}, {"type": "controls_whileForever", "message0": "🔁 永远循环 %1", "args0": [{"type": "input_statement", "name": "DO"}], "previousStatement": null, "nextStatement": null, "style": "loop_blocks", "helpUrl": "%{BKY_CONTROLS_WHILEUNTIL_HELPURL}", "extensions": ["controls_whileUntil_tooltip"]}]