{"toolbox_name": "텍스트", "string_add_string": {"message0": "문자열(%1) + 문자열(%2)"}, "string_charAt": {"message0": "%1 의 %2 번째 문자"}, "string_length": {"message0": "%1 의 문자 수"}, "string_indexOf": {"message0": "%1 에 %2 포함되어 있습니까?"}, "string_substring": {"message0": "%1에서 %2 %3번째 문자부터 %4 %5번째 문자까지 가져오기", "args0": [null, {"options": [["번째", "0"], ["뒤에서부터 번째", "1"]]}, null, {"options": [["번째", "0"], ["뒤에서부터 번째", "1"]]}, null]}, "string_find_str": {"message0": "%2 에서 %1 을 %3 위치에서 찾기", "args0": [null, null, {"options": [["처음", "indexOf"], ["마지막", "lastIndexOf"]]}]}, "string_to": {"message0": "문자열 %1 를 %2 로 변환", "args0": [null, {"options": [["정수", "toInt"], ["소수", "toFloat"]]}]}, "number_to": {"message0": "숫자 %1 를 ASCII 문자열로 변환"}, "toascii": {"message0": "문자 %1 를 ASCII 값으로 변환"}, "number_to_string": {"message0": "숫자 %1 를 문자열로 변환"}, "text": {"message0": "%1"}, "text_join": {"message0": ""}, "text_create_join_container": {"message0": "%{BKY_TEXT_CREATE_JOIN_TITLE_JOIN} %1 %2"}, "text_create_join_item": {"message0": "%{BKY_TEXT_CREATE_JOIN_ITEM_TITLE_ITEM}"}, "text_append": {"message0": "%1 뒤에 텍스트 %2 추가"}, "text_length": {"message0": "%1 의 길이"}, "text_isEmpty": {"message0": "%1 이 비어있습니까?"}, "text_indexOf": {"message0": "텍스트 %1 에서 %2 %3", "args0": [null, {"options": [["첫 번째 텍스트 발생 찾기", "FIRST"], ["마지막 텍스트 발생 찾기", "LAST"]]}, null]}, "text_charAt": {"message0": "텍스트 %1 에서 %2", "args0": [null, {"options": [["첫 번째 문자에서 # 문자 가져오기", "FROM_START"], ["마지막에서 # 문자까지 가져오기", "FROM_END"], ["첫 번째 문자 얻기", "FIRST"], ["마지막 문자 얻기", "LAST"], ["랜덤 문자 얻기", "RANDOM"]]}]}, "tt_getSubstring": {"message0": "텍스트 %1에서 부분 문자열 가져오기", "message1": "%1 %2", "args1": [{"options": [["#번째 문자부터", "FROM_START"], ["뒤에서 #번째 문자부터", "FROM_END"], ["첫 번째 문자부터", "FIRST"]]}, null], "message2": "%1 %2", "args2": [{"options": [["#번째 문자까지", "FROM_START"], ["뒤에서 #번째 문자까지", "FROM_END"], ["마지막 문자까지", "LAST"]]}, null]}, "text_changeCase": {"message0": "%1 %2", "args0": [{"options": [["대문자로 변환", "UPPERCASE"], ["소문자로 변환", "LOWERCASE"], ["제목 스타일로 변환", "TITLECASE"]]}, null]}, "text_trim": {"message0": "%1 의 %2", "args0": [{"options": [["양쪽 끝의 공백 제거", "BOTH"], ["왼쪽 끝의 공백 제거", "LEFT"], ["오른쪽 끝의 공백 제거", "RIGHT"]]}, null]}, "text_count": {"message0": "%2 에서 %1 의 발생 횟수 세기"}, "text_replace": {"message0": "%1 에 있는 %2 를 %3 (으)로 교체"}, "text_reverse": {"message0": "텍스트 %1 뒤집기"}}