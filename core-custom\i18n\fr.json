{"toolbox_name": "Code personnalisé", "custom_code": {"message0": "Code personnalisé %1"}, "custom_macro": {"message0": "Définir la macro %1 comme %2"}, "custom_library": {"message0": "Inclure la bibliothèque %1"}, "custom_variable": {"message0": "Définir la variable type %1 nom %2 valeur initiale %3", "args0": [{"options": [["<PERSON><PERSON>", "int"], ["<PERSON><PERSON> long", "long*"], ["Flottant", "float"], ["Double précision", "double"], ["Entier non signé", "unsigned char"], ["Long non signé", "unsigned char"], ["Booléen", "bool"], ["<PERSON><PERSON><PERSON>", "char"], ["<PERSON><PERSON>ne de caractères", "string"]]}, null, null]}, "custom_function": {"message0": "Définir la fonction %1 %2 type de retour %3 liste des paramètres %4 %5 corps de la fonction %6", "args0": [null, null, {"options": [["<PERSON><PERSON>", "int"], ["<PERSON><PERSON> long", "long*"], ["Flottant", "float"], ["Double précision", "double"], ["Entier non signé", "unsigned char"], ["Long non signé", "unsigned char"], ["Booléen", "bool"], ["<PERSON><PERSON><PERSON>", "char"], ["<PERSON><PERSON>ne de caractères", "string"]]}, null, null, null]}}