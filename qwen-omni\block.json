[{"type": "qwen_omni_config", "message0": "配置通义千问API %1 API Key %2 %3 Base URL %4", "args0": [{"type": "input_dummy"}, {"type": "input_value", "name": "API_KEY", "check": "String"}, {"type": "input_dummy"}, {"type": "input_value", "name": "BASE_URL", "check": "String"}], "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "tooltip": "配置通义千问API的访问密钥和基础URL", "helpUrl": ""}, {"type": "qwen_omni_chat", "message0": "通义千问对话 %1 用户消息 %2 %3 模型 %4", "args0": [{"type": "input_dummy"}, {"type": "input_value", "name": "MESSAGE", "check": "String"}, {"type": "input_dummy"}, {"type": "field_dropdown", "name": "MODEL", "options": [["qwen-turbo", "qwen-turbo"], ["qwen-plus", "qwen-plus"], ["qwen-max", "qwen-max"], ["qwen-long", "qwen-long"], ["qwen-omni-turbo", "qwen-omni-turbo"]]}], "output": "String", "colour": "#FF6B35", "tooltip": "发送消息到通义千问并获取回复", "helpUrl": ""}, {"type": "qwen_omni_chat_with_history", "message0": "通义千问多轮对话 %1 用户消息 %2 %3 模型 %4", "args0": [{"type": "input_dummy"}, {"type": "input_value", "name": "MESSAGE", "check": "String"}, {"type": "input_dummy"}, {"type": "field_dropdown", "name": "MODEL", "options": [["qwen-turbo", "qwen-turbo"], ["qwen-plus", "qwen-plus"], ["qwen-max", "qwen-max"], ["qwen-long", "qwen-long"], ["qwen-omni-turbo", "qwen-omni-turbo"]]}], "output": "String", "colour": "#FF6B35", "tooltip": "支持多轮对话的通义千问（简化版本）", "helpUrl": ""}, {"type": "qwen_omni_clear_history", "message0": "清空通义千问对话历史", "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "tooltip": "清空保存的对话历史记录", "helpUrl": ""}, {"type": "qwen_omni_set_system_prompt", "message0": "设置通义千问系统提示词 %1 系统提示词 %2", "args0": [{"type": "input_dummy"}, {"type": "input_value", "name": "SYSTEM_PROMPT", "check": "String"}], "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "tooltip": "设置AI助手的角色和行为规则", "helpUrl": ""}, {"type": "qwen_omni_get_response_status", "message0": "获取通义千问响应状态", "output": "Boolean", "colour": "#FF6B35", "tooltip": "检查最后一次API调用是否成功", "helpUrl": ""}, {"type": "qwen_omni_get_error_message", "message0": "获取通义千问错误信息", "output": "String", "colour": "#FF6B35", "tooltip": "获取最后一次API调用的错误信息", "helpUrl": ""}]