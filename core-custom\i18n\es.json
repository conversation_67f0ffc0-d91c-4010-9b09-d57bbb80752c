{"toolbox_name": "<PERSON><PERSON><PERSON>", "custom_code": {"message0": "Código personalizado %1"}, "custom_macro": {"message0": "Definir macro %1 como %2"}, "custom_library": {"message0": "Incluir biblioteca %1"}, "custom_variable": {"message0": "Definir variable Tipo %1 Nombre %2 Valor inicial %3", "args0": [{"options": [["<PERSON><PERSON>", "int"], ["<PERSON><PERSON> largo", "long*"], ["Flotante", "float"], ["Doble precisión", "double"], ["<PERSON>tero sin signo", "unsigned char"], ["Entero largo sin signo", "unsigned char"], ["<PERSON><PERSON><PERSON>", "bool"], ["<PERSON><PERSON><PERSON>", "char"], ["Cadena", "string"]]}, null, null]}, "custom_function": {"message0": "Definir función %1 %2 Tipo de retorno %3 Lista de parámetros %4 %5 Cuerpo de la función %6", "args0": [null, null, {"options": [["<PERSON><PERSON>", "int"], ["<PERSON><PERSON> largo", "long*"], ["Flotante", "float"], ["Doble precisión", "double"], ["<PERSON>tero sin signo", "unsigned char"], ["Entero largo sin signo", "unsigned char"], ["<PERSON><PERSON><PERSON>", "bool"], ["<PERSON><PERSON><PERSON>", "char"], ["Cadena", "string"]]}, null, null, null]}}