[{"type": "r4_io_adc_resolution", "message0": "设置ADC分辨率 %1", "args0": [{"type": "field_dropdown", "name": "RESOLUTION", "options": [["10位", "10"], ["12位", "12"], ["14位", "14"]]}], "previousStatement": null, "nextStatement": null, "colour": "#FF9800"}, {"type": "r4_io_dac_init", "message0": "初始化DAC为 %1 频率 %2 Hz", "args0": [{"type": "field_dropdown", "name": "CHANNEL", "options": [["正弦波", "sine"], ["方波", "square"], ["锯齿波", "saw"]]}, {"type": "input_value", "name": "FREQUENCY", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#FF9800"}, {"type": "r4_io_dac_set_frequency", "message0": "设置DAC频率为 %1 Hz", "args0": [{"type": "input_value", "name": "FREQUENCY", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#FF9800"}, {"type": "r4_io_dac_set_amplitude", "message0": "设置DAC幅度为 %1", "args0": [{"type": "input_value", "name": "AMPLITUDE", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#FF9800", "tooltip": "设置DAC输出幅度，范围为0-1"}, {"type": "r4_io_dac_set_offset", "message0": "设置DAC偏移为 %1", "args0": [{"type": "input_value", "name": "OFFSET", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#FF9800"}, {"type": "r4_io_dac_start", "message0": "启动DAC", "previousStatement": null, "nextStatement": null, "colour": "#FF9800"}, {"type": "r4_io_dac_stop", "message0": "停止DAC", "previousStatement": null, "nextStatement": null, "colour": "#FF9800"}]