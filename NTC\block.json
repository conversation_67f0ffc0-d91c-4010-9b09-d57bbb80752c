[{"type": "ntc_init", "message0": "NTC热敏温度传感器连接到引脚%1参考电阻%2Ω 标称电阻%3Ω 标称温度%4°C B值%5", "args0": [{"type": "field_dropdown", "name": "PIN", "options": "${board.analogPins}"}, {"type": "field_number", "name": "REF_RESISTANCE", "value": 10000, "min": 1, "max": 1000000, "precision": 1}, {"type": "field_number", "name": "NOMINAL_RESISTANCE", "value": 10000, "min": 1, "max": 1000000, "precision": 1}, {"type": "field_number", "name": "NOMINAL_TEMP", "value": 25, "min": -50, "max": 200, "precision": 1}, {"type": "field_number", "name": "B_VALUE", "value": 3950, "min": 1000, "max": 10000, "precision": 1}], "previousStatement": null, "nextStatement": null, "colour": "#FF6600", "inputsInline": false, "tooltip": "初始化NTC热敏温度传感器，设置引脚和参数"}, {"type": "ntc_read_celsius", "message0": "从引脚%1读取NTC摄氏温度", "args0": [{"type": "field_dropdown", "name": "PIN", "options": "${board.analogPins}"}], "output": "Number", "colour": "#FF6600", "inputsInline": true, "tooltip": "读取NTC传感器的摄氏温度值"}, {"type": "ntc_read_fahrenheit", "message0": "从引脚%1读取NTC华氏温度", "args0": [{"type": "field_dropdown", "name": "PIN", "options": "${board.analogPins}"}], "output": "Number", "colour": "#FF6600", "inputsInline": true, "tooltip": "读取NTC传感器的华氏温度值"}, {"type": "ntc_read_kelvin", "message0": "从引脚%1读取NTC开尔文温度", "args0": [{"type": "field_dropdown", "name": "PIN", "options": "${board.analogPins}"}], "output": "Number", "colour": "#FF6600", "inputsInline": true, "tooltip": "读取NTC传感器的开尔文温度值"}]