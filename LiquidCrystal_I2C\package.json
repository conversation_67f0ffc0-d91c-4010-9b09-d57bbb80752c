{"name": "@aily-project/lib-liquidcrystal_i2c", "nickname": "LCD1602 I2C驱动库", "author": "aily Project", "bundleDependencies": false, "compatibility": {"core": [], "voltage": [3.3, 5]}, "dependencies": {}, "deprecated": false, "description": "LCD1602 I2C显示屏控制支持库，支持Arduino UNO、MEGA、ESP8266、ESP32等开发板", "devDependencies": {}, "keywords": ["aily", "blockly", "lcd", "lcd1602", "lcd2004", "I2C", "显示屏", "液晶显示"], "scripts": {}, "version": "1.0.0", "tested": true, "tester": "mango-0616"}