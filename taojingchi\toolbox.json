{"kind": "category", "name": "淘晶驰串口屏", "colour": "#AA278D", "icon": "iconfont icon-oled12864", "contents": [{"kind": "block", "type": "taojingchi_init", "inputs": {"RXPIN": {"shadow": {"type": "math_number", "fields": {"NUM": 2}}}, "TXPIN": {"shadow": {"type": "math_number", "fields": {"NUM": 3}}}}}, {"kind": "block", "type": "taojingchi_backlight", "inputs": {"BRIGHTNESS": {"shadow": {"type": "math_number", "fields": {"NUM": 100}}}}}, {"kind": "block", "type": "taojingchi_display_page", "inputs": {"PAGE": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}}}, {"kind": "block", "type": "taojingchi_set_var", "inputs": {"VALUE": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}}}, {"kind": "block", "type": "taojingchi_display_image", "inputs": {"PAGE": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}, "IMG": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}, "ID": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}}}, {"kind": "block", "type": "taojing<PERSON>_send_command", "inputs": {"COMMAND": {"shadow": {"type": "text", "fields": {"TEXT": "CMD"}}}}}, {"kind": "block", "type": "taojingchi_send_data", "inputs": {"COMMAND": {"shadow": {"type": "text", "fields": {"TEXT": "CMD"}}}, "VALUE": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}}}]}