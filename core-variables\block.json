[{"inputsInline": true, "type": "variable_define", "message0": "声明 %1 为 %2 并赋值 %3", "args0": [{"type": "field_input", "name": "VAR", "text": "%{BKY_VARIABLES_CURRENT_NAME}"}, {"type": "field_dropdown", "name": "TYPE", "options": [["整型", "int"], ["长整型", "long"], ["浮点型", "float"], ["双精度浮点型", "double"], ["无符号整型", "unsigned int"], ["无符号长整型", "unsigned long"], ["布尔型", "bool"], ["字符型", "char"], ["字符串型", "string"]]}, {"type": "input_value", "name": "VALUE"}], "previousStatement": null, "nextStatement": null, "colour": "#ff8c1a", "toolbox": {"show": true, "inputs": {"VALUE": {"block": {"type": "math_number", "fields": {"NUM": 0}}}}}}, {"type": "variables_get", "message0": "%1", "args0": [{"type": "field_variable", "name": "VAR", "variable": "defaultVarName"}], "output": null, "style": "variable_blocks", "helpUrl": "%{BKY_VARIABLES_GET_HELPURL}", "tooltip": "%{BKY_VARIABLES_GET_TOOLTIP}", "extensions": ["contextMenu_variableSetterGetter"]}, {"type": "variables_set", "message0": "赋值 %1 为 %2", "args0": [{"type": "field_variable", "name": "VAR", "variable": "%{BKY_VARIABLES_CURRENT_NAME}"}, {"type": "input_value", "name": "VALUE"}], "previousStatement": null, "nextStatement": null, "style": "variable_blocks", "tooltip": "%{BKY_VARIABLES_SET_TOOLTIP}", "helpUrl": "%{BKY_VARIABLES_SET_HELPURL}", "extensions": ["contextMenu_variableSetterGetter"]}, {"type": "type_cast", "message0": "将 %1 强制转换为 %2", "args0": [{"type": "input_value", "name": "VALUE"}, {"type": "field_dropdown", "name": "TYPE", "options": [["整型 (int)", "int"], ["长整型 (long)", "long"], ["浮点型 (float)", "float"], ["双精度浮点型 (double)", "double"], ["无符号整型 (unsigned int)", "unsigned int"], ["无符号长整型 (unsigned long)", "unsigned long"], ["布尔型 (bool)", "bool"], ["字符型 (char)", "char"], ["字节型 (byte)", "byte"], ["字符串型 (String)", "String"]]}], "output": null, "style": "variable_blocks", "tooltip": "将一个值强制转换为指定的数据类型。例如：(int)3.14 会将浮点数转换为整数 3", "helpUrl": "https://www.arduino.cc/reference/en/language/variables/data-types/", "inputsInline": true, "extensions": ["contextMenu_variableSetterGetter"]}]