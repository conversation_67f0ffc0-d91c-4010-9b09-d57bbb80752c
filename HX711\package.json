{"name": "@aily-project/lib-hx711", "nickname": "HX711称重传感器库", "author": "bogde", "description": "用于控制HX711称重传感器模块，支持校准、去皮和读取重量功能", "version": "0.5.2", "url": "https://github.com/bogde/HX711", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "hx711", "weight", "scale", "load cell", "weight sensor", "电子秤", "称重"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "K2L"}