{"name": "@aily-project/lib-dht", "nickname": "DHT温湿度传感器", "author": "adafruit", "description": "DHT11/DHT22(AM2302)/DHT21(AM2301)温湿度传感器库，支持读取温度和湿度数据", "version": "1.0.0", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "sensor", "temperature", "humidity", "DHT11", "DHT22", "DHT21", "AM2302", "AM2301", "温度", "湿度", "传感器", "dht_init", "dht_read_temperature", "dht_read_humidity"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "奈何col"}