{"toolbox_name": "LCD1602 I2C", "lcd_i2c_init": {"message0": "Inicializar pantalla LCD I2C dirección %1 columnas %2 filas %3", "args0": [[["0x27", "0x27"], ["0x26", "0x26"], ["0x25", "0x25"], ["0x24", "0x24"], ["0x23", "0x23"], ["0x22", "0x22"], ["0x21", "0x21"], ["0x20", "0x20"]], null, null]}, "lcd_i2c_clear": {"message0": "Limpiar pantalla LCD"}, "lcd_i2c_set_cursor": {"message0": "Mover cursor LCD a columna %1 fila %2"}, "lcd_i2c_print": {"message0": "Mostrar en LCD %1"}, "lcd_i2c_print_position": {"message0": "Mostrar en LCD en columna %1 fila %2 %3"}, "lcd_i2c_backlight_on": {"message0": "Encender retroiluminación LCD"}, "lcd_i2c_backlight_off": {"message0": "Apagar retroiluminación LCD"}, "lcd_i2c_custom_char": {"message0": "Carácter personalizado %1 número %2", "args0": [null, ["0", "0"], ["1", "1"], ["2", "2"], ["3", "3"], ["4", "4"], ["5", "5"], ["6", "6"], ["7", "7"]]}}