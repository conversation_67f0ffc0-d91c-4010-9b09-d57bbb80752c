{"toolbox_name": "LCD1602 I2C", "lcd_i2c_init": {"message0": "LCD I2C 디스플레이 초기화 주소 %1 열 수 %2 행 수 %3", "args0": [[["0x27", "0x27"], ["0x26", "0x26"], ["0x25", "0x25"], ["0x24", "0x24"], ["0x23", "0x23"], ["0x22", "0x22"], ["0x21", "0x21"], ["0x20", "0x20"]], null, null]}, "lcd_i2c_clear": {"message0": "LCD 디스플레이 지우기"}, "lcd_i2c_set_cursor": {"message0": "LCD 커서를 열 %1 행 %2로 설정"}, "lcd_i2c_print": {"message0": "LCD에 출력 %1"}, "lcd_i2c_print_position": {"message0": "LCD의 열 %1 행 %2에 %3 출력"}, "lcd_i2c_backlight_on": {"message0": "LCD 백라이트 켜기"}, "lcd_i2c_backlight_off": {"message0": "LCD 백라이트 끄기"}, "lcd_i2c_custom_char": {"message0": "사용자 정의 문자 %1 번호 %2", "args0": [null, ["0", "0"], ["1", "1"], ["2", "2"], ["3", "3"], ["4", "4"], ["5", "5"], ["6", "6"], ["7", "7"]]}}