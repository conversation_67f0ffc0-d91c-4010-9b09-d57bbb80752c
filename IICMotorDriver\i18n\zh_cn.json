{"toolbox_name": "IIC电机驱动", "iicmd_init": {"message0": "电机驱动初始化"}, "iicmd_dirinit": {"message0": "电机参考方向初始化 电机1%1 电机2%2 电机3%3 电机4%4", "args0": [{"options": [["正向", "DIRP"], ["反向", "DIRN"]]}, {"options": [["正向", "DIRP"], ["反向", "DIRN"]]}, {"options": [["正向", "DIRP"], ["反向", "DIRN"]]}, {"options": [["正向", "DIRP"], ["反向", "DIRN"]]}]}, "iicmd_stop": {"message0": "设置 %1 停止", "args0": [{"options": [["电机1", "M1"], ["电机2", "M2"], ["电机3", "M3"], ["电机4", "M4"], ["全部电机", "MALL"]]}]}, "iicmd_runone": {"message0": "设置电机 %1 转速为%2", "args0": [{"options": [["电机1", "M1"], ["电机2", "M2"], ["电机3", "M3"], ["电机4", "M4"]]}, null]}, "iicmd_runall": {"message0": "设置全部电机转速 %1", "args0": [null]}, "iicmd_runall2": {"message0": "设置全部电机速度 电机1%1 电机2%2 电机3%3 电机4%4", "args0": [null, null, null, null]}, "iicmd_digitout": {"message0": "控制端口%1 输出 %2", "args0": [{"options": [["端口1", "S1"], ["端口2", "S2"], ["端口3", "S3"], ["端口4", "S4"]]}, {"options": [["高电平", "HIGH"], ["低电平", "LOW"]]}]}, "iicmd_servo": {"message0": "舵机端口%1 转动到(0-180) %2°", "args0": [{"options": [["端口1", "S1"], ["端口2", "S2"], ["端口3", "S3"], ["端口4", "S4"]]}, null]}}