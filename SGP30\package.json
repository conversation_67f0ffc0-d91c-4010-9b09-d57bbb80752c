{"name": "@aily-project/lib-sgp30", "nickname": "SGP30空气质量传感器", "author": "aily Project", "description": "SGP30气体/空气质量感器支持库，支持测量TVOC和eCO2，适用于Arduino、ESP32等开发板", "version": "1.0.0", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp32:esp32c3", "esp32:esp32s3"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "sgp30", "air quality", "gas sensor", "tvoc", "eco2", "空气质量", "二氧化碳", "挥发性有机化合物"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "openjumper"}