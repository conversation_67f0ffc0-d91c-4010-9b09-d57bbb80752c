[{"type": "lcd_init", "message0": "初始化LCD显示器 列数%1 行数%2 引脚配置RS %3 EN %4 D4 %5 D5 %6 D6 %7 D7 %8 背光 %9", "args0": [{"type": "field_number", "name": "COLS", "value": 16}, {"type": "field_number", "name": "ROWS", "value": 2}, {"type": "field_number", "name": "RS_PIN", "value": 12}, {"type": "field_number", "name": "E_PIN", "value": 11}, {"type": "field_number", "name": "D4_PIN", "value": 5}, {"type": "field_number", "name": "D5_PIN", "value": 4}, {"type": "field_number", "name": "D6_PIN", "value": 3}, {"type": "field_number", "name": "D7_PIN", "value": 2}, {"type": "field_number", "name": "LIGHT_PIN", "value": 6}], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"inputsInline": true, "message0": "清空LCD显示", "type": "lcd_clear", "args0": [], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"inputsInline": true, "message0": "设置LCD光标到列%1 行%2", "type": "lcd_set_cursor", "args0": [{"type": "input_value", "name": "COL", "value": 0}, {"type": "input_value", "name": "ROW", "value": 0}], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"inputsInline": true, "message0": "LCD输出 %1", "type": "lcd_print", "args0": [{"type": "input_value", "name": "TEXT", "value": "\"\""}], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"inputsInline": true, "message0": "LCD在列%1 行%2 输出%3", "type": "lcd_print_position", "args0": [{"type": "input_value", "name": "COL", "value": 0}, {"type": "input_value", "name": "ROW", "value": 0}, {"type": "input_value", "name": "TEXT", "value": "\"\""}], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"inputsInline": true, "message0": "打开LCD背光", "type": "lcd_backlight_on", "args0": [], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"inputsInline": true, "message0": "关闭LCD背光", "type": "lcd_backlight_off", "args0": [], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"inputsInline": true, "message0": "自定义字符 %1 编号 %2", "type": "lcd_custom_char", "args0": [{"type": "field_bitmap", "name": "CUSTOM_CHAR", "width": 5, "height": 8}, {"type": "field_dropdown", "name": "CHAR_INDEX", "options": [["0", "0"], ["1", "1"], ["2", "2"], ["3", "3"], ["4", "4"], ["5", "5"], ["6", "6"], ["7", "7"]]}], "output": "Number", "colour": "#4CAF66"}]