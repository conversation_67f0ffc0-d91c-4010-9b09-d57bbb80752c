{"kind": "category", "name": "LIS3DHTR", "contents": [{"kind": "block", "type": "lis3dhtr_begin"}, {"kind": "block", "type": "lis3dhtr_set_full_scale_range"}, {"kind": "block", "type": "lis3dhtr_open_temp"}, {"kind": "block", "type": "lis3dhtr_close_temp"}, {"kind": "block", "type": "lis3dhtr_get_temperature"}, {"kind": "block", "type": "lis3dhtr_get_acceleration_x"}, {"kind": "block", "type": "lis3dhtr_get_acceleration_y"}, {"kind": "block", "type": "lis3dhtr_get_acceleration_z"}]}