{"toolbox_name": "I/O Pins", "io_pinmode": {"message0": "Set pin %1 mode to %2"}, "io_digitalwrite": {"message0": "Output digital signal %2 on pin %1"}, "io_digitalread": {"message0": "Read digital signal from pin %1"}, "io_analogwrite": {"message0": "Output PWM signal %2 on pin %1"}, "io_analogread": {"message0": "Read analog signal from pin %1"}, "io_pin_digi": {"message0": "Digital pin %1"}, "io_pin_adc": {"message0": "Analog pin %1"}, "io_pin_pwm": {"message0": "PWM pin %1"}, "io_mode": {"message0": "Pin mode %1"}, "io_state": {"message0": "%1 level"}}