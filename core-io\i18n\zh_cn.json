{"toolbox_name": "I/O引脚", "io_pinmode": {"message0": "引脚 %1 模式设置为 %2"}, "io_digitalwrite": {"message0": "引脚 %1输出数字信号 %2"}, "io_digitalread": {"message0": "读取引脚 %1 的数字信号"}, "io_analogwrite": {"message0": "引脚 %1 输出PWM信号%2"}, "io_analogread": {"message0": "读取引脚 %1 的模拟信号"}, "io_pin_digi": {"message0": "数字引脚%1"}, "io_pin_adc": {"message0": "模拟引脚%1"}, "io_pin_pwm": {"message0": "PWM引脚%1"}, "io_mode": {"message0": "引脚模式%1"}, "io_state": {"message0": "%1电平"}}