{"toolbox_name": "LCD1602 I2C", "lcd_i2c_init": {"message0": "LCD I2Cディスプレイを初期化 アドレス%1 列数%2 行数%3", "args0": [[["0x27", "0x27"], ["0x26", "0x26"], ["0x25", "0x25"], ["0x24", "0x24"], ["0x23", "0x23"], ["0x22", "0x22"], ["0x21", "0x21"], ["0x20", "0x20"]], null, null]}, "lcd_i2c_clear": {"message0": "LCDディスプレイをクリア"}, "lcd_i2c_set_cursor": {"message0": "LCDカーソルを列%1 行%2に設定"}, "lcd_i2c_print": {"message0": "LCDに出力 %1"}, "lcd_i2c_print_position": {"message0": "LCDの列%1 行%2に%3を出力"}, "lcd_i2c_backlight_on": {"message0": "LCDバックライトをオン"}, "lcd_i2c_backlight_off": {"message0": "LCDバックライトをオフ"}, "lcd_i2c_custom_char": {"message0": "カスタム文字 %1 番号 %2", "args0": [null, ["0", "0"], ["1", "1"], ["2", "2"], ["3", "3"], ["4", "4"], ["5", "5"], ["6", "6"], ["7", "7"]]}}