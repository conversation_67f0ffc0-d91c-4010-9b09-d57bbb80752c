{"name": "@aily-project/lib-blinker", "nickname": "点灯物联网", "author": "Diandeng Tech", "description": "Blinker物联网控制库，支持手机APP控制、智能音箱控制，使用蓝牙BLE、MQTT等通信方式，兼容多种开发板", "version": "1.0.0", "compatibility": {"core": ["esp32:esp32", "renesas_uno:unor4wifi"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "blinker", "点灯科技", "iot", "物联网", "smart home", "wifi", "mqtt", "bluetooth", "小爱同学", "天猫精灵", "小度"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "i3water", "example": "@aily-project/example-blinker-iot", "url": "https://github.com/blinker-iot/blinker-library/tree/dev_edu"}