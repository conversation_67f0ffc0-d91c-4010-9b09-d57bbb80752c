{"toolbox_name": "Broches E/S", "io_pinmode": {"message0": "Définir le mode de la broche %1 sur %2"}, "io_digitalwrite": {"message0": "Sortir le signal numérique %2 sur la broche %1"}, "io_digitalread": {"message0": "Lire le signal numérique de la broche %1"}, "io_analogwrite": {"message0": "Sortir le signal PWM %2 sur la broche %1"}, "io_analogread": {"message0": "Lire le signal analogique de la broche %1"}, "io_pin_digi": {"message0": "Broche numérique %1"}, "io_pin_adc": {"message0": "Broche analogique %1"}, "io_pin_pwm": {"message0": "Broche PWM %1"}, "io_mode": {"message0": "Mode de broche %1"}, "io_state": {"message0": "Niveau %1"}}