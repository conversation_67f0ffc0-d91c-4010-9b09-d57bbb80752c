{"name": "@aily-project/lib-ai-vox", "nickname": "AI语音交互", "author": "nulllab", "description": "Arduino版小智AI，AI Vox语音交互引擎支持库，用于ESP32系列开发板。", "version": "1.0.0", "url": "https://dcnmu33qx4fc.feishu.cn/docx/Lpy7dfEYAo04PzxJNI0ceTj5nxg", "compatibility": {"core": ["esp32:esp32", "esp32:esp32s3"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "aivox", "ai", "voice", "speech", "esp32", "i2s", "aivox_init_std", "aivox_loop", "artificial intelligence"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tester": "nulllab", "tested": true, "example": "@aily-project/example-ai-vox"}