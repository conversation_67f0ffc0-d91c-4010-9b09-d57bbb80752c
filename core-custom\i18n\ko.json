{"toolbox_name": "사용자 정의 코드", "custom_code": {"message0": "사용자 정의 코드 %1"}, "custom_macro": {"message0": "매크로 정의 %1 을(를) %2 로 설정"}, "custom_library": {"message0": "라이브러리 %1 포함"}, "custom_variable": {"message0": "변수 정의 타입 %1 이름 %2 초기값 %3", "args0": [{"options": [["정수형", "int"], ["롱 정수형", "long*"], ["실수형", "float"], ["배정도 실수형", "double"], ["부호 없는 정수형", "unsigned char"], ["부호 없는 롱 정수형", "unsigned char"], ["불린형", "bool"], ["문자형", "char"], ["문자열형", "string"]]}, null, null]}, "custom_function": {"message0": "함수 정의 %1 %2 반환 타입 %3 매개변수 목록 %4 %5 함수 본문 %6", "args0": [null, null, {"options": [["정수형", "int"], ["롱 정수형", "long*"], ["실수형", "float"], ["배정도 실수형", "double"], ["부호 없는 정수형", "unsigned char"], ["부호 없는 롱 정수형", "unsigned char"], ["불린형", "bool"], ["문자형", "char"], ["문자열형", "string"]]}, null, null, null]}}