{"toolbox_name": "시리얼", "serial_begin": {"message0": "시리얼 %1을(를) 초기화하고 보율을 %2로 설정"}, "serial_available": {"message0": "시리얼 %1 버퍼에 데이터가 있음"}, "serial_read": {"message0": "시리얼 %1 데이터를 %2로 읽기", "args0": [null, {"options": [["읽기", "read"], ["피크", "peek"], ["정수 구문 분석", "parseInt"], ["실수 구문 분석", "parseFloat"]]}]}, "serial_print": {"message0": "시리얼 %1이(가) %2 출력"}, "serial_println": {"message0": "시리얼 %1이(가) %2 출력하고 줄 바꿈"}, "serial_write": {"message0": "시리얼 %1이(가) 원시 데이터 %2 출력"}, "serial_read_string": {"message0": "시리얼 %1 문자열 읽기"}}