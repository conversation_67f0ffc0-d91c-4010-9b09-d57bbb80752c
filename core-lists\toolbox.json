{"kind": "category", "icon": "fa-light fa-layer-group", "name": "数组", "colour": "#03A9F4", "contents": [{"kind": "block", "type": "list_create_with", "inputs": {"INPUT0": {"block": {"kind": "block", "type": "list_create_with_item", "inputs": {"LENGTH": {"block": {"kind": "block", "type": "math_number", "fields": {"NUM": "3"}}}, "VALUE0": {"block": {"kind": "block", "type": "math_number", "fields": {"NUM": "1"}}}, "VALUE1": {"block": {"kind": "block", "type": "math_number", "fields": {"NUM": "2"}}}, "VALUE2": {"block": {"kind": "block", "type": "math_number", "fields": {"NUM": "3"}}}}}}}}, {"kind": "block", "type": "list_create_with", "inputs": {"INPUT0": {"block": {"kind": "block", "type": "text", "fields": {"TEXT": "Hello, <PERSON>!"}}}}}, {"kind": "block", "type": "list_create_with_item", "inputs": {"LENGTH": {"block": {"kind": "block", "type": "math_number", "fields": {"NUM": "3"}}}, "VALUE0": {"block": {"kind": "block", "type": "math_number", "fields": {"NUM": "1"}}}, "VALUE1": {"block": {"kind": "block", "type": "math_number", "fields": {"NUM": "2"}}}, "VALUE2": {"block": {"kind": "block", "type": "math_number", "fields": {"NUM": "3"}}}}}, {"kind": "block", "type": "list_get_index", "inputs": {"AT": {"block": {"kind": "block", "type": "math_number", "fields": {"NUM": "0"}}}}}, {"kind": "block", "type": "list_set_index", "inputs": {"AT": {"block": {"kind": "block", "type": "math_number", "fields": {"NUM": "0"}}}, "TO": {"block": {"kind": "block", "type": "math_number", "fields": {"NUM": "10"}}}}}, {"kind": "block", "type": "list_length"}, {"kind": "block", "type": "list2_get_value", "inputs": {"ROW": {"block": {"kind": "block", "type": "math_number", "fields": {"NUM": "0"}}}, "COL": {"block": {"kind": "block", "type": "math_number", "fields": {"NUM": "0"}}}}}, {"kind": "block", "type": "list2_set_value", "inputs": {"ROW": {"block": {"kind": "block", "type": "math_number", "fields": {"NUM": "0"}}}, "COL": {"block": {"kind": "block", "type": "math_number", "fields": {"NUM": "0"}}}, "VALUE": {"block": {"kind": "block", "type": "math_number", "fields": {"NUM": "10"}}}}}, {"kind": "block", "type": "list2_get_length"}]}