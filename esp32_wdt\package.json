{"name": "@aily-project/lib-esp32-wdt", "nickname": "ESP32看门狗", "author": "aily Project", "description": "适用于ESP32的任务看门狗定时器库，支持任务和用户级别的看门狗监控", "version": "0.0.1", "compatibility": {"core": ["esp32:esp32"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "watchdog", "wdt", "task_wdt", "esp32", "wdt_init", "wdt_add_task", "wdt_reset", "wdt_add_user", "wdt_reset_user", "wdt_delete_task", "wdt_delete_user", "wdt_deinit", "system", "monitor", "timeout"], "scripts": {}, "dependencies": {}, "devDependencies": {}}