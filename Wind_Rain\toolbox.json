{"kind": "category", "name": "风速风向雨量", "contents": [{"kind": "label", "text": "初始化风速风向传感器"}, {"kind": "block", "type": "wind_begin", "fields": {"SPEED_PIN": "35", "DIR_PIN": "34"}}, {"kind": "block", "type": "wind_begin_speed", "fields": {"SPEED_PIN": "35"}}, {"kind": "block", "type": "wind_begin_direction", "fields": {"DIR_PIN": "34"}}, {"kind": "sep"}, {"kind": "label", "text": "数据更新"}, {"kind": "block", "type": "wind_update"}, {"kind": "block", "type": "wind_update_speed"}, {"kind": "block", "type": "wind_update_direction"}, {"kind": "sep"}, {"kind": "label", "text": "数据获取"}, {"kind": "block", "type": "wind_get_speed_kmh"}, {"kind": "block", "type": "wind_get_speed_mph"}, {"kind": "block", "type": "wind_get_direction"}, {"kind": "block", "type": "wind_get_direction_string"}, {"kind": "block", "type": "wind_get_direction_adc"}, {"kind": "block", "type": "wind_is_data_ready"}, {"kind": "sep"}, {"kind": "label", "text": "雨量传感器"}, {"kind": "block", "type": "rain_init", "fields": {"PIN": "32"}}, {"kind": "block", "type": "rain_update"}, {"kind": "block", "type": "rain_get_total"}, {"kind": "block", "type": "rain_get_hour"}, {"kind": "block", "type": "rain_get_day"}, {"kind": "block", "type": "rain_get_ticks"}, {"kind": "block", "type": "rain_is_data_ready"}]}