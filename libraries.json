[{"name": "@aily-project/lib-shiftregister", "nickname": "移位寄存器驱动库", "version": "0.0.1", "description": "移位寄存器74HC595控制库，支持Arduino UNO、MEGA、ESP8266、ESP32等开发板", "author": "<PERSON><PERSON>", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "shiftregister", "74HC595", "shift_register", "移位寄存器", "io扩展"], "tested": true, "icon": ""}, {"name": "@aily-project/lib-dht", "nickname": "DHT温湿度传感器", "version": "1.0.0", "description": "DHT11/DHT22(AM2302)/DHT21(AM2301)温湿度传感器库，支持读取温度和湿度数据", "author": "adafruit", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "sensor", "temperature", "humidity", "DHT11", "DHT22", "DHT21", "AM2302", "AM2301", "温度", "湿度", "传感器", "dht_init", "dht_read_temperature", "dht_read_humidity"], "tested": true, "icon": "iconfont icon-dht22"}, {"name": "@aily-project/lib-adafruit-gfx", "nickname": "彩色显示屏", "version": "1.0.0", "description": "基于Adafruit GFX的彩色显示屏驱动库，支持ST7735、ST7789、ST7796S等常用驱动芯片。", "author": "community", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["tft", "Adafruit_GFX", "显示", "绘图", "屏幕", "LCD", "TFT"], "tested": true, "icon": "iconfont icon-oled12864"}, {"name": "@aily-project/lib-adafruit_ina219", "nickname": "INA219传感器驱动库", "version": "0.0.1", "description": "INA219传感器驱动库，适用于esp32、arduino，支持I2C通信，提供电流、总线电压、分线电压、功率数据获取功能。", "author": "<PERSON>Jumper", "compatibility": {"core": ["esp32:esp32", "esp32:esp32s3", "arduino:avr"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "INA219", "电流", "总线电压", "分线电压", "功率", "I2C", "传感器"], "tested": true, "icon": ""}, {"name": "@aily-project/lib-adafruit-vl53l0x", "nickname": "VL53L0X激光测距传感器", "version": "1.0.0", "description": "VL53L0X激光测距传感器驱动库，使用I2C通信，提供距离数据获取功能。", "author": "<PERSON>Jumper", "compatibility": {"core": ["esp32:esp32", "arduino:avr"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "VL53L0X", "激光测距", "传感器"], "tested": true, "icon": ""}, {"name": "@aily-project/lib-ags02ma", "nickname": "AGS02MA TVOC传感器", "version": "0.0.1", "description": "AGS02MA TVOC气体传感器控制库,适用于Arduino、ESP32等开发板", "author": "aily Project", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp32:esp32s3"], "voltage": [3.3, 5]}, "keywords": ["ags02ma", "TVOC传感器", "AGS02MA", "gas-sensor", "气体传感器", "空气质量", "TVOC"], "tested": true, "icon": ""}, {"name": "@aily-project/lib-ai-assistant", "nickname": "ai-assistant", "version": "0.0.1", "description": "用于小智语音识别后向各种设备通过串口发送控制命令，供主设备识别和执行相应操作,支持arduino、mega2560、esp32s3。", "author": "<PERSON>Jumper", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp32:esp32s3"], "voltage": [3.3, 5]}, "keywords": ["小智", "AI", "AI控制", "AI—Assistant", "语音识别", "语音控制", "语音助手", "智能家居", "智能设备", "智能语音", "artificial intelligence"], "tested": true, "icon": ""}, {"name": "@aily-project/lib-ai-vox", "nickname": "AI语音交互", "version": "1.0.0", "description": "Arduino版小智AI，AI Vox语音交互引擎支持库，用于ESP32系列开发板。", "author": "nulllab", "compatibility": {"core": ["esp32:esp32", "esp32:esp32s3"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "aivox", "ai", "voice", "speech", "esp32", "i2s", "aivox_init_std", "aivox_loop", "artificial intelligence"], "tested": true, "icon": "fa-light fa-message-bot", "example": "@aily-project/example-ai-vox"}, {"name": "@aily-project/lib-aily_iic", "nickname": "Aily I2C通信库", "version": "0.0.1", "description": "基于Wire库封装的I2C通信支持库，适用于Arduino UNO、MEGA、ESP8266、ESP32等开发板", "author": "aily Project", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp8266:esp8266"], "voltage": [3.3, 5]}, "keywords": ["aily", "iic", "i2c", "Wire", "i2c_begin", "i2c_write", "a<PERSON><PERSON><PERSON>"], "tested": true, "icon": "fa-light fa-network-wired"}, {"name": "@aily-project/lib-aily_servo", "nickname": "Aily 舵机控制库", "version": "0.0.1", "description": "基于Servo库封装的舵机控制支持库，适用于Arduino UNO、MEGA、UNO R4、ESP32等开发板，支持角度控制、脉宽控制等功能", "author": "aily Project", "compatibility": {"core": ["arduino:avr", "arduino:mega", "renesas_uno:unor4", "renesas_uno:unor4wifi", "esp32:esp32", "esp32:esp32s3", "esp32:esp32c3"], "voltage": [3.3, 5]}, "keywords": ["aily", "servo", "舵机", "PWM", "角度控制", "a<PERSON><PERSON><PERSON>", "esp32"], "tested": true, "icon": "iconfont icon-servo"}, {"name": "@aily-project/lib-keyboard", "nickname": "USB模拟键盘", "version": "1.0.0", "description": "将Arduino模拟成USB键盘，可以实现键盘输入、按键模拟等功能", "author": "<PERSON><PERSON><PERSON><PERSON>", "compatibility": {"core": ["esp32:esp32s3", "esp32:esp32c3", "renesas_uno:minima", "renesas_uno:unor4wifi"], "voltage": [5]}, "keywords": ["aily", "blockly", "keyboard", "usb", "hid"], "tested": true, "icon": "fa-light fa-keyboard"}, {"name": "@aily-project/lib-mouse", "nickname": "USB模拟鼠标", "version": "1.0.0", "description": "将Arduino模拟成USB鼠标，可以实现鼠标点击、移动等功能", "author": "<PERSON><PERSON><PERSON><PERSON>", "compatibility": {"core": ["esp32:esp32s3", "esp32:esp32c3", "renesas_uno:minima", "renesas_uno:unor4wifi"], "voltage": []}, "keywords": ["aily", "blockly", "mouse", "usb", "hid"], "tested": true, "icon": "fa-light fa-computer-mouse-scrollwheel"}, {"name": "@aily-project/lib-r4-led-matrix", "nickname": "R4 WiFi LED矩阵库", "version": "0.0.1", "description": "用于Arduino UNO R4 Wifi LED矩阵，支持显示文本、图案和动画", "author": "aily Project", "compatibility": {"core": ["arduino:arduino_r4_wifi"], "voltage": [3.3, 5]}, "keywords": ["led", "matrix", "display", "animation", "text"], "tested": true, "icon": "iconfont icon-a-8x8lattice"}, {"name": "@aily-project/lib-core-tone", "nickname": "ArduinoTone", "version": "0.1.0", "description": "发声函数，可用于控制无源蜂鸣器发出指定频率的声音", "author": "aily Project", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "core", "tone", "buzzer", "sound", "audio", "music", "beep"], "tested": true, "icon": "fa-light fa-volume-high"}, {"name": "@aily-project/lib-bh1750", "nickname": "BH1750光照传感器", "version": "0.0.1", "description": "BH1750数字光照强度传感器控制库，适用于Arduino、ESP32等开发板", "author": "aily Project", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp32:esp32s3"], "voltage": [3.3, 5]}, "keywords": ["bh1750", "光照传感器", "BH1750", "light"], "tested": true, "icon": ""}, {"name": "@aily-project/lib-bmp280", "nickname": "BMP280气压传感器", "version": "0.0.1", "description": "用于BMP280温度、气压与海拔高度测量的传感器库，适用于Arduino、ESP32等开发板", "author": "aily-project", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp32:esp32c3", "esp32:esp32s3"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "传感器", "气压", "温度", "高度", "BMP280"], "tested": true, "icon": ""}, {"name": "@aily-project/lib-blinker", "nickname": "点灯物联网", "version": "1.0.0", "description": "Blinker物联网控制库，支持手机APP控制、智能音箱控制，使用蓝牙BLE、MQTT等通信方式，兼容多种开发板", "author": "Diandeng Tech", "compatibility": {"core": ["esp32:esp32", "renesas_uno:unor4wifi"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "blinker", "点灯科技", "iot", "物联网", "smart home", "wifi", "mqtt", "bluetooth", "小爱同学", "天猫精灵", "小度"], "tested": true, "icon": "iconfont icon-blinker", "example": "@aily-project/example-blinker-iot"}, {"name": "@aily-project/lib-encoder", "nickname": "Arduino旋转编码器库", "version": "0.0.1", "description": "Arduino旋转编码器驱动库，支持I2C通信，提供旋转编码器数据获取功能。", "author": "<PERSON>Jumper", "compatibility": {"core": ["arduino:avr"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "<PERSON><PERSON><PERSON><PERSON>", "旋转编码器", "编码器", "传感器"], "tested": true, "icon": ""}, {"name": "@aily-project/lib-esp32-ble-keyboard", "nickname": "BLE蓝牙键盘", "version": "1.0.0", "description": "将ESP32模拟成蓝牙键盘，支持键盘输入、按键模拟、媒体控制等功能", "author": "aily Project", "compatibility": {"core": ["esp32:esp32s3", "esp32:esp32c3"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "ble", "bluetooth", "keyboard", "hid", "esp32"], "tested": true, "icon": "fa-light fa-keyboard"}, {"name": "@aily-project/lib-esp32_encoder", "nickname": "ESP32旋转编码器库", "version": "0.0.1", "description": "ESP32旋转编码器驱动库适用于esp32，支持I2C通信，提供旋转编码器数据获取功能。", "author": "<PERSON>Jumper", "compatibility": {"core": ["esp32:esp", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "ESP32Encoder", "旋转编码器", "编码器", "传感器"], "tested": true, "icon": ""}, {"name": "@aily-project/lib-esp32-i2s", "nickname": "ESP32 I2S音频库", "version": "1.0.0", "description": "支持ESP32 I2S数字麦克风录音和功放播放的音频库，包含WAV文件录制播放、音调生成、旋律播放等功能", "author": "aily Project", "compatibility": {"core": ["esp32:esp32", "esp32:esp32s3"], "voltage": [3.3]}, "keywords": ["esp32-i2s", "音频功放", "WAV文件", "音频录制", "音频播放", "音调生成", "旋律播放", "I2S", "音频处理", "音频库", "音频输入", "音频输出", "音频采集", "音频播放器", "数字麦克风", "I2S麦克风", "麦克风", "audio", "microphone", "speaker"], "tested": true, "icon": ""}, {"name": "@aily-project/lib-esp32-sd", "nickname": "ESP32 SD卡库", "version": "0.0.1", "description": "SD卡及文件系统操作库,适用于ESP32等开发板", "author": "esp32", "compatibility": {"core": ["esp32:esp32", "esp32:esp32s3"], "voltage": [3.3, 5]}, "keywords": ["sd", "SD卡", "存储"], "tested": true, "icon": ""}, {"name": "@aily-project/lib-fastled", "nickname": "RGB灯带驱动库", "version": "1.0.1", "description": "基于FastLed的GRB灯带驱动库，支持WS2812B/WS2811/NEOPIXEL等多种RGB灯带的控制，提供颜色、亮度、彩虹效果等多种预置特效。", "author": "奈何col", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "fastled", "led", "neopixel", "ws2812b", "ws2812", "ws2811", "apa102", "ws2801", "lpd8806", "rgb", "rainbow", "light", "display", "显示"], "tested": true, "icon": ""}, {"name": "@aily-project/lib-hx711", "nickname": "HX711称重传感器库", "version": "0.5.2", "description": "用于控制HX711称重传感器模块，支持校准、去皮和读取重量功能", "author": "bogde", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "hx711", "weight", "scale", "load cell", "weight sensor", "电子秤", "称重"], "tested": true, "icon": "fa-light fa-weight-scale"}, {"name": "@aily-project/lib-iicmotordriver", "nickname": "IIC电机驱动库", "version": "0.0.1", "description": "通过该库控制IIC电机驱动板，实现4路可调速电机控制、4路舵机控制", "author": "K2L", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "iic", "motordriver", "电机驱动", "电机"], "tested": true, "icon": "iconfont icon-motor"}, {"name": "@aily-project/lib-liquidcrystal", "nickname": "LCD1602显示屏", "version": "1.0.0", "description": "LCD1602显示屏控制支持库，使用4线并行通信，支持Arduino UNO、MEGA、ESP8266、ESP32等开发板", "author": "aily Project", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "lcd", "液晶显示", "lcd1602", "LiquidCrystal"], "tested": true, "icon": "iconfont icon-lcd1602"}, {"name": "@aily-project/lib-liquidcrystal_i2c", "nickname": "LCD1602 I2C驱动库", "version": "1.0.0", "description": "LCD1602 I2C显示屏控制支持库，支持Arduino UNO、MEGA、ESP8266、ESP32等开发板", "author": "aily Project", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "lcd", "lcd1602", "lcd2004", "I2C", "显示屏", "液晶显示"], "tested": true, "icon": "iconfont icon-lcd1602"}, {"name": "@aily-project/lib-lm35", "nickname": "LM35温度传感器库", "version": "0.0.1", "description": "LM35温度传感器库，提供简单易用的温度读取功能", "author": "aily Project", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "temperature", "lm35", "sensor", "analog", "lm35_setup", "lm35_read"], "tested": true, "icon": "fa-light fa-temperature-half"}, {"name": "@aily-project/lib-mp3player-gd3800", "nickname": "GD3800音频播放", "version": "0.0.1", "description": "GD3800音频播放模块驱动库，支持串口通信，可用于播放MP3等格式的音频文件。", "author": "<PERSON>Jumper", "compatibility": {"core": ["esp32:esp32", "esp32:esp32s3", "arduino:avr"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "GD3800", "模块", "mp3", "音乐"], "tested": true, "icon": "fa-light fa-music"}, {"name": "@aily-project/lib-mqtt", "nickname": "MQTT通信", "version": "0.0.1", "description": "基于PubSubClient的MQTT支持库，适用于Arduino UNO R4 WiFi、ESP32等开发板", "author": "aily Project", "compatibility": {"core": ["esp32:esp32", "renesas_uno:unor4wifi"], "voltage": [3.3, 5]}, "keywords": ["aily", "iot", "mqtt", "ethernet", "wifi", "PubSubClient", "SRAM", "network", "物联网"], "tested": true, "icon": "fa-light fa-comments"}, {"name": "@aily-project/lib-ntc", "nickname": "NTC热敏电阻", "version": "1.0.0", "description": "NTC热敏电阻库，支持Esp32等开发板，提供摄氏度、华氏度、开尔文温度读取", "author": "aily Project", "compatibility": {"core": ["esp32:esp32", "esp32:esp32s3"], "voltage": [3.3, 5]}, "keywords": ["ntc", "thermistor", "temperature", "sensor", "温度传感器", "热敏电阻"], "tested": true, "icon": "fa-light fa-temperature-three-quarters"}, {"name": "@aily-project/lib-nv170d", "nickname": "常用语音播报模块", "version": "0.0.1", "description": "通过该库控制语音播报模块，内置69种常用语", "author": "K2L", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "mp3", "voiceplayer", "语音播报"], "tested": true, "icon": ""}, {"name": "@aily-project/lib-openjumper-iicps3", "nickname": "IICPS3数据解析专用库", "version": "0.0.1", "description": "通过该库解析IICPS3手柄接收模块的数据，支持获取手柄的按键状态、摇杆位置。", "author": "<PERSON>Jumper", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "iic", "ps3", "openjumper"], "tested": true, "icon": ""}, {"name": "@aily-project/lib-tts", "nickname": "文本转语音", "version": "0.0.1", "description": "通过OpenJumper TTS库控制文本转语音模块，支持播放铃声、提示音、警告音、数字和文本。", "author": "<PERSON>Jumper", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "tts", "text-to-speech", "openjumpert", "artificial intelligence"], "tested": true, "icon": "fa-light fa-volume-high"}, {"name": "@aily-project/lib-servo", "nickname": "舵机驱动", "version": "1.0.0", "description": "舵机控制支持库，支持Arduino UNO、MEGA、ESP32等开发板", "author": "aily Project", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "servo", "servo_attach", "servo_write", "执行器"], "tested": true, "icon": "iconfont icon-servo"}, {"name": "@aily-project/lib-servo360", "nickname": "360舵机驱动", "version": "1.0.0", "description": "360舵机控制支持库，支持Arduino UNO、MEGA、ESP32等开发板", "author": "aily Project", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "servo", "servo_attach", "servo_write", "执行器"], "tested": true, "icon": "iconfont icon-servo"}, {"name": "@aily-project/lib-sgp30", "nickname": "SGP30空气质量传感器", "version": "1.0.0", "description": "SGP30气体/空气质量感器支持库，支持测量TVOC和eCO2，适用于Arduino、ESP32等开发板", "author": "aily Project", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp32:esp32c3", "esp32:esp32s3"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "sgp30", "air quality", "gas sensor", "tvoc", "eco2", "空气质量", "二氧化碳", "挥发性有机化合物"], "tested": true, "icon": ""}, {"name": "@aily-project/lib-si7021", "nickname": "Si7021温湿度传感器", "version": "1.0.0", "description": "Si7021温湿度传感器控制库，适用于Arduino、ESP32等开发板，基于Adafruit Si7021库", "author": "aily Project", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp32:esp32c3", "esp32:esp32s3"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "si7021", "temperature", "humidity", "sensor", "温度", "湿度", "传感器"], "tested": true, "icon": ""}, {"name": "@aily-project/lib-sparkfun_BMI270", "nickname": "BMI270传感器驱动库", "version": "0.0.1", "description": "BMI270传感器驱动库适用于esp32，支持I2C通信，提供加速度、陀螺仪数据获取功能。", "author": "<PERSON>Jumper", "compatibility": {"core": ["esp32:esp32", "esp32:esp32s3"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "BMI270", "加速度", "陀螺仪", "校准", "传感器"], "tested": true, "icon": ""}, {"name": "@aily-project/lib-tb6612-motor", "nickname": "TB6612直流电机驱动", "version": "0.0.2", "description": "支持TB6612双路直流电机驱动器的控制库，适用于Arduino、ESP32等开发板", "author": "aily Project", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp32:esp32s3"], "voltage": [3.3, "5-10"]}, "keywords": ["tb6612", "直流电机", "motor", "双路电机驱动"], "tested": true, "icon": ""}, {"name": "@aily-project/lib-stepper", "nickname": "步进电机驱动库", "version": "0.0.1", "description": "28BYJ-48步进电机控制库,支持Arduino UNO,ESP32等开发板", "author": "aily-project", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp32:esp32s3"], "voltage": [5]}, "keywords": ["stepper", "motor", "步进电机", "电机", "28BYJ-48", "执行器"], "tested": true, "icon": ""}, {"name": "@aily-project/lib-u8g2", "nickname": "单色显示屏", "version": "1.0.0", "description": "基于u8g2的单色显示屏驱动库，可驱动多种OLED、LCD单色显示屏，支持SSD1306、SSD1309、SH1106、SH1107、ST7920等常用驱动芯片。", "author": "奈何col", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "display", "u8g2", "oled", "lcd", "12864", "ssd1306", "sh1106", "st7920", "显示屏", "图形", "点阵显示屏"], "tested": true, "icon": "iconfont icon-oled12864"}, {"name": "@aily-project/lib-ultrasonic", "nickname": "超声波传感器", "version": "0.0.1", "description": "超声波传感器，驱动库支持SR04", "author": "aily Project", "compatibility": "", "keywords": ["aily", "blockly", "core", "sensor"], "tested": true, "icon": "iconfont icon-sr04"}, {"name": "@aily-project/lib-wind", "nickname": "风速风向雨量传感器", "version": "1.0.0", "description": "风速风向雨量传感器控制库，支持风速测量(km/h, mph)和风向检测，以及雨量测量，适用于Arduino、ESP32等开发板", "author": "aily Project", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp32:esp32c3", "esp32:esp32s3"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "wind", "windspeed", "winddirection", "sensor", "weather", "风速", "风向", "传感器", "气象", "雨量", "降雨"], "tested": true, "icon": ""}, {"name": "@aily-project/lib-adafruit-adxl345", "nickname": "ADXL345加速度传感器", "version": "1.0.0", "description": "ADXL345三轴加速度传感器库，I2C通信，可读取X/Y/Z轴加速度数据", "author": "adafruit", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "sensor", "accelerometer", "ADXL345", "acceleration", "I2C", "加速度", "传感器", "三轴", "adxl345_init", "adxl345_read_x", "adxl345_read_y", "adxl345_read_z", "adxl345_read_xyz", "adxl345_set_range", "adxl345_set_data_rate"], "tested": false, "icon": "fa-light fa-gauge"}, {"name": "@aily-project/lib-aht-sensor", "nickname": "AHT温湿度传感器", "version": "0.0.1", "description": "适用于AHT10、AHT20温湿度传感器", "author": "Adafruit", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "aht", "温度", "湿度", "传感器", "oled", "Adafruit_AHTX0", "temperature", "humidity"], "tested": false, "icon": "iconfont icon-dht22"}, {"name": "@aily-project/lib-fingerprint", "nickname": "指纹识别库", "version": "0.0.1", "description": "基于Adafruit_Fingerprint库的指纹识别支持库，支持Arduino UNO、MEGA、ESP8266、ESP32等开发板", "author": "Adafruit", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "fingerprint", "Adafruit_Fingerprint", "指纹识别"], "tested": false, "icon": "fa-light fa-fingerprint"}, {"name": "@aily-project/lib-mlx90614", "nickname": "MLX90614红外测温库", "version": "1.0.0", "description": "MLX90614红外非接触式温度传感器驱动库，可测量物体和环境温度", "author": "Adafruit", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "MLX90614", "temperature", "infrared", "sensor", "read_object_temp", "read_ambient_temp", "read_emissivity", "write_emissivity"], "tested": false, "icon": ""}, {"name": "@aily-project/lib-adafruit-sht3x", "nickname": "SHT3x温湿度传感器", "version": "1.0.0", "description": "SHT30/SHT31/SHT35温湿度传感器控制库，I2C通讯，包含温度、湿度读取及加热器控制功能", "author": "Adafruit", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "sht31", "temperature", "humidity", "sensor", "i2c", "adafruit", "sht31_init", "sht31_read_temperature", "sht31_read_humidity", "sht31_heater_control"], "tested": false, "icon": "fa-light fa-temperature-three-quarters"}, {"name": "@aily-project/lib-adafruit-sht4x", "nickname": "SHT4x温湿度传感器", "version": "1.0.0", "description": "SHT40/SHT41/SHT45温湿度传感器支持库，支持高精度温湿度测量", "author": "Adafruit", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "sht4x", "temperature", "humidity", "sensor", "i2c", "adafruit", "sht40", "sht41", "sht45"], "tested": false, "icon": "fa-light fa-temperature-three-quarters"}, {"name": "@aily-project/lib-adafruit-shtc3", "nickname": "SHTC3温湿度传感器", "version": "1.0.0", "description": "SHTC3数字温湿度传感器库，支持I2C通信，低功耗模式，适用于精确温湿度检测", "author": "Adafruit", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "sensor", "temperature", "humidity", "SHTC3", "I2C", "digital", "precision", "温度", "湿度", "传感器", "数字", "精密", "shtc3_init", "shtc3_read_temperature", "shtc3_read_humidity", "shtc3_read_both", "shtc3_is_connected", "shtc3_sleep", "shtc3_set_power_mode"], "tested": false, "icon": "fa-light fa-temperature-three-quarters"}, {"name": "@aily-project/lib-arduino_ble", "nickname": "ArduinoBLE", "version": "0.0.1", "description": "ArduinoBLE支持库，支持Arduino及ESP系列", "author": "<PERSON><PERSON><PERSON><PERSON>", "compatibility": {"core": ["arduino:avr"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "BLE", "ble_begin", "ble_scan", "ble_connect", "ble_disconnect", "ble_read_characteristic", "ble_write_characteristic"], "tested": false, "icon": "fa-light fa-bluetooth"}, {"name": "@aily-project/lib-arduino-freertos", "nickname": "Arduino freeRTOS", "version": "1.0.0", "description": "适用于Arduino UNO R4的freeRTOS支持库", "author": "aily-project", "compatibility": {"core": ["renesas_uno:minima", "renesas_uno:unor4wifi"], "voltage": [3.3, 5]}, "keywords": ["a<PERSON><PERSON><PERSON>", "freertos", "rtos", "blockly", "multitasking", "real-time"], "tested": false, "icon": "fa-light fa-crosshairs"}, {"name": "@aily-project/lib-arduino_http", "nickname": "ArduinoHTTP", "version": "1.0.0", "description": "Arduino HTTP and WebSocket库，支持WiFi连接和HTTP请求操作", "author": "Developer", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "http", "WiFi101", "ArduinoHttpClient"], "tested": false, "icon": "fa-light fa-globe"}, {"name": "@aily-project/lib-core-i2c", "nickname": "ArduinoI2C", "version": "0.0.1", "description": "I2C总线库，用于检测并输出连接设备的地址，同时报告通信错误，支持Arduino UNO、MEGA、ESP8266、ESP32等开发板", "author": "<PERSON><PERSON><PERSON><PERSON>", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "iic", "i2c", "TWI", "Wire"], "tested": false, "icon": "fa-light fa-network-wired"}, {"name": "@aily-project/lib-arduino-modbus", "nickname": "Arduino Modbus通信库", "version": "0.0.1", "description": "Arduino Modbus RTU/TCP客户端和服务器通信库，支持读写coils、寄存器等功能", "author": "<PERSON><PERSON><PERSON><PERSON>", "compatibility": {"core": ["arduino:avr", "arduino:samd", "renesas_uno:minima", "renesas_uno:unor4wifi"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "modbus", "rtu", "tcp", "rs485", "communication", "industrial", "coil", "register", "client", "server", "物联网"], "tested": false, "icon": ""}, {"name": "@aily-project/lib-r4-can", "nickname": "R4 CAN总线通信库", "version": "1.0.0", "description": "用于Arduino UNO R4的CAN总线通信库，支持CAN消息的发送和接收", "author": "<PERSON><PERSON><PERSON><PERSON>", "compatibility": {"core": ["renesas_uno:minima", "renesas_uno:unor4wifi"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "can", "communication", "bus", "can_begin", "can_read", "can_write", "can_filter"], "tested": false, "icon": ""}, {"name": "@aily-project/lib-r4-rtc", "nickname": "R4实时时钟", "version": "1.0.0", "description": "适用于Arduino UNO R4的RTC库，提供精确的时间跟踪、定时回调和时间格式化功能", "author": "<PERSON><PERSON><PERSON><PERSON>", "compatibility": {"core": ["renesas_uno:minima", "renesas_uno:unor4wifi"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "rtc", "clock", "time", "calendar", "realtime"], "tested": false, "icon": "fa-light fa-clock"}, {"name": "@aily-project/lib-r4-wdt", "nickname": "R4看门狗", "version": "1.0.0", "description": "适用于Arduino UNO R4的看门狗库", "author": "<PERSON><PERSON><PERSON><PERSON>", "compatibility": {"core": ["renesas_uno:minima", "renesas_uno:unor4wifi"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "watchdog", "wdt", "wdt_begin", "wdt_refresh", "wdt_gettimeout", "system", "reset"], "tested": false, "icon": "fa-light fa-shield-dog"}, {"name": "@aily-project/lib-wifis3", "nickname": "WiFiS3", "version": "0.0.1", "description": "Arduino UNO R4 WiFi库 WiFiS3", "author": "奈何col", "compatibility": {"core": ["renesas_uno:unor4wifi"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "wifi", "WiFiS3", "WiFiServer", "WiFiClient", "WiFiUDP", "RTC", "ping", "物联网"], "tested": false, "icon": "fa-light fa-wifi"}, {"name": "@aily-project/lib-arduino-rs485", "nickname": "RS485通信库", "version": "0.1.0", "description": "优化的RS485串行通信库，支持一键配置、自动初始化、主从通信等简化功能", "author": "<PERSON><PERSON><PERSON><PERSON>", "compatibility": {"core": ["arduino:avr", "arduino:samd", "renesas_uno:minima", "renesas_uno:unor4wifi"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "rs485", "serial", "communication", "max3157", "mkr485", "modbus", "industrial", "rs485_begin", "rs485_write", "rs485_read", "rs485_print", "rs485_receive", "rs485_transmission", "rs485_quick_setup", "rs485_simple_send", "rs485_simple_receive", "rs485_master_send", "rs485_slave_receive", "auto_init", "simplified"], "tested": false, "icon": ""}, {"name": "@aily-project/lib-core-software_serial", "nickname": "ArduinoSoftwareSerial", "version": "1.0.0", "description": "使用Arduino数字引脚模拟串口进行通信", "author": "<PERSON><PERSON><PERSON><PERSON>", "compatibility": {"core": ["arduino:avr"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "softwareserial", "serial", "communication"], "tested": false, "icon": ""}, {"name": "@aily-project/lib-bme280", "nickname": "BME280传感器库", "version": "0.0.1", "description": "用于读取BME280传感器的数据，包括温度、压力、海拔高度和湿度，并将数据输出至串行监视器", "author": "aily Project", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "BME280", "传感器", "温度", "压力", "海拔", "湿度"], "tested": false, "icon": ""}, {"name": "@aily-project/lib-dfplayer", "nickname": "DFPlayer", "version": "0.0.1", "description": "DFPlayer控制库，用于控制DFPlayer Mini模块，实现音频播放、暂停、音量调节及其它功能", "author": "dfrobot", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["dfplayer", "DFRobotDFPlayerMini", "a<PERSON><PERSON><PERSON>", "audio", "playback", "volume"], "tested": false, "icon": "fa-light fa-list-music"}, {"name": "@aily-project/lib-ds18b20", "nickname": "Dallas温度传感器", "version": "1.0.0", "description": "Dallas数字温度传感器库，支持DS18B20、DS18S20、DS1820、DS1822、DS1825、MAX31820、MAX31850等Dallas/MAXIM温度传感器", "author": "aily Project", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "sensor", "temperature", "DS18B20", "DS18S20", "DS1820", "DS1822", "DS1825", "MAX31820", "MAX31850", "Dallas", "OneWire", "温度", "传感器", "数字温度传感器", "ds18b20_init", "ds18b20_init_pin", "ds18b20_read_temperature_c", "ds18b20_read_temperature_f", "ds18b20_read_temperature_c_pin", "ds18b20_read_temperature_f_pin", "ds18b20_simple_read", "ds18b20_get_device_count", "ds18b20_get_device_count_pin", "ds18b20_read_temperature_by_index", "ds18b20_read_temperature_by_index_pin", "ds18b20_set_resolution", "ds18b20_is_parasite_power"], "tested": false, "icon": "fa-light fa-temperature-three-quarters"}, {"name": "@aily-project/lib-esp32-ble-gamepad", "nickname": "ESP32蓝牙手柄", "version": "1.0.0", "description": "ESP32蓝牙游戏手柄库，支持按键、摇杆、特殊按键等功能", "author": "aily Project", "compatibility": {"core": ["esp32:esp32c3", "esp32:esp32s3"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "esp32", "ble", "gamepad", "bluetooth", "controller", "按键", "游戏手柄", "蓝牙"], "tested": false, "icon": "fa-light fa-gamepad-modern"}, {"name": "@aily-project/lib-esp32-ble-mouse", "nickname": "BLE蓝牙鼠标", "version": "1.0.0", "description": "适用于ESP32的BLE鼠标库，支持鼠标移动、点击、滚轮等操作", "author": "aily Project", "compatibility": {"core": ["esp32:esp32c3", "esp32:esp32s3"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "esp32", "ble", "bluetooth", "mouse", "hid", "ble_mouse_init", "ble_mouse_move", "ble_mouse_click", "ble_mouse_scroll", "ble_keyboard_press", "ble_keyboard_release", "ble_keyboard_write", "ble_gamepad_press", "ble_gamepad_release", "ble_gamepad_joystick"], "tested": false, "icon": "fa-light fa-computer-mouse-scrollwheel"}, {"name": "@aily-project/lib-esp32-twai", "nickname": "ESP32 CAN总线", "version": "0.0.1", "description": "ESP32 CAN(TWAI)通信库，支持发送和接收CAN消息，适用于ESP32系列开发板", "author": "aily Project", "compatibility": {"core": ["esp32:esp32", "esp32:esp32c3", "esp32:esp32s3"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "esp32", "can", "twai", "esp32_can_init", "esp32_can_send_message", "esp32_can_receive_message", "esp32_can_configure_alerts", "esp32_can_check_alerts"], "tested": false, "icon": ""}, {"name": "@aily-project/lib-esp32-espnow", "nickname": "ESP-NOW通信", "version": "1.0.0", "description": "ESP32 ESP-NOW无线通信支持库，支持点对点通信、广播模式和网络模式", "author": "aily Project", "compatibility": {"core": ["esp32:esp32", "esp32:esp32c3", "esp32:esp32s3", "esp32:esp32c6", "esp32:esp32h2"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "esp32", "espnow", "wireless", "communication", "mesh", "broadcast", "peer-to-peer"], "tested": false, "icon": "fa-light fa-signal-stream"}, {"name": "@aily-project/lib-esp32-i2c", "nickname": "ESP32 I2C通信库", "version": "1.0.0", "description": "ESP32专用I2C通信支持库，提供主从模式通信、设备扫描等功能，支持自定义引脚配置", "author": "aily Project", "compatibility": {"core": ["esp32:esp32", "esp32:esp32c3", "esp32:esp32s2", "esp32:esp32s3", "esp32:esp32c6", "esp32:esp32h2"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "esp32", "i2c", "wire", "communication", "master", "slave", "scan", "iic", "两线通信", "esp32_i2c_begin", "esp32_i2c_scan_devices", "esp32_i2c_write_to_device", "esp32_i2c_read_from_device", "esp32_i2c_slave_begin"], "tested": false, "icon": ""}, {"name": "@aily-project/lib-esp32-spi", "nickname": "ESP32 SPI", "version": "0.0.1", "description": "基于SPI.h的SPI通信支持库，适用于Arduino UNO、MEGA、ESP8266、ESP32等开发板", "author": "esp32", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "spi", "SPIClass", "begin", "beginTransaction", "transfer", "pinSS"], "tested": false, "icon": ""}, {"name": "@aily-project/lib-esp32-usb-gamepad", "nickname": "USB模拟手柄", "version": "1.0.0", "description": "将ESP32模拟成USB游戏手柄，支持按键、摇杆、扳机和方向键控制", "author": "espressif", "compatibility": {"core": ["esp32:esp32s3", "esp32:esp32c3"], "voltage": []}, "keywords": ["aily", "blockly", "gamepad", "usb", "hid", "joystick", "controller"], "tested": false, "icon": "fa-light fa-gamepad-modern"}, {"name": "@aily-project/lib-esp32-wdt", "nickname": "ESP32看门狗", "version": "0.0.1", "description": "适用于ESP32的任务看门狗定时器库，支持任务和用户级别的看门狗监控", "author": "aily Project", "compatibility": {"core": ["esp32:esp32"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "watchdog", "wdt", "task_wdt", "esp32", "wdt_init", "wdt_add_task", "wdt_reset", "wdt_add_user", "wdt_reset_user", "wdt_delete_task", "wdt_delete_user", "wdt_deinit", "system", "monitor", "timeout"], "tested": false, "icon": "fa-light fa-shield-dog"}, {"name": "@aily-project/lib-esp32-wifi", "nickname": "ESP32 WiFi库", "version": "1.0.0", "description": "ESP32 WiFi功能支持库，包含WiFi连接、热点模式、HTTP请求", "author": "aily Project", "compatibility": {"core": ["esp32:esp32"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "esp32", "wifi", "wireless", "internet", "http", "post", "get", "json", "api", "client", "ap", "hotspot", "iot", "esp32_wifi_begin", "esp32_wifi_connected", "esp32_wifi_local_ip", "esp32_wifi_scan", "esp32_wifi_ap_mode", "esp32_wifi_http_get", "esp32_wifi_http_post"], "tested": false, "icon": "fa-light fa-wifi"}, {"name": "@aily-project/lib-esp32-wifimanager", "nickname": "WiFi管理器", "version": "1.0.0", "description": "ESP32 WiFiManager库，提供WiFi配置门户和自动连接功能", "author": "aily Project", "compatibility": {"core": ["esp32:esp32", "esp32:esp32c3", "esp32:esp32s2", "esp32:esp32s3"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "wifi", "w<PERSON><PERSON><PERSON>", "esp32", "esp8266", "autoconnect", "config", "portal", "wireless", "network"], "tested": false, "icon": "fa-light fa-router"}, {"name": "@aily-project/lib-espui", "nickname": "ESPUI网页界面库", "version": "0.0.1", "description": "基于ESPUI的ESP32/ESP8266网页界面库，支持创建按钮、滑条、开关等Web UI控件", "author": "aily Project", "compatibility": {"core": ["esp32:esp32", "esp8266:esp8266"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "espui", "esp32", "esp8266", "web", "ui", "interface", "gui", "button", "slider", "switch", "label", "text", "number", "graph", "gauge", "control", "wifi", "http", "webserver", "async", "espui_begin", "espui_label", "espui_button", "espui_switcher", "espui_slider", "espui_text", "espui_number", "espui_graph", "espui_gauge", "espui_update_control", "espui_update_label", "espui_on_event"], "tested": false, "icon": "fa-light fa-browser"}, {"name": "@aily-project/lib-gp2y1010au0f", "nickname": "粉尘传感器驱动库", "version": "0.0.1", "description": "读取GP2Y1010AU0F粉尘传感器的值，并计算和打印粉尘浓度", "author": "奈何col", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "GP2Y1010AU0F", "dust sensor", "粉尘", "<PERSON><PERSON><PERSON><PERSON>", "ESP32", "ESP8266"], "tested": false, "icon": ""}, {"name": "@aily-project/lib-ht16k33", "nickname": "HT16K33四位七段数码管", "version": "1.2.0", "description": "HT16K33四位七段数码管驱动库，支持数字、时间、日期显示，亮度和闪烁控制，包含传感器显示、数字时钟等实用组合功能", "author": "aily Project", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "HT16K33", "七段数码管", "4位数码管", "I2C", "显示", "数码管", "温度显示", "电压显示", "时钟显示", "倒计时", "传感器显示", "数字时钟", "计数器", "得分显示", "ht16k33_init", "ht16k33_simple_display", "ht16k33_display_temperature", "ht16k33_display_voltage", "ht16k33_clock_display", "ht16k33_countdown", "ht16k33_display_int", "ht16k33_display_float", "ht16k33_display_hex", "ht16k33_display_time", "ht16k33_display_date", "ht16k33_display_clear", "ht16k33_set_brightness", "ht16k33_set_blink", "ht16k33_display_on_off", "ht16k33_display_colon", "ht16k33_display_test", "ht16k33_sensor_display", "ht16k33_digital_clock", "ht16k33_score_display", "ht16k33_counter_display"], "tested": false, "icon": ""}, {"name": "@aily-project/lib-icm20948", "nickname": "ICM20948九轴传感器", "version": "1.0.0", "description": "ICM20948九轴传感器支持库，支持加速度计、陀螺仪、磁力计和AHRS姿态解算", "author": "aily Project", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp32:esp32c3", "esp32:esp32s3", "renesas_uno:minima", "renesas_uno:unor4wifi"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "icm20948", "imu", "ahrs", "accelerometer", "gyroscope", "magnetometer", "sensor"], "tested": false, "icon": "fa-light fa-plane-departure"}, {"name": "@aily-project/lib-irremote", "nickname": "红外遥控库", "version": "0.0.1", "description": "红外遥控控制支持库，支持Arduino UNO、MEGA、ESP8266、ESP32等开发板", "author": "K2L", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "ir", "红外", "ir_send_nec", "ir_receive"], "tested": false, "icon": ""}, {"name": "@aily-project/lib-keypad", "nickname": "键盘矩阵库", "version": "0.0.1", "description": "4x4矩阵键盘控制库，支持ESP32开发板", "author": "<PERSON><PERSON>", "compatibility": {"core": ["esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "keypad", "matrix", "keyboard", "esp32", "keypad_initialize", "keypad_getkey", "keypad_delete"], "tested": false, "icon": "iconfont icon-keyboard4x4"}, {"name": "@aily-project/lib-max31865", "nickname": "MAX31865温度传感器库", "version": "1.0.0", "description": "MAX31865温度传感器库，支持PT100/PT1000温度传感器，可配置2/3/4线连接方式", "author": "RAKWireless", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "max31865", "temperature", "PT100", "PT1000", "RTD", "sensor"], "tested": false, "icon": "fa-light fa-temperature-three-quarters"}, {"name": "@aily-project/lib-max7219", "nickname": "MAX7219显示驱动库", "version": "0.0.1", "description": "段位LED显示器支持库，支持Arduino UNO、MEGA、ESP8266、ESP32等开发板", "author": "aily Project", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "max7219", "display", "显示"], "tested": false, "icon": "iconfont icon-digital-tube"}, {"name": "@aily-project/lib-afmotor", "nickname": "Adafruit电机驱动库", "version": "1.0.0", "description": "Adafruit电机驱动板库，支持控制直流电机和步进电机，适用于Arduino控制板", "author": "Adafruit", "compatibility": {"core": ["arduino:avr"], "voltage": [5]}, "keywords": ["aily", "blockly", "motor", "dc motor", "stepper motor", "afmotor", "adafruit", "shield"], "tested": false, "icon": "fa-light fa-engine"}, {"name": "@aily-project/lib-mpu6050", "nickname": "MPU6050", "version": "1.0.0", "description": "MPU6050六轴(加速度计+陀螺仪)传感器库，适用于Arduino UNO R3", "author": "Adafruit", "compatibility": {"core": ["arduino:avr"], "voltage": [5]}, "keywords": ["aily", "blockly", "mpu6050", "accelerometer", "gyroscope", "temperature", "sensor", "motion", "imu"], "tested": false, "icon": "fa-light fa-drone-front"}, {"name": "@aily-project/lib-onebutton", "nickname": "OneButton按键", "version": "0.0.1", "description": "OneButton按键支持库, 支持单击、双击、长按等多种按键操作", "author": "", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "onebutton"], "tested": false, "icon": "fa-light fa-circle-dot"}, {"name": "@aily-project/lib-asr", "nickname": "语音识别", "version": "0.0.1", "description": "通过OpenJumper ASR库用于识别特定的语音识别词", "author": "<PERSON>Jumper", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "asr", "speech-recognition", "openjumper", "artificial intelligence"], "tested": false, "icon": "fa-light fa-microphone"}, {"name": "@aily-project/lib-pid", "nickname": "PID控制器库", "version": "0.1.0", "description": "优化的PID控制器库，提供快速设置、参数预设、温度控制、电机调速等功能，支持Arduino各种开发板", "author": "aily Project", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "pid", "control", "proportional", "integral", "derivative", "pid_init", "pid_compute", "pid_quick_setup", "pid_temperature_control", "pid_motor_speed_control", "pid_control_loop", "pid_set_mode", "pid_set_tunings", "pid_set_output_limits", "pid_adaptive_control", "pid_get_input", "pid_get_output", "pid_set_setpoint", "pid_set_input", "pid_is_at_setpoint", "pid_get_error", "temperature", "motor", "speed", "position", "level", "preset", "automatic", "tuning", "quick", "simplified"], "tested": false, "icon": "fa-light fa-dharmachakra"}, {"name": "@aily-project/lib-ps2x", "nickname": "PS2控制器库", "version": "1.1.0", "description": "用于读取PlayStation 2控制器输入的库，支持模拟摇杆、按钮检测、压力感应和震动反馈", "author": "<PERSON>", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp32:esp32c3", "esp32:esp32s3", "renesas_uno:minima", "renesas_uno:unor4wifi"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "PS2", "controller", "playstation", "joystick", "button", "analog", "pressure", "vibration", "rumble", "ps2x_init", "ps2x_read", "ps2x_button", "ps2x_analog"], "tested": false, "icon": "fa-light fa-gamepad-modern"}, {"name": "@aily-project/lib-qwen-omni", "nickname": "通义千问", "version": "0.0.1", "description": "阿里云通义千问Qwen-Omni大语言模型API库，支持文字对话、多轮对话、系统提示词设置等功能，适用于ESP32等支持WiFi的开发板", "author": "<PERSON><PERSON><PERSON>", "compatibility": {"core": ["esp32:esp32", "esp32:esp32c3", "esp32:esp32s3", "esp32:esp32c6", "esp32:esp32h2"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "qwen", "qwen-omni", "artificial intelligence", "通义千问", "阿里云", "大语言模型", "LLM", "AI", "人工智能", "对话", "聊天", "chat", "conversation"], "tested": false, "icon": "fa-light fa-comment-dot"}, {"name": "@aily-project/lib-rtc", "nickname": "RTC时钟", "version": "1.0.0", "description": "RTC库支持DS1307、DS3231、PCF8523、PCF8563等多种实时时钟模块，用于精确计时和日期管理", "author": "Adafruit", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "rtc", "datetime", "time", "date", "clock", "DS3231", "DS1307", "PCF8523", "PCF8563"], "tested": false, "icon": "fa-light fa-clock"}, {"name": "@aily-project/lib-lis3dhtr", "nickname": "LIS3DHTR驱动库", "version": "0.0.1", "description": "LIS3DHTR加速度传感器支持库，支持Arduino UNO、MEGA、ESP8266、ESP32等开发板", "author": "aily Project", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "LIS3DHTR", "加速度", "传感器"], "tested": false, "icon": ""}, {"name": "@aily-project/lib-taojingchi", "nickname": "淘晶驰串口屏驱动库", "version": "1.0.0", "description": "淘晶驰串口屏控制支持库，支持背光调节、页面切换、变量设置等功能", "author": "aily Project", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp32:esp32c3", "esp32:esp32s3", "esp32:esp32s2", "renesas_uno:minima", "renesas_uno:unor4wifi"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "串口屏", "显示器", "display", "screen", "serial", "hmi", "nextion", "tft"], "tested": false, "icon": "iconfont icon-oled12864"}, {"name": "@aily-project/lib-tcs34725", "nickname": "颜色识别库", "version": "0.0.1", "description": "颜色识别传感器库,支持Arduino UNO、MEGA等开发板", "author": "K2L", "compatibility": {"core": ["arduino:avr"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "tcs34725", "传感器", "颜色识别"], "tested": false, "icon": ""}, {"name": "@aily-project/lib-tm1637", "nickname": "TM1637四位数码管", "version": "1.0.0", "description": "TM1637四位七段数码管显示驱动库，支持显示数字、文本、设置亮度等功能", "author": "aily Project", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "TM1637", "display", "seven-segment", "数码管", "显示", "tm1637_init", "tm1637_print", "tm1637_clear", "tm1637_brightness", "tm1637_colon"], "tested": false, "icon": ""}, {"name": "@aily-project/lib-tm1650", "nickname": "TM1650四位数码管驱动库", "version": "0.0.1", "description": "TM1650四位数码管驱动库", "author": "K2L", "compatibility": {"core": ["arduino:avr"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "TM1650", "数码管驱动器", "显示"], "tested": false, "icon": "iconfont icon-digital-tube"}, {"name": "@aily-project/lib-tm16xx", "nickname": "TM16xx数码管显示库", "version": "1.0.0", "description": "TM16xx系列芯片驱动库，支持TM1637、TM1638、TM1640、TM1650 TM1668等多种数码管显示模块", "author": "aily Project", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "TM16xx", "TM1637", "TM1638", "TM1640", "TM1650", "TM1668", "display", "segment", "digital", "7segment", "数码管"], "tested": false, "icon": ""}, {"name": "@aily-project/lib-core-custom", "nickname": "自定义代码", "version": "1.0.0", "description": "允许插入自定义代码、宏定义、函数", "author": "aily Project", "compatibility": {"core": []}, "keywords": ["aily", "blockly", "lib", "custom", "code"], "tested": true, "icon": "fa-light fa-code"}, {"name": "@aily-project/lib-core-eeprom", "nickname": "EEPROM存储库", "version": "0.0.1", "description": "eeprom", "author": "aily Project", "compatibility": {"core": []}, "keywords": ["aily", "blockly", "core", "eeprom"], "tested": false, "icon": "fa-light fa-database"}, {"name": "@aily-project/lib-core-functions", "nickname": "自定义函数", "version": "0.0.1", "description": "核心库，通常已经集成到初始模板中", "author": "aily Project", "compatibility": {"core": []}, "keywords": ["aily", "blockly", "core", "functions"], "tested": true, "icon": "fa-light fa-function"}, {"name": "@aily-project/lib-core-interrupt", "nickname": "外部中断", "version": "0.0.1", "description": "Arduino外部中断支持库", "author": "<PERSON><PERSON><PERSON><PERSON>", "compatibility": {"core": []}, "keywords": ["aily", "blockly", "core", "interrupt"], "tested": true, "icon": "fa-light fa-flag-pennant"}, {"name": "@aily-project/lib-core-io", "nickname": "I/O控制", "version": "1.0.0", "description": "基础I/O控制，支持使用Arduino框架的开发板", "author": "aily Project", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["core", "io", "pinmode", "digitalwrite", "digitalread", "analogwrite", "analogread"], "tested": true, "icon": "fa-light fa-microchip"}, {"name": "@aily-project/lib-core-lists", "nickname": "数组", "version": "0.0.2", "description": "核心库，通常已经集成到初始模板中", "author": "aily Project", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "lib", "list", "code"], "tested": false, "icon": "fa-light fa-layer-group"}, {"name": "@aily-project/lib-core-logic", "nickname": "逻辑控制", "version": "0.0.1", "description": "核心库，通常已经集成到初始模板中", "author": "aily Project", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "core", "io"], "tested": true, "icon": "fa-light fa-split"}, {"name": "@aily-project/lib-core-loop", "nickname": "循环控制", "version": "0.0.1", "description": "核心库，通常已经集成到初始模板中", "author": "aily Project", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "core", "loop"], "tested": true, "icon": "fa-light fa-arrows-repeat"}, {"name": "@aily-project/lib-core-math", "nickname": "数学支持", "version": "0.0.1", "description": "数学相关功能支持库", "author": "aily Project", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "core", "math"], "tested": false, "icon": "fa-light fa-calculator"}, {"name": "@aily-project/lib-core-serial", "nickname": "串口通信", "version": "0.0.1", "description": "串口通信库,支持串口发送和接收", "author": "aily Project", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["core", "serial", "serialbegin", "serialprint", "serialprintln", "serialavailable", "serialread", "serialwrite"], "tested": true, "icon": "fa-light fa-arrow-right-arrow-left"}, {"name": "@aily-project/lib-core-string", "nickname": "字符串操作", "version": "0.0.1", "description": "核心库，通常已经集成到初始模板中", "author": "aily Project", "compatibility": "", "keywords": ["aily", "blockly", "string", "char", "字符串操作", "字符串连接", "字符提取", "字符串长度"], "tested": false, "icon": ""}, {"name": "@aily-project/lib-core-text", "nickname": "文本操作", "version": "0.0.1", "description": "文本相关函数", "author": "aily Project", "compatibility": {}, "keywords": ["aily", "blockly", "core", "text", "string"], "tested": false, "icon": "fa-light fa-input-text"}, {"name": "@aily-project/lib-core-time", "nickname": "时间控制", "version": "1.0.0", "description": "核心库，通常已经集成到初始模板中。包含delay、millis、micros等函数", "author": "aily Project", "compatibility": {"core": []}, "keywords": ["aily", "blockly", "core", "time"], "tested": true, "icon": "fa-light fa-timer"}, {"name": "@aily-project/lib-core-variables", "nickname": "变量", "version": "1.0.1", "description": "核心库，通常已经集成到初始模板中", "author": "", "compatibility": "", "keywords": ["aily", "blockly", "core", "variables"], "tested": true, "icon": "fa-light fa-value-absolute"}]