{"toolbox_name": "Текст", "string_add_string": {"message0": "Строка(%1) + Строка(%2)"}, "string_charAt": {"message0": "%2-й символ %1"}, "string_length": {"message0": "Количество символов в %1"}, "string_indexOf": {"message0": "Содержит ли %1 %2?"}, "string_substring": {"message0": "%1 получить символы с %2 %3 по %4 %5", "args0": [null, {"options": [["с", "0"], ["с конца", "1"]]}, null, {"options": [["по", "0"], ["по конец", "1"]]}, null]}, "string_find_str": {"message0": "Найти %1 в %2 на %3 позиции", "args0": [null, null, {"options": [["Первое", "indexOf"], ["Последнее", "lastIndexOf"]]}]}, "string_to": {"message0": "Преобразовать строку %1 в %2", "args0": [null, {"options": [["целое число", "toInt"], ["дробное число", "toFloat"]]}]}, "number_to": {"message0": "Преобразовать число %1 в строку ASCII"}, "toascii": {"message0": "Преобразовать символ %1 в значение ASCII"}, "number_to_string": {"message0": "Преобразовать число %1 в строку"}, "text": {"message0": "%1"}, "text_join": {"message0": ""}, "text_create_join_container": {"message0": "%{BKY_TEXT_CREATE_JOIN_TITLE_JOIN} %1 %2"}, "text_create_join_item": {"message0": "%{BKY_TEXT_CREATE_JOIN_ITEM_TITLE_ITEM}"}, "text_append": {"message0": "Добавить текст %2 после %1"}, "text_length": {"message0": "Длина %1"}, "text_isEmpty": {"message0": "%1 пусто"}, "text_indexOf": {"message0": "В тексте %1 %2 %3", "args0": [null, {"options": [["найти первое появление текста", "FIRST"], ["найти последнее появление текста", "LAST"]]}, null]}, "text_charAt": {"message0": "В тексте %1 %2", "args0": [null, {"options": [["Получить символ #", "FROM_START"], ["Получить символ с конца #", "FROM_END"], ["Получить первый символ", "FIRST"], ["Получить последний символ", "LAST"], ["Получить случайный символ", "RANDOM"]]}]}, "tt_getSubstring": {"message0": "получить подстроку в тексте %1", "message1": "%1 %2", "args1": [{"options": [["с # символа", "FROM_START"], ["с # с конца", "FROM_END"], ["с первого символа", "FIRST"]]}, null], "message2": "%1 %2", "args2": [{"options": [["до # символа", "FROM_START"], ["до # с конца", "FROM_END"], ["до последнего символа", "LAST"]]}, null]}, "text_changeCase": {"message0": "%1 %2", "args0": [{"options": [["изменить на заглавные", "UPPERCASE"], ["изменить на строчные", "LOWERCASE"], ["изменить на заглавные строки", "TITLECASE"]]}, null]}, "text_trim": {"message0": "Обрезка %1 %2", "args0": [{"options": [["обрезать пробелы с обоих сторон", "BOTH"], ["обрезать пробелы слева", "LEFT"], ["обрезать пробелы справа", "RIGHT"]]}, null]}, "text_count": {"message0": "Подсчитать количество появлений %1 в %2"}, "text_replace": {"message0": "Заменить %2 на %3 в %1"}, "text_reverse": {"message0": "Развернуть текст %1"}}