{"toolbox_name": "Seriell", "serial_begin": {"message0": "Seriell%1 initialisieren mit Baudrate %2"}, "serial_available": {"message0": "Seriell%1 Puffer hat Daten"}, "serial_read": {"message0": "Seriell%1 Daten %2 lesen", "args0": [null, {"options": [["lesen", "read"], ["sp<PERSON><PERSON>", "peek"], ["<PERSON><PERSON><PERSON><PERSON> analysieren", "parseInt"], ["Fließkommazahl analysieren", "parseFloat"]]}]}, "serial_print": {"message0": "Seriell%1 gibt %2 aus"}, "serial_println": {"message0": "Seriell%1 gibt %2 aus mit Zeilenumbruch"}, "serial_write": {"message0": "Seriell%1 gibt Rohdaten %2 aus"}, "serial_read_string": {"message0": "Seriell%1 Zeichenfolge lesen"}}