{"kind": "category", "name": "RS485通信", "contents": [{"kind": "block", "type": "rs485_begin"}, {"kind": "block", "type": "rs485_simple_send"}, {"kind": "block", "type": "rs485_simple_receive"}, {"kind": "block", "type": "rs485_received_data"}, {"kind": "sep"}, {"kind": "label", "text": "📡 主从通信"}, {"kind": "block", "type": "rs485_master_send"}, {"kind": "block", "type": "rs485_slave_receive"}, {"kind": "sep"}, {"kind": "label", "text": "📤 数据发送"}, {"kind": "block", "type": "rs485_begin_transmission"}, {"kind": "block", "type": "rs485_write"}, {"kind": "block", "type": "rs485_print"}, {"kind": "block", "type": "rs485_println"}, {"kind": "block", "type": "rs485_end_transmission"}, {"kind": "block", "type": "rs485_flush"}, {"kind": "sep"}, {"kind": "label", "text": "📥 数据接收"}, {"kind": "block", "type": "rs485_receive"}, {"kind": "block", "type": "rs485_no_receive"}, {"kind": "block", "type": "rs485_available"}, {"kind": "block", "type": "rs485_read"}, {"kind": "block", "type": "rs485_peek"}, {"kind": "sep"}, {"kind": "label", "text": "🔧 高级功能"}, {"kind": "block", "type": "rs485_send_break"}, {"kind": "block", "type": "rs485_send_break_microseconds"}, {"kind": "block", "type": "rs485_end"}]}