{"toolbox_name": "Texto", "string_add_string": {"message0": "Cadena(%1) + Cadena(%2)"}, "string_charAt": {"message0": "El carácter %2 de %1"}, "string_length": {"message0": "Cuenta de caracteres de %1"}, "string_indexOf": {"message0": "¿Contiene %1 a %2?"}, "string_substring": {"message0": "%1 obtener caracteres desde %2 %3 hasta %4 %5", "args0": [null, {"options": [["el", "0"], ["el último", "1"]]}, null, {"options": [["el", "0"], ["el último", "1"]]}, null]}, "string_find_str": {"message0": "Buscar %1 en %2 en la posición %3", "args0": [null, null, {"options": [["Primera", "indexOf"], ["Última", "lastIndexOf"]]}]}, "string_to": {"message0": "Convertir cadena %1 a %2", "args0": [null, {"options": [["entero", "toInt"], ["decimal", "toFloat"]]}]}, "number_to": {"message0": "Convertir número %1 a cadena ASCII"}, "toascii": {"message0": "Convertir carácter %1 a valor ASCII"}, "number_to_string": {"message0": "Convertir número %1 a cadena"}, "text": {"message0": "%1"}, "text_join": {"message0": ""}, "text_create_join_container": {"message0": "%{BKY_TEXT_CREATE_JOIN_TITLE_JOIN} %1 %2"}, "text_create_join_item": {"message0": "%{BKY_TEXT_CREATE_JOIN_ITEM_TITLE_ITEM}"}, "text_append": {"message0": "Agregar texto %2 después de %1"}, "text_length": {"message0": "Longitud de %1"}, "text_isEmpty": {"message0": "%1 está vacío"}, "text_indexOf": {"message0": "En el texto %1 %2 %3", "args0": [null, {"options": [["buscar primera aparición del texto", "FIRST"], ["buscar última aparición del texto", "LAST"]]}, null]}, "text_charAt": {"message0": "En el texto %1 %2", "args0": [null, {"options": [["obtener carácter #", "FROM_START"], ["obtener carácter desde el final #", "FROM_END"], ["obtener primer carácter", "FIRST"], ["obtener último <PERSON>", "LAST"], ["obtener carácter aleatorio", "RANDOM"]]}]}, "tt_getSubstring": {"message0": "obtener subcadena en el texto %1", "message1": "%1 %2", "args1": [{"options": [["desde el carácter #", "FROM_START"], ["desde el # desde el final", "FROM_END"], ["desde el primer carácter", "FIRST"]]}, null], "message2": "%1 %2", "args2": [{"options": [["hasta el carácter #", "FROM_START"], ["hasta el # desde el final", "FROM_END"], ["hasta el último car<PERSON>", "LAST"]]}, null]}, "text_changeCase": {"message0": "%1 %2", "args0": [{"options": [["convertir a mayúsculas", "UPPERCASE"], ["convertir a minúsculas", "LOWERCASE"], ["convertir a formato título", "TITLECASE"]]}, null]}, "text_trim": {"message0": "Recortar %1 %2", "args0": [{"options": [["recortar espacios de ambos lados", "BOTH"], ["recortar espacios del lado izquierdo", "LEFT"], ["recortar espacios del lado derecho", "RIGHT"]]}, null]}, "text_count": {"message0": "Contar ocurrencias de %1 en %2"}, "text_replace": {"message0": "Reemplazar %2 con %3 en %1"}, "text_reverse": {"message0": "Invertir texto %1"}}