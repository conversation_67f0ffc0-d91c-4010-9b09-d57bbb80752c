{"toolbox_name": "Registre à décalage", "74hc595_create": {"message0": "Initialiser 74HC595 %1 quantité%2 data:%3  clock:%4 latch:%5", "args0": [null, null, {"options": "${board.digitalPins}"}, {"options": "${board.digitalPins}"}, {"options": "${board.digitalPins}"}]}, "74hc595_set": {"message0": "74HC595 %1 définir la broche %2 à %3", "args0": [null, {"options": "${board.digitalPins}"}, {"options": [["HIGH", "HIGH"], ["LOW", "LOW"]]}]}, "74hc595_setAll": {"message0": "74HC595 %1 définir toutes les broches à %2", "args0": [null, {"options": [["HIGH", "High"], ["LOW", "Low"]]}]}, "74hc595_setAllBin": {"message0": "74HC595%1 définir les niveaux de sortie avec le tableau %2[]", "args0": [null, null]}, "74hc595_getstate": {"message0": "74HC595%1 obtenir l'état de la broche de sortie n°%2", "args0": [null, null]}}