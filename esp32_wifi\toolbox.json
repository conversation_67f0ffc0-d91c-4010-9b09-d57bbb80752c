{"kind": "category", "name": "ESP32 WiFi", "icon": "fa-light fa-wifi", "contents": [{"kind": "label", "text": "WiFi连接"}, {"kind": "block", "type": "esp32_wifi_begin", "inputs": {"SSID": {"shadow": {"type": "text", "fields": {"TEXT": "your-ssid"}}}, "PASSWORD": {"shadow": {"type": "text", "fields": {"TEXT": "your-password"}}}}}, {"kind": "block", "type": "esp32_wifi_connected"}, {"kind": "block", "type": "esp32_wifi_status"}, {"kind": "block", "type": "esp32_wifi_disconnect"}, {"kind": "sep"}, {"kind": "label", "text": "网络信息"}, {"kind": "block", "type": "esp32_wifi_local_ip"}, {"kind": "block", "type": "esp32_wifi_rssi"}, {"kind": "sep"}, {"kind": "label", "text": "网络扫描"}, {"kind": "block", "type": "esp32_wifi_scan"}, {"kind": "block", "type": "esp32_wifi_get_scan_result", "inputs": {"INDEX": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}}}, {"kind": "sep"}, {"kind": "label", "text": "热点模式"}, {"kind": "block", "type": "esp32_wifi_ap_mode", "inputs": {"SSID": {"shadow": {"type": "text", "fields": {"TEXT": "ESP32-AP"}}}, "PASSWORD": {"shadow": {"type": "text", "fields": {"TEXT": "12345678"}}}}}, {"kind": "block", "type": "esp32_wifi_ap_ip"}, {"kind": "sep"}, {"kind": "label", "text": "网络客户端"}, {"kind": "block", "type": "esp32_wifi_client_connect", "inputs": {"HOST": {"shadow": {"type": "text", "fields": {"TEXT": "baidu.com"}}}, "PORT": {"shadow": {"type": "math_number", "fields": {"NUM": 80}}}}}, {"kind": "block", "type": "esp32_wifi_client_print", "inputs": {"DATA": {"shadow": {"type": "text", "fields": {"TEXT": "Hello"}}}}}, {"kind": "block", "type": "esp32_wifi_client_available"}, {"kind": "block", "type": "esp32_wifi_client_read_string"}, {"kind": "block", "type": "esp32_wifi_client_stop"}, {"kind": "sep"}, {"kind": "label", "text": "HTTP请求"}, {"kind": "block", "type": "esp32_wifi_http_get", "inputs": {"URL": {"shadow": {"type": "text", "fields": {"TEXT": "http://httpbin.org/get"}}}}}, {"kind": "block", "type": "esp32_wifi_http_post", "inputs": {"URL": {"shadow": {"type": "text", "fields": {"TEXT": "http://httpbin.org/post"}}}, "DATA": {"shadow": {"type": "text", "fields": {"TEXT": "key=value&name=test"}}}}}, {"kind": "block", "type": "esp32_wifi_http_post_json", "inputs": {"URL": {"shadow": {"type": "text", "fields": {"TEXT": "http://httpbin.org/post"}}}, "JSON_DATA": {"shadow": {"type": "text", "fields": {"TEXT": "{\"name\":\"test\",\"value\":123}"}}}}}, {"kind": "block", "type": "esp32_wifi_http_request", "inputs": {"URL": {"shadow": {"type": "text", "fields": {"TEXT": "http://httpbin.org/post"}}}, "DATA": {"shadow": {"type": "text", "fields": {"TEXT": "data to send"}}}, "CONTENT_TYPE": {"shadow": {"type": "text", "fields": {"TEXT": "application/json"}}}}}, {"kind": "sep"}, {"kind": "label", "text": "事件处理"}, {"kind": "block", "type": "esp32_wifi_event_handler"}]}