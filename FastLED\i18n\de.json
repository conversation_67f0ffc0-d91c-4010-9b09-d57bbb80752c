{"toolbox_name": "FastLED", "fastled_init": {"message0": "Initialisiere RGB-LED-Streifen Pin %1 Typ %2 Anzahl LEDs %3", "args0": [null, {"options": [["WS2812B", "WS2812B"], ["WS2812", "WS2812"], ["WS2811", "WS2811"], ["NEOPIXEL", "NEOPIXEL"], ["WS2801", "WS2801"], ["LPD8806", "LPD8806"], ["APA102", "APA102"]]}, null]}, "fastled_set_pixel": {"message0": "Setze RGB-LED-Streifen Pin %1 Index %2 Farbe %3"}, "fastled_show": {"message0": "Aktualisiere RGB-LED-Streifen"}, "fastled_clear": {"message0": "Lösche RGB-LED-Streifen Pin %1"}, "fastled_brightness": {"message0": "Helligkeit einstellen %1"}, "fastled_rgb": {"message0": "RGB-Farbe R %1 G %2 B %3"}, "fastled_preset_color": {"message0": "Farbe %1"}, "fastled_fill_solid": {"message0": "Alle LEDs füllen Pin %1 Farbe %2"}, "fastled_hsv": {"message0": "HSV-Farbe H %1 S %2 V %3"}, "fastled_rainbow": {"message0": "Regenbogeneffekt Pin %1 Startfarbton %2 Inkrement %3"}, "fastled_fire_effect": {"message0": "Feuereffekt Pin %1 Hitze %2 Abkühlgeschwindigkeit %3"}, "fastled_meteor": {"message0": "Meteor-Effekt Pin %1 Farbe %2 Meteorgröße %3 Schweifausblendung %4 Geschwindigkeit %5"}, "fastled_palette_cycle": {"message0": "Palettenzyklus-Effekt Pin %1 Palette %2 Geschwindigkeit %3", "args0": [null, {"options": [["Regenbogen", "RainbowColors_p"], ["<PERSON><PERSON>", "LavaColors_p"], ["Wolken", "CloudColors_p"], ["<PERSON><PERSON>", "OceanColors_p"], ["<PERSON><PERSON>", "ForestColors_p"], ["Party", "PartyColors_p"], ["<PERSON><PERSON>", "HeatColors_p"]]}, null]}, "fastled_breathing": {"message0": "Atmungseffekt Pin %1 Farbe %2 Geschwindigkeit %3"}, "fastled_twinkle": {"message0": "Funkel-Effekt Pin %1 Anzahl Funkeln %2 Hintergrund %3 Funkelfarbe %4"}}