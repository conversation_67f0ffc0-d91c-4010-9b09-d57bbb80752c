{"toolbox_name": "Registrador de deslocamento", "74hc595_create": {"message0": "Inicializar 74HC595 %1 quantidade %2 data:%3  clock:%4 latch:%5", "args0": [null, null, {"options": "${board.digitalPins}"}, {"options": "${board.digitalPins}"}, {"options": "${board.digitalPins}"}]}, "74hc595_set": {"message0": "74HC595 %1 definir pino %2 para %3", "args0": [null, {"options": "${board.digitalPins}"}, {"options": [["ALTO", "HIGH"], ["BAIXO", "LOW"]]}]}, "74hc595_setAll": {"message0": "74HC595 %1 definir todos os pinos para %2", "args0": [null, {"options": [["ALTO", "High"], ["BAIXO", "Low"]]}]}, "74hc595_setAllBin": {"message0": "74HC595%1 definir níveis de saída usando o array %2[]", "args0": [null, null]}, "74hc595_getstate": {"message0": "74HC595%1 obter o estado do pino de saída número %2", "args0": [null, null]}}