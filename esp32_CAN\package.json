{"name": "@aily-project/lib-esp32-twai", "nickname": "ESP32 CAN总线", "author": "aily Project", "description": "ESP32 CAN(TWAI)通信库，支持发送和接收CAN消息，适用于ESP32系列开发板", "version": "0.0.1", "compatibility": {"core": ["esp32:esp32", "esp32:esp32c3", "esp32:esp32s3"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "esp32", "can", "twai", "esp32_can_init", "esp32_can_send_message", "esp32_can_receive_message", "esp32_can_configure_alerts", "esp32_can_check_alerts"], "scripts": {}, "dependencies": {}, "devDependencies": {}}