# ESP32蓝牙游戏手柄库

这是一个用于aily blockly的ESP32蓝牙游戏手柄库，可以将ESP32开发板转换为蓝牙游戏手柄，支持按键、摇杆、方向键和特殊按键等功能。

## 功能特性

- 支持多达16个标准按键
- 支持摇杆轴控制（X、Y、Z、RX、RY、RZ）
- 支持方向键（HAT）控制
- 支持特殊按键（开始、选择、菜单、主页等）
- 支持模拟输入映射到摇杆轴
- 支持物理按键映射到游戏手柄按键
- 自动处理去抖动

## 使用说明

### 1. 初始化游戏手柄
使用"初始化ESP32蓝牙游戏手柄"块来设置蓝牙游戏手柄，可以自定义设备名称。

### 2. 检查连接状态
使用"蓝牙游戏手柄已连接"块来检查是否有设备连接到游戏手柄。

### 3. 按键控制
- 使用"按下按键"和"释放按键"块来控制特定按键
- 使用"当引脚按键被按下时"块将物理按键映射到游戏手柄按键

### 4. 摇杆控制
- 使用"设置摇杆轴"块来控制摇杆位置
- 使用"将引脚模拟输入映射到摇杆轴"块将电位器等模拟输入映射到摇杆

### 5. 方向键控制
使用"设置方向键"块来控制游戏手柄的方向键。

### 6. 特殊按键
使用特殊按键相关的块来控制开始、选择、菜单等特殊按键。

## 兼容性

- 支持ESP32、ESP32-C3、ESP32-S3开发板
- 工作电压：3.3V
- 兼容Windows、Android、iOS等支持蓝牙游戏手柄的系统

## 注意事项

1. 确保ESP32开发板支持蓝牙功能
2. 某些系统可能需要手动配对蓝牙设备
3. 游戏手柄功能需要目标设备支持HID（人机接口设备）协议

## 示例

本库基于以下开源项目：
- ESP32-BLE-Gamepad by lemmingDev

## 许可证

本库遵循原始项目的许可证。
