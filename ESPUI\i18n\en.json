{"toolbox_name": "ESPUI Web Interface", "ESPUI_BEGIN_MESSAGE0": "Initialize ESPUI Interface Title %1 Username %2 Password %3", "ESPUI_BEGIN_TOOLTIP": "Initialize ESPUI interface with title and access credentials", "ESPUI_LABEL_MESSAGE0": "Create Label Text %1 Color %2", "ESPUI_LABEL_TOOLTIP": "Create a text label", "ESPUI_BUTTON_MESSAGE0": "Create Button Text %1 Color %2", "ESPUI_BUTTON_TOOLTIP": "Create a button control", "ESPUI_SWITCHER_MESSAGE0": "Create Switch Text %1 Color %2 Initial State %3", "ESPUI_SWITCHER_TOOLTIP": "Create a switch control", "ESPUI_SLIDER_MESSAGE0": "Create Slider Text %1 Color %2 Min %3 Max %4 Initial Value %5", "ESPUI_SLIDER_TOOLTIP": "Create a slider control", "ESPUI_TEXT_MESSAGE0": "Create Text Input Text %1 Color %2 Initial Value %3", "ESPUI_TEXT_TOOLTIP": "Create a text input field", "ESPUI_NUMBER_MESSAGE0": "Create Number Input Text %1 Color %2 Min %3 Max %4 Initial Value %5", "ESPUI_NUMBER_TOOLTIP": "Create a number input field", "ESPUI_GRAPH_MESSAGE0": "Create Graph Text %1 Color %2", "ESPUI_GRAPH_TOOLTIP": "Create a graph control", "ESPUI_GAUGE_MESSAGE0": "Create Gauge Text %1 Color %2 Min %3 Max %4 Initial Value %5", "ESPUI_GAUGE_TOOLTIP": "Create a gauge control", "ESPUI_UPDATE_CONTROL_MESSAGE0": "Update Control ID %1 Value %2", "ESPUI_UPDATE_CONTROL_TOOLTIP": "Update the value of specified control", "ESPUI_UPDATE_LABEL_MESSAGE0": "Update Control Label ID %1 Label %2", "ESPUI_UPDATE_LABEL_TOOLTIP": "Update the label text of specified control", "ESPUI_ON_EVENT_MESSAGE0": "When Control Event Triggered Control ID %1 Event Type %2 %3 Execute %4", "ESPUI_ON_EVENT_TOOLTIP": "Set control event callback function", "COLOR_TURQUOISE": "Turquoise", "COLOR_EMERALD": "Emerald", "COLOR_PETERRIVER": "Peter River", "COLOR_AMETHYST": "Amethyst", "COLOR_WETASPHALT": "Wet Asphalt", "COLOR_GREENSEA": "Green Sea", "COLOR_NEPHRITIS": "Nephritis", "COLOR_BELIZEHOLE": "Belize Hole", "COLOR_WISTERIA": "Wisteria", "COLOR_MIDNIGHTBLUE": "Midnight Blue", "COLOR_SUNFLOWER": "Sunflower", "COLOR_CARROT": "Carrot", "COLOR_ALIZARIN": "<PERSON><PERSON><PERSON>", "COLOR_CLOUDS": "Clouds", "COLOR_CONCRETE": "Concrete", "COLOR_ORANGE": "Orange", "COLOR_PUMPKIN": "<PERSON><PERSON><PERSON>", "COLOR_POMEGRANATE": "Pomegranate", "COLOR_SILVER": "Silver", "COLOR_ASBESTOS": "Asbestos", "COLOR_DARK": "Dark", "COLOR_NONE": "None", "STATE_ON": "On", "STATE_OFF": "Off", "EVENT_B_DOWN": "Press", "EVENT_B_UP": "Release", "EVENT_S_ACTIVE": "Change", "ESPUI_GET_VALUE_MESSAGE0": "Get Control Value ID %1", "ESPUI_GET_VALUE_TOOLTIP": "Get the current value of specified control", "ESPUI_ADD_GRAPH_POINT_MESSAGE0": "Add Graph Point Graph ID %1 Value %2", "ESPUI_ADD_GRAPH_POINT_TOOLTIP": "Add a data point to specified graph", "ESPUI_CLEAR_GRAPH_MESSAGE0": "Clear Graph Graph ID %1", "ESPUI_CLEAR_GRAPH_TOOLTIP": "Clear all data points from specified graph", "ESPUI_WIFI_SETUP_MESSAGE0": "WiFi Setup SSID %1 Password %2 Mode %3", "ESPUI_WIFI_SETUP_TOOLTIP": "Configure WiFi connection or hotspot mode", "ESPUI_WIFI_STATUS_MESSAGE0": "WiFi Connection Status", "ESPUI_WIFI_STATUS_TOOLTIP": "Check if WiFi is connected successfully", "ESPUI_GET_IP_MESSAGE0": "Get IP Address", "ESPUI_GET_IP_TOOLTIP": "Get current ESP device IP address", "WIFI_STA_MODE": "Station Mode", "WIFI_AP_MODE": "Access Point Mode"}