[{"type": "custom_code", "message0": "自定义代码 %1", "args0": [{"type": "field_multilinetext", "name": "CODE", "text": "// custom code;"}], "previousStatement": null, "nextStatement": null, "colour": "#FF9800", "tooltip": "在程序中插入自定义代码"}, {"type": "custom_macro", "message0": "宏定义 %1 为 %2", "args0": [{"type": "field_input", "name": "NAME", "text": "PIN_LED"}, {"type": "field_input", "name": "VALUE", "text": "13"}], "inputsInline": true, "colour": "#FF9800", "tooltip": "添加宏定义到程序顶部"}, {"type": "custom_library", "message0": "引用库 %1", "args0": [{"type": "field_input", "name": "LIB_NAME", "text": "Servo.h"}], "colour": "#FF9800", "tooltip": "添加库引用到程序顶部"}, {"type": "custom_variable", "message0": "定义变量 类型 %1 名称 %2 初始值 %3", "args0": [{"type": "field_dropdown", "name": "TYPE", "options": [["整型", "int"], ["长整型", "long*"], ["浮点型", "float"], ["双精度浮点型", "double"], ["无符号整型", "unsigned char"], ["无符号长整型", "unsigned char"], ["布尔型", "bool"], ["字符型", "char"], ["字符串型", "string"]]}, {"type": "field_input", "name": "NAME", "text": "newVariable"}, {"type": "field_input", "name": "VALUE", "text": "0"}], "inputsInline": true, "colour": "#FF9800", "tooltip": "添加全局变量定义"}, {"type": "custom_function", "message0": "函数定义 %1 %2 返回类型 %3 参数列表 %4 %5 函数体 %6", "args0": [{"type": "field_input", "name": "NAME", "text": "myFunction"}, {"type": "input_dummy"}, {"type": "field_dropdown", "name": "RETURN", "options": [["整型", "int"], ["长整型", "long*"], ["浮点型", "float"], ["双精度浮点型", "double"], ["无符号整型", "unsigned char"], ["无符号长整型", "unsigned char"], ["布尔型", "bool"], ["字符型", "char"], ["字符串型", "string"]]}, {"type": "field_input", "name": "PARAMS", "text": "int x, int y"}, {"type": "input_dummy"}, {"type": "field_multilinetext", "name": "BODY", "text": "// custom code;"}], "colour": "#FF9800", "tooltip": "定义一个自定义函数"}]