{"name": "@aily-project/lib-mqtt", "nickname": "MQTT通信库", "author": "<PERSON><PERSON><PERSON><PERSON>", "description": "允许Arduino UNO R3通过WiFi模块与MQTT服务器进行通信，实现物联网数据收发", "version": "0.1.0", "compatibility": {"core": ["arduino:avr"], "voltage": [5]}, "keywords": ["aily", "blockly", "mqtt", "communication", "iot", "a<PERSON><PERSON><PERSON>", "uno", "r3"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "hide": true}