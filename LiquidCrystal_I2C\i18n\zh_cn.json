{"toolbox_name": "LCD1602 I2C", "lcd_i2c_init": {"message0": "初始化LCD I2C显示器 地址%1 列数%2 行数%3", "args0": [[["0x27", "0x27"], ["0x26", "0x26"], ["0x25", "0x25"], ["0x24", "0x24"], ["0x23", "0x23"], ["0x22", "0x22"], ["0x21", "0x21"], ["0x20", "0x20"]], null, null]}, "lcd_i2c_clear": {"message0": "清空LCD显示"}, "lcd_i2c_set_cursor": {"message0": "设置LCD光标到列%1 行%2"}, "lcd_i2c_print": {"message0": "LCD输出 %1"}, "lcd_i2c_print_position": {"message0": "LCD在列%1 行%2 输出%3"}, "lcd_i2c_backlight_on": {"message0": "打开LCD背光"}, "lcd_i2c_backlight_off": {"message0": "关闭LCD背光"}, "lcd_i2c_custom_char": {"message0": "自定义字符 %1 编号 %2", "args0": [null, ["0", "0"], ["1", "1"], ["2", "2"], ["3", "3"], ["4", "4"], ["5", "5"], ["6", "6"], ["7", "7"]]}}