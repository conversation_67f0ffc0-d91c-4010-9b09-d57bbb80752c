{"toolbox_name": "シリアル", "serial_begin": {"message0": "シリアル%1を初期化し、ボーレートを%2に設定する"}, "serial_available": {"message0": "シリアル%1バッファにデータがあります"}, "serial_read": {"message0": "シリアル%1データを%2読み取ります", "args0": [null, {"options": [["読み取り", "read"], ["ピーク", "peek"], ["整数を解析", "parseInt"], ["浮動小数点数を解析", "parseFloat"]]}]}, "serial_print": {"message0": "シリアル%1が%2を出力"}, "serial_println": {"message0": "シリアル%1が%2を出力し、改行します"}, "serial_write": {"message0": "シリアル%1が生データ%2を出力"}, "serial_read_string": {"message0": "シリアル%1の文字列を読み取ります"}}