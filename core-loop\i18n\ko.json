{"toolbox_name": "반복", "arduino_setup": {"message0": "▶️설정 %1"}, "arduino_loop": {"message0": "🔁루프 %1"}, "controls_repeat_ext": {"message0": "%1번 반복", "message1": "%1 실행"}, "controls_repeat": {"message0": "%1번 반복", "message1": "%1 실행"}, "controls_whileUntil": {"message0": "%1 %2", "args0": [{"options": [["조건이 충족될 때까지 반복", "WHILE"], ["조건이 충족될 때까지 반복", "UNTIL"]]}], "message1": "%1 실행"}, "controls_for": {"message0": "변수 %1을 %2에서 %3으로, %4씩 증가", "message1": "%1 실행"}, "controls_flow_statements": {"message0": "%1", "args0": [{"options": [["반복문 종료", "BREAK"], ["다음 반복으로 계속", "CONTINUE"]]}]}, "controls_whileForever": {"message0": "🔁 영원히 반복 %1"}}