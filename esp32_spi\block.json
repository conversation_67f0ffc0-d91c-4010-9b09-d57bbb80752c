[{"type": "esp32_spi_create", "message0": "创建 SPI对象 %1 使用芯片 %2", "args0": [{"type": "field_variable", "name": "VAR", "variable": "spi1", "variableTypes": ["SPI"], "defaultType": "SPI"}, {"type": "field_dropdown", "name": "SPI_TYPE", "options": [["HSPI", "HSPI"], ["VSPI", "VSPI"]]}], "previousStatement": null, "nextStatement": null, "colour": "#009688", "inputsInline": true}, {"type": "esp32_spi_begin", "message0": "初始化 SPI对象 %1 (默认引脚)", "args0": [{"type": "input_value", "name": "SPI_INST"}], "previousStatement": null, "nextStatement": null, "colour": "#009688", "inputsInline": true}, {"type": "esp32_spi_begin_pins", "message0": "初始化 SPI对象 %1, SCK: %2, MISO: %3, MOSI: %4, SS: %5", "args0": [{"type": "input_value", "name": "SPI_INST"}, {"type": "input_value", "name": "SCK_PIN"}, {"type": "input_value", "name": "MISO_PIN"}, {"type": "input_value", "name": "MOSI_PIN"}, {"type": "input_value", "name": "SS_PIN"}], "previousStatement": null, "nextStatement": null, "colour": "#009688", "inputsInline": true}, {"type": "esp32_spi_begin_transaction", "message0": "SPI对象 %1 开始事务 (时钟: %2, 位序: %3, 数据模式: %4)", "args0": [{"type": "input_value", "name": "SPI_INST"}, {"type": "input_value", "name": "CLOCK_SPEED"}, {"type": "field_dropdown", "name": "BIT_ORDER", "options": [["MSBFIRST", "MSBFIRST"], ["LSBFIRST", "LSBFIRST"]]}, {"type": "field_dropdown", "name": "DATA_MODE", "options": [["模式0", "SPI_MODE0"], ["模式1", "SPI_MODE1"], ["模式2", "SPI_MODE2"], ["模式3", "SPI_MODE3"]]}], "previousStatement": null, "nextStatement": null, "colour": "#009688", "inputsInline": true}, {"type": "esp32_spi_end_transaction", "message0": "SPI对象 %1 结束事务", "args0": [{"type": "input_value", "name": "SPI_INST"}], "previousStatement": null, "nextStatement": null, "colour": "#009688", "inputsInline": true}, {"type": "esp32_spi_transfer", "message0": "SPI对象 %1 传输数据 %2", "args0": [{"type": "input_value", "name": "SPI_INST"}, {"type": "input_value", "name": "DATA"}], "output": "Number", "colour": "#009688", "inputsInline": true}, {"type": "esp32_spi_pin_ss", "message0": "获取 SPI对象 %1 的SS引脚", "args0": [{"type": "input_value", "name": "SPI_INST"}], "output": "Number", "colour": "#009688", "inputsInline": true}]