{"toolbox_name": "رياضيات", "math_number": {"message0": "%1"}, "math_arithmetic": {"message0": "%1 %2 %3", "args0": [null, {"options": [["جمع", "ADD"], ["طرح", "MINUS"], ["ضرب", "MULTIPLY"], ["قسمة", "DIVIDE"], ["باقي القسمة", "MODULO"], ["قوة", "POWER"]]}, null]}, "math_single": {"message0": "%1 %2", "args0": [{"options": [["الجذر التربيعي", "ROOT"], ["القيمة المطلقة", "ABS"], ["سالب", "NEG"], ["ln", "LN"], ["log10", "LOG10"], ["e^", "EXP"], ["10^", "POW10"]]}, null]}, "math_trig": {"message0": "%1 %2", "args0": [{"options": [["جي<PERSON> الزاوية", "SIN"], ["<PERSON><PERSON><PERSON> التمام", "COS"], ["ظل الزاوية", "TAN"], ["زاوية القوس الجيب", "ASIN"], ["زاوية القوس الجيب التمام", "ACOS"], ["زاوية القوس الظل", "ATAN"]]}, null]}, "math_constant": {"message0": "%1", "args0": [{"options": [["π", "PI"], ["e", "E"], ["النسبة الذهبية", "GOLDEN_RATIO"], ["√2", "SQRT2"], ["√1/2", "SQRT1_2"], ["∞", "INFINITY"]]}]}, "math_number_property": {"message0": "%1 %2", "args0": [null, {"options": [["<PERSON><PERSON><PERSON> زوجي", "EVEN"], ["عد<PERSON> فردي", "ODD"], ["<PERSON><PERSON><PERSON>و<PERSON>ي", "PRIME"], ["<PERSON><PERSON><PERSON> صحيح", "WHOLE"], ["<PERSON><PERSON><PERSON> موجب", "POSITIVE"], ["<PERSON><PERSON><PERSON> سالب", "NEGATIVE"], ["قابل للقسمة على", "DIVISIBLE_BY"]]}]}, "math_change": {"message0": "غير %1 ب %2"}, "math_round": {"message0": "%1 %2", "args0": [{"options": [["تقريب", "ROUND"], ["تقريب لأعلى", "ROUNDUP"], ["تقريب لأسفل", "ROUNDDOWN"]]}, null]}, "math_on_list": {"message0": "%1 %2", "args0": [{"options": [["مجموع القائمة", "SUM"], ["أصغر قيمة في القائمة", "MIN"], ["أكبر قيمة في القائمة", "MAX"], ["متوسط القائمة", "AVERAGE"], ["قيمة الوسطى في القائمة", "MEDIAN"], ["القيمة الأكثر تكرار في القائمة", "MODE"], ["الانحراف المعياري للقائمة", "STD_DEV"], ["عنصر عشوائي من القائمة", "RANDOM"]]}, null]}, "math_modulo": {"message0": "باقي %1 ÷ %2"}, "math_constrain": {"message0": "تحديد %1 بين %2 و %3"}, "math_random_int": {"message0": "عدد صحيح عشوائي من %1 إلى %2"}, "math_random_float": {"message0": "كسر عشوائي"}, "math_atan2": {"message0": "زاوية الاتجاه للنقطة (x: %1, y: %2)"}, "math_round_to_decimal": {"message0": "تقريب %1 إلى %2 منازل عشرية"}, "math_bitwise_not": {"message0": "~ %1"}, "map_to": {"message0": "ارسم الخريطة %1 من [%2,%3] إلى [%4,%5]"}, "constrain": {"message0": "قيد %1 بين (الأدنى) %2 و (الأقصى) %3"}}