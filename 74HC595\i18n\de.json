{"toolbox_name": "Schieberegister", "74hc595_create": {"message0": "Initialisiere 74HC595 %1 Anzahl %2 Daten:%3  Takt:%4 Latch:%5", "args0": [null, null, {"options": "${board.digitalPins}"}, {"options": "${board.digitalPins}"}, {"options": "${board.digitalPins}"}]}, "74hc595_set": {"message0": "74HC595 %1 Setze Pin %2 auf %3", "args0": [null, {"options": "${board.digitalPins}"}, {"options": [["HIGH", "HIGH"], ["LOW", "LOW"]]}]}, "74hc595_setAll": {"message0": "74HC595 %1 Setze alle Pins auf %2", "args0": [null, {"options": [["HIGH", "High"], ["LOW", "Low"]]}]}, "74hc595_setAllBin": {"message0": "74HC595%1 Setze Ausgangspegel mit Array %2[]", "args0": [null, null]}, "74hc595_getstate": {"message0": "74HC595%1 Lese Status von Ausgang %2", "args0": [null, null]}}