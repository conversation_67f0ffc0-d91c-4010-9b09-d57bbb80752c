[{"type": "vl53l0x_config_i2c", "message0": "配置VL53L0X I2C引脚 %1", "args0": [{"type": "field_dropdown", "name": "WIRE_OPTION", "options": [["Wire1 (SDA:8, SCL:9)", "WIRE1"], ["Wire2 (SDA:4, SCL:5)", "WIRE2"]]}], "previousStatement": null, "nextStatement": null, "colour": "#2D7DB3", "tooltip": "配置VL53L0X传感器使用的I2C引脚"}, {"type": "vl53l0x_init_with_wire", "message0": "初始化VL53L0X传感器 %1 使用 %2", "args0": [{"type": "field_variable", "name": "SENSOR", "variable": "sensor"}, {"type": "field_dropdown", "name": "WIRE", "options": "${board.i2c}"}], "previousStatement": null, "nextStatement": null, "inputsInline": false, "colour": "#2D7DB3", "tooltip": "初始化VL53L0X激光测距传感器，指定使用的I2C总线", "extensions": ["vl53l0x_init_with_wire_pin_info"]}, {"type": "vl53l0x_begin", "message0": "初始化VL53L0X传感器 %1 %2", "args0": [{"type": "field_variable", "name": "SENSOR", "variable": "sensor", "variableTypes": ["Adafruit_VL53L0X"], "defaultType": "Adafruit_VL53L0X"}, {"type": "input_dummy", "name": "I2C_PINS"}], "previousStatement": null, "nextStatement": null, "colour": "#2D7DB3", "tooltip": "初始化VL53L0X激光测距传感器，配置I2C引脚并检查连接", "extensions": ["vl53l0x_begin_extension"]}, {"type": "vl53l0x_ranging_test", "message0": "传感器 %1 进行测量并存入结果 %2", "args0": [{"type": "field_variable", "name": "SENSOR", "variable": "sensor", "variableTypes": ["Adafruit_VL53L0X"], "defaultType": "Adafruit_VL53L0X"}, {"type": "field_variable", "name": "MEASURE", "variable": "measure", "variableTypes": ["VL53L0X_RangingMeasurementData_t"], "defaultType": "VL53L0X_RangingMeasurementData_t"}], "previousStatement": null, "nextStatement": null, "colour": "#2D7DB3", "tooltip": "进行一次详细测量并将结果存入指定变量"}, {"type": "vl53l0x_check_range_valid", "message0": "测量结果 %1 有效", "args0": [{"type": "field_variable", "name": "MEASURE", "variable": "measure", "variableTypes": ["VL53L0X_RangingMeasurementData_t"], "defaultType": "VL53L0X_RangingMeasurementData_t"}], "output": "Boolean", "colour": "#2D7DB3", "tooltip": "检查测量结果是否有效，状态值不为4时表示有效"}, {"type": "vl53l0x_get_range_mm", "message0": "从测量结果 %1 中获取距离(mm)", "args0": [{"type": "field_variable", "name": "MEASURE", "variable": "measure", "variableTypes": ["VL53L0X_RangingMeasurementData_t"], "defaultType": "VL53L0X_RangingMeasurementData_t"}], "output": "Number", "colour": "#2D7DB3", "tooltip": "从测量结果中获取距离数据，单位是毫米"}]