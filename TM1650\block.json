[{"type": "tm1650_init", "message0": "四位数码TM1650管初始化", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#FF8800"}, {"type": "tm1650_set", "message0": "四位数码TM1650 显示%1,亮度设置为%2", "args0": [{"type": "field_dropdown", "name": "TM1650PWRSWITCH", "options": [["开", "1"], ["关", "0"]]}, {"type": "field_dropdown", "name": "TM1650BRIGHTNESS", "options": [["0", "0"], ["1", "1"], ["2", "2"], ["3", "3"], ["4", "4"], ["5", "5"], ["6", "6"], ["7", "7"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#FF8800"}, {"type": "tm1650_displaystring", "message0": "四位数码TM1650 显示字符串%1", "args0": [{"type": "input_value", "name": "TMSTR", "check": "String"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#FF8800"}, {"type": "tm1650_displayNumber", "message0": "四位数码TM1650 显示数字%1", "args0": [{"type": "input_value", "name": "TMNUM", "check": "Number"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#FF8800"}, {"type": "tm1650_clearDisplay", "message0": "四位数码TM1650 清屏", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#FF8800"}]