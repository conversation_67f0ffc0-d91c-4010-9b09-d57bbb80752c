{"toolbox_name": "<PERSON>и<PERSON><PERSON>", "arduino_setup": {"message0": "▶️Настроить %1"}, "arduino_loop": {"message0": "🔁Цикл %1"}, "controls_repeat_ext": {"message0": "Повторить %1 раз", "message1": "Выполнить %1"}, "controls_repeat": {"message0": "Повторить %1 раз", "message1": "Выполнить %1"}, "controls_whileUntil": {"message0": "%1 %2", "args0": [{"options": [["Повторять, пока условие не выполнено", "WHILE"], ["Повторять до тех пор, пока условие не выполнится", "UNTIL"]]}], "message1": "Выполнить %1"}, "controls_for": {"message0": "Переменная %1 от %2 до %3, увеличивать на %4", "message1": "Выполнить %1"}, "controls_flow_statements": {"message0": "%1", "args0": [{"options": [["Выйти из цикла", "BREAK"], ["Продолжить следующую итерацию", "CONTINUE"]]}]}, "controls_whileForever": {"message0": "🔁 Бесконечный цикл %1"}}