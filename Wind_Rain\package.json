{"name": "@aily-project/lib-wind", "nickname": "风速风向雨量传感器", "author": "aily Project", "description": "风速风向雨量传感器控制库，支持风速测量(km/h, mph)和风向检测，以及雨量测量，适用于Arduino、ESP32等开发板", "version": "1.0.0", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp32:esp32c3", "esp32:esp32s3"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "wind", "windspeed", "winddirection", "sensor", "weather", "风速", "风向", "传感器", "气象", "雨量", "降雨"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "openjumper"}