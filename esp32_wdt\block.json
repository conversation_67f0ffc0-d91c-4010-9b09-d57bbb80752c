[{"type": "wdt_init", "message0": "初始化任务看门狗 超时时间 %1 秒 %2 触发异常时重启", "args0": [{"type": "field_number", "name": "TIMEOUT", "value": 5, "min": 1, "max": 128}, {"type": "field_checkbox", "name": "PANIC", "checked": false}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00979D"}, {"type": "wdt_add_task", "message0": "添加当前任务到看门狗监控", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00979D"}, {"type": "wdt_reset", "message0": "重置看门狗定时器（喂狗）", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00979D"}, {"type": "wdt_add_user", "message0": "添加用户 %1 到看门狗监控", "args0": [{"type": "input_value", "name": "USER_NAME", "check": "String"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00979D"}, {"type": "wdt_reset_user", "message0": "重置用户 %1 的看门狗定时器", "args0": [{"type": "input_value", "name": "USER_NAME", "check": "String"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00979D"}, {"type": "wdt_delete_task", "message0": "从看门狗监控中移除当前任务", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00979D"}, {"type": "wdt_delete_user", "message0": "从看门狗监控中移除用户 %1", "args0": [{"type": "input_value", "name": "USER_NAME", "check": "String"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00979D"}, {"type": "wdt_deinit", "message0": "反初始化看门狗定时器", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00979D"}]