{"toolbox_name": "I/O контакты", "io_pinmode": {"message0": "Установить режим контакта %1 на %2"}, "io_digitalwrite": {"message0": "Вывод цифрового сигнала %2 на контакт %1"}, "io_digitalread": {"message0": "Считать цифровой сигнал с контакта %1"}, "io_analogwrite": {"message0": "Вывод PWM сигнала %2 на контакт %1"}, "io_analogread": {"message0": "Считать аналоговый сигнал с контакта %1"}, "io_pin_digi": {"message0": "Цифровой контакт %1"}, "io_pin_adc": {"message0": "Аналоговый контакт %1"}, "io_pin_pwm": {"message0": "PWM контакт %1"}, "io_mode": {"message0": "Режим контакта %1"}, "io_state": {"message0": "Уровень %1"}}