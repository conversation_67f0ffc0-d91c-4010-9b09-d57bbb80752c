{"kind": "category", "icon": "fa-light fa-network-wired", "name": "I2C", "contents": [{"kind": "block", "type": "wire_begin"}, {"kind": "block", "type": "wire_begin_address"}, {"kind": "block", "type": "wire_set_clock"}, {"kind": "block", "type": "wire_end"}, {"kind": "block", "type": "wire_begin_transmission"}, {"kind": "block", "type": "wire_end_transmission"}, {"kind": "block", "type": "wire_write"}, {"kind": "block", "type": "wire_write_bytes"}, {"kind": "block", "type": "wire_request_from"}, {"kind": "block", "type": "wire_available"}, {"kind": "block", "type": "wire_read"}, {"kind": "block", "type": "wire_peek"}, {"kind": "block", "type": "wire_flush"}, {"kind": "block", "type": "wire_on_receive"}, {"kind": "block", "type": "wire_on_request"}, {"kind": "block", "type": "wire_scan_devices"}, {"kind": "block", "type": "wire_set_timeout"}, {"kind": "block", "type": "wire_get_timeout_flag"}, {"kind": "block", "type": "wire_clear_timeout_flag"}]}