[{"type": "mqtt_begin", "message0": "MQTT连接到服务器 %1 端口 %2 用户名 %3 密码 %4", "args0": [{"type": "input_value", "name": "BROKER", "check": "String"}, {"type": "input_value", "name": "PORT", "check": "Number"}, {"type": "input_value", "name": "USERNAME", "check": "String"}, {"type": "input_value", "name": "PASSWORD", "check": "String"}], "inputsInline": false, "previousStatement": null, "nextStatement": null, "colour": 65, "tooltip": "连接到MQTT服务器"}, {"type": "mqtt_connected", "message0": "MQTT是否已连接", "output": "Boolean", "colour": 65, "tooltip": "检查MQTT客户端是否已连接"}, {"type": "mqtt_poll", "message0": "MQTT保持连接", "previousStatement": null, "nextStatement": null, "colour": 65, "tooltip": "保持MQTT连接活跃，应放在loop中"}, {"type": "mqtt_subscribe", "message0": "MQTT订阅主题 %1", "args0": [{"type": "input_value", "name": "TOPIC", "check": "String"}], "previousStatement": null, "nextStatement": null, "colour": 65, "tooltip": "订阅MQTT主题"}, {"type": "mqtt_publish", "message0": "MQTT发布消息 %1 到主题 %2", "args0": [{"type": "input_value", "name": "MESSAGE", "check": ["String", "Number"]}, {"type": "input_value", "name": "TOPIC", "check": "String"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 65, "tooltip": "发布消息到MQTT主题"}, {"type": "mqtt_message_available", "message0": "MQTT有新消息可读", "output": "Boolean", "colour": 65, "tooltip": "检查是否有MQTT新消息可读"}, {"type": "mqtt_read_topic", "message0": "MQTT读取消息主题", "output": "String", "colour": 65, "tooltip": "读取接收到的消息的主题"}, {"type": "mqtt_read_message", "message0": "MQTT读取消息内容", "output": "String", "colour": 65, "tooltip": "读取接收到的消息内容"}]