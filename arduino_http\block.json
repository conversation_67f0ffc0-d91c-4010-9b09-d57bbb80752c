[{"type": "wifi_connect", "message0": "连接WiFi SSID: %1 密码: %2", "args0": [{"type": "field_input", "name": "SSID", "text": "yourSSID"}, {"type": "field_input", "name": "PASS", "text": "yourPASS"}], "previousStatement": null, "nextStatement": null, "colour": "#5C81A6"}, {"type": "wifi_ssid", "message0": "当前WiFi网络", "output": "String", "colour": "#5C81A6"}, {"type": "wifi_localip", "message0": "本地IP地址", "output": "String", "colour": "#5C81A6"}, {"type": "http_get", "message0": "HTTP GET 请求到 %1", "args0": [{"type": "field_input", "name": "URL", "text": "http://example.com"}], "previousStatement": null, "nextStatement": null, "colour": "#FF8C00"}, {"type": "http_post", "message0": "HTTP POST 请求到 %1, 类型: %2, 数据: %3", "args0": [{"type": "field_input", "name": "URL", "text": "http://example.com"}, {"type": "field_input", "name": "TYPE", "text": "application/json"}, {"type": "field_input", "name": "DATA", "text": "{}"}], "previousStatement": null, "nextStatement": null, "colour": "#FF8C00"}, {"type": "analog_read", "message0": "读取模拟端口 %1", "args0": [{"type": "field_dropdown", "name": "PIN", "options": [["A0", "A0"], ["A1", "A1"], ["A2", "A2"], ["A3", "A3"], ["A4", "A4"], ["A5", "A5"]]}], "output": "Number", "colour": "#6A5ACD"}]