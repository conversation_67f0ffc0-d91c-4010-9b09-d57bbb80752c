{"name": "@aily-project/lib-qwen-omni", "nickname": "通义千问", "author": "<PERSON><PERSON><PERSON>", "description": "阿里云通义千问Qwen-Omni大语言模型API库，支持文字对话、多轮对话、系统提示词设置等功能，适用于ESP32等支持WiFi的开发板", "version": "0.0.1", "compatibility": {"core": ["esp32:esp32", "esp32:esp32c3", "esp32:esp32s3", "esp32:esp32c6", "esp32:esp32h2"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "qwen", "qwen-omni", "artificial intelligence", "通义千问", "阿里云", "大语言模型", "LLM", "AI", "人工智能", "对话", "聊天", "chat", "conversation"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": false, "tester": ""}