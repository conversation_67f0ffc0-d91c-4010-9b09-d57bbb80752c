{"toolbox_name": "دبابيس الإدخال/الإخراج", "io_pinmode": {"message0": "تعيين وضع الدبوس %1 إلى %2"}, "io_digitalwrite": {"message0": "إرسال إشارة رقمية %2 على الدبوس %1"}, "io_digitalread": {"message0": "قراءة الإشارة الرقمية من الدبوس %1"}, "io_analogwrite": {"message0": "إرسال إشارة PWM %2 على الدبوس %1"}, "io_analogread": {"message0": "قراءة الإشارة التناظرية من الدبوس %1"}, "io_pin_digi": {"message0": "دبوس رقمي %1"}, "io_pin_adc": {"message0": "دبوس تناظري %1"}, "io_pin_pwm": {"message0": "دبوس PWM %1"}, "io_mode": {"message0": "وضع الدبوس %1"}, "io_state": {"message0": "مستوى %1"}}