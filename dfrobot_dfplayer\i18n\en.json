{"toolbox_name": "DFPlayer", "dfplayer_begin": {"message0": "Initialize DFPlayer module %1 RX pin %2 TX pin %3"}, "dfplayer_play": {"message0": "DFPlayer %1 play file number %2"}, "dfplayer_pause": {"message0": "DFPlayer %1 pause playback"}, "dfplayer_start": {"message0": "DFPlayer %1 resume playback"}, "dfplayer_stop": {"message0": "DFPlayer %1 stop playback"}, "dfplayer_next": {"message0": "DFPlayer %1 play next track"}, "dfplayer_previous": {"message0": "DFPlayer %1 play previous track"}, "dfplayer_volume": {"message0": "DFPlayer %1 set volume to %2"}, "dfplayer_volume_up": {"message0": "DFPlayer %1 increase volume"}, "dfplayer_volume_down": {"message0": "DFPlayer %1 decrease volume"}, "dfplayer_eq": {"message0": "DFPlayer %1 set equalizer to %2", "options": [["Normal", "0"], ["Pop", "1"], ["Rock", "2"], ["Jazz", "3"], ["Classic", "4"], ["Bass", "5"]]}, "dfplayer_output_device": {"message0": "DFPlayer %1 set output device to %2", "options": [["DEVICE1", "1"], ["DEVICE2", "2"]]}, "dfplayer_loop": {"message0": "DFPlayer %1 loop file number %2"}, "dfplayer_play_folder": {"message0": "DFPlayer %1 play file %3 in folder %2"}, "dfplayer_enable_loop_all": {"message0": "DFPlayer %1 enable loop all"}, "dfplayer_disable_loop_all": {"message0": "DFPlayer %1 disable loop all"}, "dfplayer_play_mp3_folder": {"message0": "DFPlayer %1 play file %2 from MP3 folder"}, "dfplayer_advertise": {"message0": "DFPlayer %1 play advertisement %2"}, "dfplayer_stop_advertise": {"message0": "DFPlayer %1 stop advertisement"}, "dfplayer_play_large_folder": {"message0": "DFPlayer %1 play file %3 in large folder %2"}, "dfplayer_loop_folder": {"message0": "DFPlayer %1 loop folder %2"}, "dfplayer_random_all": {"message0": "DFPlayer %1 random play all files"}, "dfplayer_enable_loop": {"message0": "DFPlayer %1 enable loop"}, "dfplayer_disable_loop": {"message0": "DFPlayer %1 disable loop"}, "dfplayer_read_state": {"message0": "DFPlayer %1 read state"}, "dfplayer_read_volume": {"message0": "DFPlayer %1 read volume"}, "dfplayer_read_eq": {"message0": "DFPlayer %1 read equalizer"}, "dfplayer_read_file_counts": {"message0": "DFPlayer %1 read file counts"}, "dfplayer_read_current_file_number": {"message0": "DFPlayer %1 read current file number"}, "dfplayer_read_file_counts_in_folder": {"message0": "DFPlayer %1 read file counts in folder %2"}, "dfplayer_available": {"message0": "DFPlayer %1 check if message available"}, "dfplayer_read_type": {"message0": "DFPlayer %1 read message type"}, "dfplayer_read": {"message0": "DFPlayer %1 read message parameter"}, "dfplayer_simple_play": {"message0": "Simple play: Initialize DFPlayer (RX: %1, TX: %2) and play file %3"}}