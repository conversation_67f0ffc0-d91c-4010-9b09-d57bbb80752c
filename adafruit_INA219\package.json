{"name": "@aily-project/lib-adafruit_ina219", "nickname": "INA219传感器驱动库", "author": "<PERSON>Jumper", "description": "INA219传感器驱动库，适用于esp32、arduino，支持I2C通信，提供电流、总线电压、分线电压、功率数据获取功能。", "version": "0.0.1", "compatibility": {"core": ["esp32:esp32", "esp32:esp32s3", "arduino:avr"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "INA219", "电流", "总线电压", "分线电压", "功率", "I2C", "传感器"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "openjumper", "url": "https://github.com/adafruit/Adafruit_INA219"}