{"toolbox_name": "Shift Register", "74hc595_create": {"message0": "Initialize 74HC595 %1 Quantity %2 data:%3  clock:%4 latch:%5", "args0": [null, null, {"options": "${board.digitalPins}"}, {"options": "${board.digitalPins}"}, {"options": "${board.digitalPins}"}]}, "74hc595_set": {"message0": "74HC595 %1 set pin %2 to %3", "args0": [null, {"options": "${board.digitalPins}"}, {"options": [["HIGH", "HIGH"], ["LOW", "LOW"]]}]}, "74hc595_setAll": {"message0": "74HC595 %1 set all pins to %2", "args0": [null, {"options": [["HIGH", "High"], ["LOW", "Low"]]}]}, "74hc595_setAllBin": {"message0": "74HC595 %1 set output levels using array %2[]", "args0": [null, null]}, "74hc595_getstate": {"message0": "74HC595 %1 get state of output pin %2", "args0": [null, null]}}