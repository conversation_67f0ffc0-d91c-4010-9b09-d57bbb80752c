[{"type": "wifimanager_init", "message0": "初始化WiFiManager", "previousStatement": null, "nextStatement": null, "colour": "#4CAF50", "inputsInline": true}, {"type": "wifimanager_autoconnect", "message0": "WiFiManager自动连接并广播SSID %1 密码 %2", "args0": [{"type": "input_value", "name": "SSID", "check": "String"}, {"type": "input_value", "name": "PASSWORD", "check": "String"}], "output": "Boolean", "colour": "#4CAF50", "inputsInline": true}, {"type": "wifimanager_autoconnect_simple", "message0": "WiFiManager自动连接（使用默认配置）", "output": "Boolean", "colour": "#4CAF50", "inputsInline": true}, {"type": "wifimanager_simple_setup", "message0": "WiFiManager初始化并广播SSID %1 密码 %2", "args0": [{"type": "input_value", "name": "SSID", "check": "String"}, {"type": "input_value", "name": "PASSWORD", "check": "String"}], "previousStatement": null, "nextStatement": null, "colour": "#66BB6A", "inputsInline": true, "tooltip": "一键设置WiFi连接，自动处理连接成功或失败的情况"}, {"type": "wifimanager_set_timeout", "message0": "设置配置门户超时 %1 秒", "args0": [{"type": "input_value", "name": "TIMEOUT", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50", "inputsInline": true}, {"type": "wifimanager_reset_settings", "message0": "重置WiFiManager设置", "previousStatement": null, "nextStatement": null, "colour": "#F44336", "inputsInline": true}, {"type": "wifimanager_start_config_portal", "message0": "启动配置门户 SSID %1 密码 %2", "args0": [{"type": "input_value", "name": "SSID", "check": "String"}, {"type": "input_value", "name": "PASSWORD", "check": "String"}], "output": "Boolean", "colour": "#FF9800", "inputsInline": true}, {"type": "wifimanager_set_blocking", "message0": "设置WiFiManager为 %1 模式", "args0": [{"type": "field_dropdown", "name": "MODE", "options": [["阻塞", "true"], ["非阻塞", "false"]]}], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50", "inputsInline": true}, {"type": "wifimanager_process", "message0": "处理WiFiManager（非阻塞模式）", "previousStatement": null, "nextStatement": null, "colour": "#2196F3", "inputsInline": true}, {"type": "wifimanager_set_static_ip", "message0": "设置静态IP %1 网关 %2 子网掩码 %3", "args0": [{"type": "input_value", "name": "IP", "check": "String"}, {"type": "input_value", "name": "GATEWAY", "check": "String"}, {"type": "input_value", "name": "SUBNET", "check": "String"}], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50", "inputsInline": false}, {"type": "wifimanager_add_parameter", "message0": "添加自定义参数 ID %1 标签 %2 默认值 %3 长度 %4", "args0": [{"type": "input_value", "name": "ID", "check": "String"}, {"type": "input_value", "name": "LABEL", "check": "String"}, {"type": "input_value", "name": "DEFAULT_VALUE", "check": "String"}, {"type": "input_value", "name": "LENGTH", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#9C27B0", "inputsInline": false}, {"type": "wifimanager_get_parameter", "message0": "获取参数值 %1", "args0": [{"type": "input_value", "name": "PARAM_ID", "check": "String"}], "output": "String", "colour": "#9C27B0", "inputsInline": true}]