{"name": "@aily-project/lib-liquidcrystal", "nickname": "LCD1602显示屏", "author": "aily Project", "description": "LCD1602显示屏控制支持库，使用4线并行通信，支持Arduino UNO、MEGA、ESP8266、ESP32等开发板", "version": "1.0.0", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "lcd", "液晶显示", "lcd1602", "LiquidCrystal"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "mango-0616"}