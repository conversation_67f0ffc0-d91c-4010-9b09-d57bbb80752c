{"toolbox_name": "邏輯", "controls_if": {"message0": "🔀如果 %1", "message1": "則執行 %1"}, "controls_ifelse": {"message0": "🔀如果 %1", "message1": "執行 %1", "message2": "否則 %1"}, "logic_compare": {"message0": "%1 %2 %3", "args0": [null, {"options": [["==", "EQ"], ["!=", "NEQ"], ["<", "LT"], [">", "GT"], [">=", "GTE"], ["<=", "LTE"]]}, null]}, "logic_operation": {"message0": "%1 %2 %3", "args0": [null, {"options": [["與", "AND"], ["或", "OR"]]}, null]}, "logic_negate": {"message0": "非 %1"}, "logic_boolean": {"message0": "%1", "args0": [{"options": [["真", "true"], ["假", "false"]]}]}, "logic_ternary": {"message0": "斷言 %1", "message1": "如果為true %1", "message2": "如果為false %1"}}