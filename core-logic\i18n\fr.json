{"toolbox_name": "Logique", "controls_if": {"message0": "🔀Si %1", "message1": "Alors faire %1"}, "controls_ifelse": {"message0": "🔀Si %1", "message1": "Faire %1", "message2": "Sinon %1"}, "logic_compare": {"message0": "%1 %2 %3", "args0": [null, {"options": [["==", "EQ"], ["!=", "NEQ"], ["<", "LT"], [">", "GT"], [">=", "GTE"], ["<=", "LTE"]]}, null]}, "logic_operation": {"message0": "%1 %2 %3", "args0": [null, {"options": [["ET", "AND"], ["OU", "OR"]]}, null]}, "logic_negate": {"message0": "Pas %1"}, "logic_boolean": {"message0": "%1", "args0": [{"options": [["Vrai", "true"], ["Faux", "false"]]}]}, "logic_ternary": {"message0": "Affirmer %1", "message1": "Si vrai %1", "message2": "Si faux %1"}}