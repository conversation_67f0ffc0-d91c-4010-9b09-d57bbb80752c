[{"type": "wifi_begin", "message0": "初始化 WiFi 模块", "previousStatement": null, "nextStatement": null, "colour": "#33cc33"}, {"type": "wifi_connect", "message0": "WiFi 连接到 SSID %1 密码 %2", "args0": [{"type": "input_value", "name": "SSID"}, {"type": "input_value", "name": "PASSWORD"}], "previousStatement": null, "nextStatement": null, "colour": "#33cc33"}, {"type": "wifi_connected", "message0": "是否已连接 WiFi?", "output": "Boolean", "colour": "#33cc33"}, {"type": "ciao_begin", "message0": "初始化 Ciao 模块", "previousStatement": null, "nextStatement": null, "colour": "#0099cc"}, {"type": "ciao_read", "message0": "读取 %1 协议 从主机 %2 方法 %3", "args0": [{"type": "field_dropdown", "name": "CONNECTOR", "options": [["rest", "rest"], ["mqtt", "mqtt"]]}, {"type": "input_value", "name": "HOSTNAME"}, {"type": "field_dropdown", "name": "METHOD", "options": [["GET", "GET"], ["POST", "POST"]]}], "output": "CiaoData", "colour": "#0099cc"}, {"type": "ciao_write", "message0": "写入 %1 协议 至主机 %2 数据 %3 方法 %4", "args0": [{"type": "field_dropdown", "name": "CONNECTOR", "options": [["rest", "rest"], ["mqtt", "mqtt"]]}, {"type": "input_value", "name": "HOSTNAME"}, {"type": "input_value", "name": "DATA"}, {"type": "field_dropdown", "name": "METHOD", "options": [["GET", "GET"], ["POST", "POST"]]}], "previousStatement": null, "nextStatement": null, "colour": "#0099cc"}]