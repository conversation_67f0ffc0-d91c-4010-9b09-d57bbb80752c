[{"type": "ble_begin", "message0": "初始化BLE", "previousStatement": null, "nextStatement": null, "colour": "#4CAF50"}, {"type": "ble_scan", "message0": "扫描BLE设备", "previousStatement": null, "nextStatement": null, "colour": "#2196F3"}, {"type": "ble_connect", "message0": "连接到设备 %1", "args0": [{"type": "input_value", "name": "DEVICE"}], "previousStatement": null, "nextStatement": null, "colour": "#FF9800"}, {"type": "ble_disconnect", "message0": "断开连接BLE设备", "previousStatement": null, "nextStatement": null, "colour": "#FF9800"}, {"type": "ble_read_characteristic", "message0": "读取特征 %1", "args0": [{"type": "input_value", "name": "CHARACTERISTIC"}], "output": "String", "colour": "#9C27B0"}, {"type": "ble_write_characteristic", "message0": "写入特征 %1 值 %2", "args0": [{"type": "input_value", "name": "CHARACTERISTIC"}, {"type": "input_value", "name": "VALUE"}], "previousStatement": null, "nextStatement": null, "colour": "#9C27B0"}]