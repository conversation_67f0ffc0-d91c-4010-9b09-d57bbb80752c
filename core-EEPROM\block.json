[{"inputsInline": true, "message0": "读取EEPROM 地址%1", "type": "eeprom_read", "colour": "#48c2c4", "args0": [{"type": "input_value", "name": "ADDRESS"}], "b4a": {"code": "EEPROM.read(${ADDRESS})", "library": "#include <EEPROM.h>"}, "output": "Any"}, {"inputsInline": true, "message0": "EEPROM空间大小", "type": "eeprom_length", "colour": "#48c2c4", "args0": [], "b4a": {"code": "EEPROM.length()", "library": "#include <EEPROM.h>"}, "output": "Any"}, {"inputsInline": true, "message0": "向EEPROM地址 %1 写入 %2", "type": "eeprom_write", "colour": "#48c2c4", "args0": [{"type": "input_value", "name": "ADDRESS"}, {"type": "input_value", "name": "VALUE"}], "b4a": {"code": "EEPROM.write(${ADDRESS}, ${VALUE});", "library": "#include <EEPROM.h>"}, "previousStatement": null, "nextStatement": null}]