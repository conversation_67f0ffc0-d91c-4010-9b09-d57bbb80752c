{"name": "@aily-project/lib-keyboard", "nickname": "USB模拟键盘", "author": "<PERSON><PERSON><PERSON><PERSON>", "description": "将Arduino模拟成USB键盘，可以实现键盘输入、按键模拟等功能", "version": "1.0.0", "compatibility": {"core": ["esp32:esp32s3", "esp32:esp32c3", "renesas_uno:minima", "renesas_uno:unor4wifi"], "voltage": [5]}, "keywords": ["aily", "blockly", "keyboard", "usb", "hid"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "mango-0616"}