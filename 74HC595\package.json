{"name": "@aily-project/lib-shiftregister", "nickname": "移位寄存器驱动库", "author": "<PERSON><PERSON>", "description": "移位寄存器74HC595控制库，支持Arduino UNO、MEGA、ESP8266、ESP32等开发板", "version": "0.0.1", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "shiftregister", "74HC595", "shift_register", "移位寄存器", "io扩展"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "K2L"}