[{"type": "mouse_begin", "message0": "初始化鼠标", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00979D"}, {"type": "mouse_click", "message0": "鼠标点击 %1", "args0": [{"type": "field_dropdown", "name": "BUTTON", "options": [["左键", "MOUSE_LEFT"], ["右键", "MOUSE_RIGHT"], ["中键", "MOUSE_MIDDLE"], ["所有按键", "MOUSE_ALL"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00979D"}, {"type": "mouse_move", "message0": "鼠标移动 X: %1 Y: %2 滚轮: %3", "args0": [{"type": "input_value", "name": "X", "check": "Number"}, {"type": "input_value", "name": "Y", "check": "Number"}, {"type": "input_value", "name": "WHEEL", "check": "Number"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00979D"}, {"type": "mouse_press", "message0": "鼠标按下 %1", "args0": [{"type": "field_dropdown", "name": "BUTTON", "options": [["左键", "MOUSE_LEFT"], ["右键", "MOUSE_RIGHT"], ["中键", "MOUSE_MIDDLE"], ["所有按键", "MOUSE_ALL"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00979D"}, {"type": "mouse_release", "message0": "鼠标释放 %1", "args0": [{"type": "field_dropdown", "name": "BUTTON", "options": [["左键", "MOUSE_LEFT"], ["右键", "MOUSE_RIGHT"], ["中键", "MOUSE_MIDDLE"], ["所有按键", "MOUSE_ALL"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00979D"}, {"type": "mouse_is_pressed", "message0": "鼠标按键 %1 被按下", "args0": [{"type": "field_dropdown", "name": "BUTTON", "options": [["左键", "MOUSE_LEFT"], ["右键", "MOUSE_RIGHT"], ["中键", "MOUSE_MIDDLE"], ["所有按键", "MOUSE_ALL"]]}], "output": "Boolean", "colour": "#00979D"}]