{"name": "@aily-project/lib-bh1750", "nickname": "BH1750光照传感器", "author": "aily Project", "description": "BH1750数字光照强度传感器控制库，适用于Arduino、ESP32等开发板", "version": "0.0.1", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp32:esp32s3"], "voltage": [3.3, 5]}, "keywords": ["bh1750", "光照传感器", "BH1750", "light"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "openjumper", "url": "https://github.com/claws/BH1750"}