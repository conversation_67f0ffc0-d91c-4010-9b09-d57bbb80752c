{"name": "@aily-project/lib-esp32-wifi", "nickname": "ESP32 WiFi库", "author": "aily Project", "description": "ESP32 WiFi功能支持库，包含WiFi连接、热点模式、HTTP请求", "version": "1.0.0", "compatibility": {"core": ["esp32:esp32"], "voltage": [3.3]}, "keywords": ["aily", "blockly", "esp32", "wifi", "wireless", "internet", "http", "post", "get", "json", "api", "client", "ap", "hotspot", "iot", "esp32_wifi_begin", "esp32_wifi_connected", "esp32_wifi_local_ip", "esp32_wifi_scan", "esp32_wifi_ap_mode", "esp32_wifi_http_get", "esp32_wifi_http_post"], "scripts": {}, "dependencies": {}, "devDependencies": {}}