[{"type": "tb6612_motor_init", "message0": "初始化TB6612双电机驱动器 %1 电机A名称 %2 AIN1 %3 AIN2 %4 PWMA %5 偏移量A %6", "args0": [{"type": "input_dummy"}, {"type": "field_variable", "name": "MOTOR_A_NAME", "variable": "motor1"}, {"type": "input_value", "name": "AIN1", "check": "Number"}, {"type": "input_value", "name": "AIN2", "check": "Number"}, {"type": "input_value", "name": "PWMA", "check": "Number"}, {"type": "input_value", "name": "OFFSET_A", "check": "Number"}], "message1": "电机B名称 %1 BIN1 %2 BIN2 %3 PWMB %4 偏移量B %5", "args1": [{"type": "field_variable", "name": "MOTOR_B_NAME", "variable": "motor2"}, {"type": "input_value", "name": "BIN1", "check": "Number"}, {"type": "input_value", "name": "BIN2", "check": "Number"}, {"type": "input_value", "name": "PWMB", "check": "Number"}, {"type": "input_value", "name": "OFFSET_B", "check": "Number"}], "message2": "STBY引脚 %1", "args2": [{"type": "input_value", "name": "STBY_PIN", "check": "Number"}], "inputsInline": false, "previousStatement": null, "nextStatement": null, "colour": "#FFA500", "tooltip": "初始化TB6612双电机驱动器，配置两个电机的所有引脚", "helpUrl": ""}, {"type": "tb6612_drive", "message0": "电机 %1 前进 速度: %2", "args0": [{"type": "field_dropdown", "name": "MOTOR_SELECT", "options": [["motor1", "A"], ["motor2", "B"]]}, {"type": "input_value", "name": "SPEED", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#FFA500", "tooltip": "驱动指定电机，motor1对应电机A，motor2对应电机B", "helpUrl": ""}, {"type": "tb6612_reverse", "message0": "电机 %1 后退 速度: %2", "args0": [{"type": "field_dropdown", "name": "MOTOR_SELECT", "options": [["motor1", "A"], ["motor2", "B"]]}, {"type": "input_value", "name": "SPEED", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#FFA500", "tooltip": "让指定电机后退，motor1对应电机A，motor2对应电机B", "helpUrl": ""}, {"type": "tb6612_brake", "message0": "电机 %1 刹车", "args0": [{"type": "field_dropdown", "name": "MOTOR_SELECT", "options": [["motor1", "A"], ["motor2", "B"]]}], "previousStatement": null, "nextStatement": null, "colour": "#FFA500", "tooltip": "刹车指定电机，motor1对应电机A，motor2对应电机B", "helpUrl": ""}, {"type": "tb6612_dual_action", "message0": "双电机动作 模式: %1 速度: %2\n前后: M1 %3, M2 %4\n左右: 左 %5, 右 %6", "args0": [{"type": "field_dropdown", "name": "MODE", "options": [["前进", "forward"], ["后退", "back"], ["左转", "left"], ["右转", "right"], ["刹车", "brake"]]}, {"type": "input_value", "name": "SPEED"}, {"type": "field_variable", "name": "MOTOR1", "variable": "motor1", "variableTypes": ["Motor"], "defaultType": "Motor"}, {"type": "field_variable", "name": "MOTOR2", "variable": "motor2", "variableTypes": ["Motor"], "defaultType": "Motor"}, {"type": "field_variable", "name": "LEFT_MOTOR", "variable": "leftMotor", "variableTypes": ["Motor"], "defaultType": "Motor"}, {"type": "field_variable", "name": "RIGHT_MOTOR", "variable": "rightMotor", "variableTypes": ["Motor"], "defaultType": "Motor"}], "previousStatement": null, "nextStatement": null, "colour": "#FF6600"}]