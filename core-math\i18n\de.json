{"toolbox_name": "Mathematik", "math_number": {"message0": "%1"}, "math_arithmetic": {"message0": "%1 %2 %3", "args0": [null, {"options": [["Addieren", "ADD"], ["Subtrahieren", "MINUS"], ["Multiplizieren", "MULTIPLY"], ["<PERSON><PERSON><PERSON><PERSON>", "DIVIDE"], ["<PERSON><PERSON><PERSON>", "MODULO"], ["Potenzieren", "POWER"]]}, null]}, "math_single": {"message0": "%1 %2", "args0": [{"options": [["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ROOT"], ["Betrag", "ABS"], ["Negativ", "NEG"], ["ln", "LN"], ["log10", "LOG10"], ["e^", "EXP"], ["10^", "POW10"]]}, null]}, "math_trig": {"message0": "%1 %2", "args0": [{"options": [["Sinus", "SIN"], ["<PERSON><PERSON><PERSON>", "COS"], ["Tangens", "TAN"], ["<PERSON><PERSON><PERSON>", "ASIN"], ["<PERSON><PERSON><PERSON><PERSON>", "ACOS"], ["<PERSON>ust<PERSON><PERSON>", "ATAN"]]}, null]}, "math_constant": {"message0": "%1", "args0": [{"options": [["π", "PI"], ["e", "E"], ["<PERSON><PERSON>", "GOLDEN_RATIO"], ["√2", "SQRT2"], ["√1/2", "SQRT1_2"], ["∞", "INFINITY"]]}]}, "math_number_property": {"message0": "%1 %2", "args0": [null, {"options": [["ist gerade", "EVEN"], ["ist ungerade", "ODD"], ["ist prim", "PRIME"], ["ist ganz", "WHOLE"], ["ist positiv", "POSITIVE"], ["ist negativ", "NEGATIVE"], ["ist teilbar durch", "DIVISIBLE_BY"]]}]}, "math_change": {"message0": "%1 um %2 ändern"}, "math_round": {"message0": "%1 %2", "args0": [{"options": [["<PERSON><PERSON>", "ROUND"], ["<PERSON><PERSON><PERSON><PERSON>", "ROUNDUP"], ["<PERSON><PERSON><PERSON><PERSON>", "ROUNDDOWN"]]}, null]}, "math_on_list": {"message0": "%1 %2", "args0": [{"options": [["Summe der Liste", "SUM"], ["Minimum der Liste", "MIN"], ["<PERSON> der Liste", "MAX"], ["Durchschnitt der Liste", "AVERAGE"], ["Median der Liste", "MEDIAN"], ["Modalwert der Liste", "MODE"], ["Standardabweichung der Liste", "STD_DEV"], ["Zufälliges Element der Liste", "RANDOM"]]}, null]}, "math_modulo": {"message0": "Rest von %1 ÷ %2"}, "math_constrain": {"message0": "%1 zwischen %2 und %3 einschränken"}, "math_random_int": {"message0": "zufällige ganze Zahl von %1 bis %2"}, "math_random_float": {"message0": "<PERSON><PERSON><PERSON><PERSON><PERSON> Bruch"}, "math_atan2": {"message0": "atan2 von <PERSON> (x: %1, y: %2)"}, "math_round_to_decimal": {"message0": "runde %1 auf %2 Dezimalstellen"}, "math_bitwise_not": {"message0": "~ %1"}, "map_to": {"message0": "%1 von [%2,%3] auf [%4,%5] abbilden"}, "constrain": {"message0": "%1 zwischen (min) %2 und (max) %3 einschränken"}}