# 风速风向传感器库 (Wind Speed & Direction Sensor Library)

## 简介
这是一个用于aily blockly的风速风向传感器控制库，支持风速测量（千米/小时、英里/小时）和风向检测功能。

## 功能特性
- **风速测量**：支持千米/小时(km/h)和英里/小时(mph)两种单位
- **风向检测**：提供角度值(0-360°)和方向字符串(N、NE、E、SE、S、SW、W、NW)
- **灵活初始化**：支持同时初始化风速风向传感器，或单独初始化其中一个
- **数据更新**：提供统一的数据更新接口和单独的风速/风向更新接口
- **调试功能**：内置串口打印功能，方便调试和监控
- **状态检查**：提供数据准备状态检查功能
- **🎯 简化使用**：提供一键式获取数据块，自动处理初始化和更新逻辑（推荐新手使用）
- **🔧 常用组合**：预制常用的程序段组合，快速实现功能

## 硬件连接
- **风速传感器**：连接到数字引脚（推荐引脚2），使用中断检测风速脉冲
- **风向传感器**：连接到模拟引脚（推荐引脚A0），通过ADC读取风向电压值

## 使用方法

### 🎯 简化使用（推荐新手）
**这是最简单的使用方式，无需手动初始化和更新数据：**
1. 直接拖拽"简化获取风速"或"简化获取风向"块到需要的位置
2. 选择对应的引脚
3. 系统会自动处理初始化和数据更新，无需额外操作

### 📋 常用组合（快速上手）
在toolbox的"常用组合"部分提供了预制的程序段：
- **风速显示组合**：自动格式化输出"风速: XX km/h"
- **风向显示组合**：自动格式化输出"风向: XX"

### ⚙️ 高级使用（完全控制）
1. 拖拽"初始化风速风向传感器"块到setup区域
2. 选择相应的引脚（风速引脚和风向引脚）
3. 在loop区域添加"更新风速风向数据"块
4. 使用各种获取数据的块来读取风速和风向信息

### 🔧 专业功能
- 使用单独的初始化块可以只初始化风速或风向传感器中的一个
- 使用打印块可以在串口监视器中查看详细的传感器数据
- 使用"数据是否准备就绪"块可以检查数据更新状态

## 兼容性
- **开发板**：Arduino UNO、MEGA、ESP32、ESP32-C3、ESP32-S3、Arduino UNO R4等
- **电压**：支持3.3V和5V供电系统

## 技术说明
该库基于中断机制检测风速脉冲，通过ADC读取风向电压值并转换为方向角度。对ESP32平台进行了特别优化，自动处理12位ADC精度差异。

## 开源说明
本库为原创开发，无第三方开源库依赖。
