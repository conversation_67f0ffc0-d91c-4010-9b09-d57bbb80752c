{"toolbox_name": "LCD1602 I2C", "lcd_i2c_init": {"message0": "Initialisiere LCD I2C Display Adresse %1 Spalten %2 Zeilen %3", "args0": [[["0x27", "0x27"], ["0x26", "0x26"], ["0x25", "0x25"], ["0x24", "0x24"], ["0x23", "0x23"], ["0x22", "0x22"], ["0x21", "0x21"], ["0x20", "0x20"]], null, null]}, "lcd_i2c_clear": {"message0": "LCD-Anzeige löschen"}, "lcd_i2c_set_cursor": {"message0": "Setze LCD-Cursor auf Spalte %1 Zeile %2"}, "lcd_i2c_print": {"message0": "LCD ausgeben %1"}, "lcd_i2c_print_position": {"message0": "LCD an Spalte %1 Zeile %2 ausgeben %3"}, "lcd_i2c_backlight_on": {"message0": "LCD-Hintergrundbeleuchtung einschalten"}, "lcd_i2c_backlight_off": {"message0": "LCD-Hintergrundbeleuchtung ausschalten"}, "lcd_i2c_custom_char": {"message0": "Benutzerdefiniertes Zeichen %1 Nummer %2", "args0": [null, ["0", "0"], ["1", "1"], ["2", "2"], ["3", "3"], ["4", "4"], ["5", "5"], ["6", "6"], ["7", "7"]]}}