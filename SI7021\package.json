{"name": "@aily-project/lib-si7021", "nickname": "Si7021温湿度传感器", "author": "aily Project", "description": "Si7021温湿度传感器控制库，适用于Arduino、ESP32等开发板，基于Adafruit Si7021库", "version": "1.0.0", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp32:esp32c3", "esp32:esp32s3"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "si7021", "temperature", "humidity", "sensor", "温度", "湿度", "传感器"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "openjumper"}