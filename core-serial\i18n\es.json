{"toolbox_name": "Serial", "serial_begin": {"message0": "Inicializar <PERSON>%1 con velocidad en baudios %2"}, "serial_available": {"message0": "Serial%1 el búfer tiene datos"}, "serial_read": {"message0": "Leer datos de Serial%1 %2", "args0": [null, {"options": [["leer", "read"], ["echar un vistazo", "peek"], ["analizar entero", "parseInt"], ["analizar flotante", "parseFloat"]]}]}, "serial_print": {"message0": "Serial%1 sale %2"}, "serial_println": {"message0": "Serial%1 sale %2 con nueva línea"}, "serial_write": {"message0": "Serial%1 sale datos en bruto %2"}, "serial_read_string": {"message0": "Leer cadena de Serial%1"}}