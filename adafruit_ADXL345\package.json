{"name": "@aily-project/lib-adafruit-adxl345", "nickname": "ADXL345加速度传感器", "author": "adafruit", "description": "ADXL345三轴加速度传感器库，I2C通信，可读取X/Y/Z轴加速度数据", "version": "1.0.0", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "sensor", "accelerometer", "ADXL345", "acceleration", "I2C", "加速度", "传感器", "三轴", "adxl345_init", "adxl345_read_x", "adxl345_read_y", "adxl345_read_z", "adxl345_read_xyz", "adxl345_set_range", "adxl345_set_data_rate"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": false}