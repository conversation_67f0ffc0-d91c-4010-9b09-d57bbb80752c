{"name": "@aily-project/lib-r4-wdt", "nickname": "R4看门狗", "author": "<PERSON><PERSON><PERSON><PERSON>", "description": "适用于Arduino UNO R4的看门狗库", "version": "1.0.0", "compatibility": {"core": ["renesas_uno:minima", "renesas_uno:unor4wifi"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "watchdog", "wdt", "wdt_begin", "wdt_refresh", "wdt_gettimeout", "system", "reset"], "scripts": {}, "dependencies": {}, "devDependencies": {}}