{"toolbox_name": "Serial", "serial_begin": {"message0": "Iniciar Serial%1 com taxa de baud de %2"}, "serial_available": {"message0": "Serial%1 o buffer tem dados"}, "serial_read": {"message0": "Ler dados da Serial%1 %2", "args0": [null, {"options": [["ler", "read"], ["espreitar", "peek"], ["analisar inteiro", "parseInt"], ["analisar flutuante", "parseFloat"]]}]}, "serial_print": {"message0": "Serial%1 sai %2"}, "serial_println": {"message0": "Serial%1 sai %2 com nova linha"}, "serial_write": {"message0": "Serial%1 sai dados brutos %2"}, "serial_read_string": {"message0": "Ler string da Serial%1"}}