[{"type": "espui_begin", "message0": "初始化ESPUI界面 标题 %1 用户名 %2 密码 %3", "args0": [{"type": "input_value", "name": "TITLE", "check": "String"}, {"type": "input_value", "name": "USERNAME", "check": "String"}, {"type": "input_value", "name": "PASSWORD", "check": "String"}], "inputsInline": false, "previousStatement": null, "nextStatement": null, "colour": "#03a9f4", "tooltip": "初始化ESPUI界面，设置标题和访问凭据"}, {"type": "espui_label", "message0": "创建标签 文本 %1 颜色 %2", "args0": [{"type": "input_value", "name": "TEXT", "check": "String"}, {"type": "field_dropdown", "name": "COLOR", "options": [["蓝绿色", "Turquoise"], ["翠绿色", "Emerald"], ["彼得河蓝", "Peterriver"], ["紫水晶", "Amethyst"], ["湿沥青", "<PERSON><PERSON><PERSON><PERSON>"], ["绿海", "Greensea"], ["海蓝宝石", "Nephritis"], ["贝尔法斯特蓝", "Belizehole"], ["紫藤", "Wisteria"], ["午夜蓝", "Midnightblue"], ["阳橙", "Sunflower"], ["胡萝卜", "Carrot"], ["茜红", "<PERSON><PERSON><PERSON>"], ["云朵", "Clouds"], ["混凝土", "Concrete"], ["橙色", "Orange"], ["南瓜", "<PERSON><PERSON><PERSON>"], ["石榴红", "Pomegranate"], ["银色", "Silver"], ["石墨", "Asbestos"], ["暗色", "Dark"], ["无", "None"]]}], "inputsInline": true, "output": "Number", "colour": "#03a9f4", "tooltip": "创建一个文本标签"}, {"type": "espui_button", "message0": "创建按钮 文本 %1 颜色 %2", "args0": [{"type": "input_value", "name": "TEXT", "check": "String"}, {"type": "field_dropdown", "name": "COLOR", "options": [["蓝绿色", "Turquoise"], ["翠绿色", "Emerald"], ["彼得河蓝", "Peterriver"], ["紫水晶", "Amethyst"], ["湿沥青", "<PERSON><PERSON><PERSON><PERSON>"], ["绿海", "Greensea"], ["海蓝宝石", "Nephritis"], ["贝尔法斯特蓝", "Belizehole"], ["紫藤", "Wisteria"], ["午夜蓝", "Midnightblue"], ["阳橙", "Sunflower"], ["胡萝卜", "Carrot"], ["茜红", "<PERSON><PERSON><PERSON>"], ["云朵", "Clouds"], ["混凝土", "Concrete"], ["橙色", "Orange"], ["南瓜", "<PERSON><PERSON><PERSON>"], ["石榴红", "Pomegranate"], ["银色", "Silver"], ["石墨", "Asbestos"], ["暗色", "Dark"], ["无", "None"]]}], "inputsInline": true, "output": "Number", "colour": "#03a9f4", "tooltip": "创建一个按钮控件"}, {"type": "espui_switcher", "message0": "创建开关 文本 %1 颜色 %2 初始状态 %3", "args0": [{"type": "input_value", "name": "TEXT", "check": "String"}, {"type": "field_dropdown", "name": "COLOR", "options": [["蓝绿色", "Turquoise"], ["翠绿色", "Emerald"], ["彼得河蓝", "Peterriver"], ["紫水晶", "Amethyst"], ["湿沥青", "<PERSON><PERSON><PERSON><PERSON>"], ["绿海", "Greensea"], ["海蓝宝石", "Nephritis"], ["贝尔法斯特蓝", "Belizehole"], ["紫藤", "Wisteria"], ["午夜蓝", "Midnightblue"], ["阳橙", "Sunflower"], ["胡萝卜", "Carrot"], ["茜红", "<PERSON><PERSON><PERSON>"], ["云朵", "Clouds"], ["混凝土", "Concrete"], ["橙色", "Orange"], ["南瓜", "<PERSON><PERSON><PERSON>"], ["石榴红", "Pomegranate"], ["银色", "Silver"], ["石墨", "Asbestos"], ["暗色", "Dark"], ["无", "None"]]}, {"type": "field_dropdown", "name": "STATE", "options": [["关闭", "false"], ["开启", "true"]]}], "inputsInline": false, "output": "Number", "colour": "#03a9f4", "tooltip": "创建一个开关控件"}, {"type": "espui_slider", "message0": "创建滑条 文本 %1 颜色 %2 最小值 %3 最大值 %4 初始值 %5", "args0": [{"type": "input_value", "name": "TEXT", "check": "String"}, {"type": "field_dropdown", "name": "COLOR", "options": [["蓝绿色", "Turquoise"], ["翠绿色", "Emerald"], ["彼得河蓝", "Peterriver"], ["紫水晶", "Amethyst"], ["湿沥青", "<PERSON><PERSON><PERSON><PERSON>"], ["绿海", "Greensea"], ["海蓝宝石", "Nephritis"], ["贝尔法斯特蓝", "Belizehole"], ["紫藤", "Wisteria"], ["午夜蓝", "Midnightblue"], ["阳橙", "Sunflower"], ["胡萝卜", "Carrot"], ["茜红", "<PERSON><PERSON><PERSON>"], ["云朵", "Clouds"], ["混凝土", "Concrete"], ["橙色", "Orange"], ["南瓜", "<PERSON><PERSON><PERSON>"], ["石榴红", "Pomegranate"], ["银色", "Silver"], ["石墨", "Asbestos"], ["暗色", "Dark"], ["无", "None"]]}, {"type": "input_value", "name": "MIN", "check": "Number"}, {"type": "input_value", "name": "MAX", "check": "Number"}, {"type": "input_value", "name": "VALUE", "check": "Number"}], "inputsInline": false, "output": "Number", "colour": "#03a9f4", "tooltip": "创建一个滑条控件"}, {"type": "espui_text", "message0": "创建文本输入框 文本 %1 颜色 %2 初始值 %3", "args0": [{"type": "input_value", "name": "TEXT", "check": "String"}, {"type": "field_dropdown", "name": "COLOR", "options": [["蓝绿色", "Turquoise"], ["翠绿色", "Emerald"], ["彼得河蓝", "Peterriver"], ["紫水晶", "Amethyst"], ["湿沥青", "<PERSON><PERSON><PERSON><PERSON>"], ["绿海", "Greensea"], ["海蓝宝石", "Nephritis"], ["贝尔法斯特蓝", "Belizehole"], ["紫藤", "Wisteria"], ["午夜蓝", "Midnightblue"], ["阳橙", "Sunflower"], ["胡萝卜", "Carrot"], ["茜红", "<PERSON><PERSON><PERSON>"], ["云朵", "Clouds"], ["混凝土", "Concrete"], ["橙色", "Orange"], ["南瓜", "<PERSON><PERSON><PERSON>"], ["石榴红", "Pomegranate"], ["银色", "Silver"], ["石墨", "Asbestos"], ["暗色", "Dark"], ["无", "None"]]}, {"type": "input_value", "name": "VALUE", "check": "String"}], "inputsInline": false, "output": "Number", "colour": "#03a9f4", "tooltip": "创建一个文本输入框"}, {"type": "espui_number", "message0": "创建数字输入框 文本 %1 颜色 %2 最小值 %3 最大值 %4 初始值 %5", "args0": [{"type": "input_value", "name": "TEXT", "check": "String"}, {"type": "field_dropdown", "name": "COLOR", "options": [["蓝绿色", "Turquoise"], ["翠绿色", "Emerald"], ["彼得河蓝", "Peterriver"], ["紫水晶", "Amethyst"], ["湿沥青", "<PERSON><PERSON><PERSON><PERSON>"], ["绿海", "Greensea"], ["海蓝宝石", "Nephritis"], ["贝尔法斯特蓝", "Belizehole"], ["紫藤", "Wisteria"], ["午夜蓝", "Midnightblue"], ["阳橙", "Sunflower"], ["胡萝卜", "Carrot"], ["茜红", "<PERSON><PERSON><PERSON>"], ["云朵", "Clouds"], ["混凝土", "Concrete"], ["橙色", "Orange"], ["南瓜", "<PERSON><PERSON><PERSON>"], ["石榴红", "Pomegranate"], ["银色", "Silver"], ["石墨", "Asbestos"], ["暗色", "Dark"], ["无", "None"]]}, {"type": "input_value", "name": "MIN", "check": "Number"}, {"type": "input_value", "name": "MAX", "check": "Number"}, {"type": "input_value", "name": "VALUE", "check": "Number"}], "inputsInline": false, "output": "Number", "colour": "#03a9f4", "tooltip": "创建一个数字输入框"}, {"type": "espui_graph", "message0": "创建图表 文本 %1 颜色 %2", "args0": [{"type": "input_value", "name": "TEXT", "check": "String"}, {"type": "field_dropdown", "name": "COLOR", "options": [["蓝绿色", "Turquoise"], ["翠绿色", "Emerald"], ["彼得河蓝", "Peterriver"], ["紫水晶", "Amethyst"], ["湿沥青", "<PERSON><PERSON><PERSON><PERSON>"], ["绿海", "Greensea"], ["海蓝宝石", "Nephritis"], ["贝尔法斯特蓝", "Belizehole"], ["紫藤", "Wisteria"], ["午夜蓝", "Midnightblue"], ["阳橙", "Sunflower"], ["胡萝卜", "Carrot"], ["茜红", "<PERSON><PERSON><PERSON>"], ["云朵", "Clouds"], ["混凝土", "Concrete"], ["橙色", "Orange"], ["南瓜", "<PERSON><PERSON><PERSON>"], ["石榴红", "Pomegranate"], ["银色", "Silver"], ["石墨", "Asbestos"], ["暗色", "Dark"], ["无", "None"]]}], "inputsInline": true, "output": "Number", "colour": "#03a9f4", "tooltip": "创建一个图表控件"}, {"type": "espui_gauge", "message0": "创建仪表盘 文本 %1 颜色 %2 最小值 %3 最大值 %4 初始值 %5", "args0": [{"type": "input_value", "name": "TEXT", "check": "String"}, {"type": "field_dropdown", "name": "COLOR", "options": [["蓝绿色", "Turquoise"], ["翠绿色", "Emerald"], ["彼得河蓝", "Peterriver"], ["紫水晶", "Amethyst"], ["湿沥青", "<PERSON><PERSON><PERSON><PERSON>"], ["绿海", "Greensea"], ["海蓝宝石", "Nephritis"], ["贝尔法斯特蓝", "Belizehole"], ["紫藤", "Wisteria"], ["午夜蓝", "Midnightblue"], ["阳橙", "Sunflower"], ["胡萝卜", "Carrot"], ["茜红", "<PERSON><PERSON><PERSON>"], ["云朵", "Clouds"], ["混凝土", "Concrete"], ["橙色", "Orange"], ["南瓜", "<PERSON><PERSON><PERSON>"], ["石榴红", "Pomegranate"], ["银色", "Silver"], ["石墨", "Asbestos"], ["暗色", "Dark"], ["无", "None"]]}, {"type": "input_value", "name": "MIN", "check": "Number"}, {"type": "input_value", "name": "MAX", "check": "Number"}, {"type": "input_value", "name": "VALUE", "check": "Number"}], "inputsInline": false, "output": "Number", "colour": "#03a9f4", "tooltip": "创建一个仪表盘控件"}, {"type": "espui_update_control", "message0": "更新控件 ID %1 值 %2", "args0": [{"type": "input_value", "name": "CONTROL_ID", "check": "Number"}, {"type": "input_value", "name": "VALUE"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#03a9f4", "tooltip": "更新指定控件的值"}, {"type": "espui_update_label", "message0": "更新控件标签 ID %1 标签 %2", "args0": [{"type": "input_value", "name": "CONTROL_ID", "check": "Number"}, {"type": "input_value", "name": "LABEL", "check": "String"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#03a9f4", "tooltip": "更新指定控件的标签文本"}, {"type": "espui_on_event", "message0": "当控件事件触发时 控件ID %1 事件类型 %2 %3 执行 %4", "args0": [{"type": "input_value", "name": "CONTROL_ID", "check": "Number"}, {"type": "field_dropdown", "name": "EVENT_TYPE", "options": [["按下", "B_DOWN"], ["松开", "B_UP"], ["改变", "S_ACTIVE"]]}, {"type": "input_dummy"}, {"type": "input_statement", "name": "DO"}], "inputsInline": false, "previousStatement": null, "nextStatement": null, "colour": "#03a9f4", "tooltip": "设置控件事件回调函数"}, {"type": "espui_get_control_value", "message0": "获取控件值 ID %1", "args0": [{"type": "input_value", "name": "CONTROL_ID", "check": "Number"}], "inputsInline": true, "output": "String", "colour": "#03a9f4", "tooltip": "获取指定控件的当前值"}, {"type": "espui_add_graph_point", "message0": "添加图表数据点 图表ID %1 数值 %2", "args0": [{"type": "input_value", "name": "CONTROL_ID", "check": "Number"}, {"type": "input_value", "name": "VALUE", "check": "Number"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#03a9f4", "tooltip": "向指定图表添加一个数据点"}, {"type": "espui_clear_graph", "message0": "清空图表 图表ID %1", "args0": [{"type": "input_value", "name": "CONTROL_ID", "check": "Number"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#03a9f4", "tooltip": "清空指定图表的所有数据点"}, {"type": "espui_wifi_setup", "message0": "WiFi设置 SSID %1 密码 %2 模式 %3", "args0": [{"type": "input_value", "name": "SSID", "check": "String"}, {"type": "input_value", "name": "PASSWORD", "check": "String"}, {"type": "field_dropdown", "name": "MODE", "options": [["连接模式", "STA"], ["热点模式", "AP"]]}], "inputsInline": false, "previousStatement": null, "nextStatement": null, "colour": "#03a9f4", "tooltip": "配置WiFi连接或热点模式"}, {"type": "espui_wifi_status", "message0": "WiFi连接状态", "inputsInline": true, "output": "Boolean", "colour": "#03a9f4", "tooltip": "检查WiFi是否连接成功"}, {"type": "espui_get_ip", "message0": "获取IP地址", "inputsInline": true, "output": "String", "colour": "#03a9f4", "tooltip": "获取当前ESP设备的IP地址"}]