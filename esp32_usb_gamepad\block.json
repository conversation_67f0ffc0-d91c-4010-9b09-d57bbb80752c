[{"type": "gamepad_begin", "message0": "初始化游戏手柄", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#E91E63"}, {"type": "gamepad_press_button", "message0": "游戏手柄按下按键 %1", "args0": [{"type": "input_value", "name": "BUTTON", "check": "Number"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#E91E63"}, {"type": "gamepad_release_button", "message0": "游戏手柄释放按键 %1", "args0": [{"type": "input_value", "name": "BUTTON", "check": "Number"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#E91E63"}, {"type": "gamepad_left_stick", "message0": "游戏手柄左摇杆 X: %1 Y: %2", "args0": [{"type": "input_value", "name": "X", "check": "Number"}, {"type": "input_value", "name": "Y", "check": "Number"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#E91E63"}, {"type": "gamepad_right_stick", "message0": "游戏手柄右摇杆 X: %1 Y: %2", "args0": [{"type": "input_value", "name": "X", "check": "Number"}, {"type": "input_value", "name": "Y", "check": "Number"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#E91E63"}, {"type": "gamepad_left_trigger", "message0": "游戏手柄左扳机 %1", "args0": [{"type": "input_value", "name": "VALUE", "check": "Number"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#E91E63"}, {"type": "gamepad_right_trigger", "message0": "游戏手柄右扳机 %1", "args0": [{"type": "input_value", "name": "VALUE", "check": "Number"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#E91E63"}, {"type": "gamepad_hat", "message0": "游戏手柄方向键 %1", "args0": [{"type": "field_dropdown", "name": "DIRECTION", "options": [["中心", "HAT_CENTER"], ["上", "HAT_UP"], ["右上", "HAT_UP_RIGHT"], ["右", "HAT_RIGHT"], ["右下", "HAT_DOWN_RIGHT"], ["下", "HAT_DOWN"], ["左下", "HAT_DOWN_LEFT"], ["左", "HAT_LEFT"], ["左上", "HAT_UP_LEFT"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#E91E63"}, {"type": "gamepad_reset", "message0": "重置游戏手柄状态", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#E91E63"}]