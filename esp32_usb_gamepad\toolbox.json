{"kind": "category", "icon": "fa-light fa-gamepad-modern", "name": "USB游戏手柄", "contents": [{"kind": "block", "type": "gamepad_press_button", "inputs": {"BUTTON": {"shadow": {"type": "math_number", "fields": {"NUM": 1}}}}}, {"kind": "block", "type": "gamepad_release_button", "inputs": {"BUTTON": {"shadow": {"type": "math_number", "fields": {"NUM": 1}}}}}, {"kind": "block", "type": "gamepad_left_stick", "inputs": {"X": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}, "Y": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}}}, {"kind": "block", "type": "gamepad_right_stick", "inputs": {"X": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}, "Y": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}}}, {"kind": "block", "type": "gamepad_left_trigger", "inputs": {"VALUE": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}}}, {"kind": "block", "type": "gamepad_right_trigger", "inputs": {"VALUE": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}}}, {"kind": "block", "type": "gamepad_hat", "fields": {"DIRECTION": "HAT_CENTER"}}, {"kind": "block", "type": "gamepad_reset"}]}