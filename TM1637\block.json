[{"type": "tm1637_init", "message0": "初始化TM1637数码管 CLK引脚%1 DIO引脚%2", "args0": [{"type": "field_dropdown", "name": "CLK_PIN", "options": "${board.digitalPins}"}, {"type": "field_dropdown", "name": "DIO_PIN", "options": "${board.digitalPins}"}], "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "inputsInline": true}, {"type": "tm1637_print_number", "message0": "TM1637显示数字%1", "args0": [{"type": "input_value", "name": "NUMBER", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "inputsInline": true}, {"type": "tm1637_print_text", "message0": "TM1637显示文本%1", "args0": [{"type": "input_value", "name": "TEXT", "check": "String"}], "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "inputsInline": true}, {"type": "tm1637_clear", "message0": "TM1637清屏", "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "inputsInline": true}, {"type": "tm1637_set_brightness", "message0": "TM1637设置亮度%1", "args0": [{"type": "field_dropdown", "name": "BRIGHTNESS", "options": [["0%", "0"], ["12.5%", "12"], ["25%", "25"], ["37.5%", "37"], ["50%", "50"], ["62.5%", "62"], ["75%", "75"], ["87.5%", "87"], ["100%", "100"]]}], "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "inputsInline": true}, {"type": "tm1637_set_colon", "message0": "TM1637冒号显示%1", "args0": [{"type": "field_dropdown", "name": "COLON_STATE", "options": [["开启", "true"], ["关闭", "false"]]}], "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "inputsInline": true}, {"type": "tm1637_print_time", "message0": "TM1637显示时间 小时%1 分钟%2", "args0": [{"type": "input_value", "name": "HOUR", "check": "Number"}, {"type": "input_value", "name": "MINUTE", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "inputsInline": true}, {"type": "tm1637_blink", "message0": "TM1637闪烁显示 延时%1毫秒 重复%2次", "args0": [{"type": "field_number", "name": "DELAY", "value": 500, "min": 10, "max": 5000}, {"type": "field_number", "name": "REPEATS", "value": 5, "min": 1, "max": 50}], "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "inputsInline": true}]