[{"type": "esp32_i2c_begin", "message0": "初始化I2C通信 SDA引脚%1 SCL引脚%2 频率%3Hz", "args0": [{"type": "input_value", "name": "SDA_PIN", "check": "Number"}, {"type": "input_value", "name": "SCL_PIN", "check": "Number"}, {"type": "field_dropdown", "name": "FREQUENCY", "options": [["100000 (标准模式)", "100000"], ["400000 (快速模式)", "400000"], ["1000000 (快速模式+)", "1000000"]]}], "previousStatement": null, "nextStatement": null, "colour": "#FF9800", "tooltip": "初始化ESP32的I2C通信，设置SDA和SCL引脚以及通信频率"}, {"type": "esp32_i2c_begin_simple", "message0": "初始化I2C通信（使用默认引脚）", "previousStatement": null, "nextStatement": null, "colour": "#FF9800", "tooltip": "使用默认引脚初始化ESP32的I2C通信"}, {"type": "esp32_i2c_scan_devices", "message0": "扫描I2C设备", "output": "Number", "colour": "#FF9800", "tooltip": "扫描并返回发现的I2C设备数量"}, {"type": "esp32_i2c_begin_transmission", "message0": "开始向设备%1发送数据", "args0": [{"type": "input_value", "name": "ADDRESS", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#FF9800", "tooltip": "开始向指定I2C地址的设备发送数据"}, {"type": "esp32_i2c_write_byte", "message0": "写入字节数据%1", "args0": [{"type": "input_value", "name": "DATA", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#FF9800", "tooltip": "向I2C设备写入一个字节数据"}, {"type": "esp32_i2c_write_string", "message0": "写入字符串%1", "args0": [{"type": "input_value", "name": "DATA", "check": "String"}], "previousStatement": null, "nextStatement": null, "colour": "#FF9800", "tooltip": "向I2C设备写入字符串数据"}, {"type": "esp32_i2c_end_transmission", "message0": "结束数据发送", "output": "Number", "colour": "#FF9800", "tooltip": "结束I2C数据发送，返回错误代码（0表示成功）"}, {"type": "esp32_i2c_request_from", "message0": "从设备%1请求%2字节数据", "args0": [{"type": "input_value", "name": "ADDRESS", "check": "Number"}, {"type": "input_value", "name": "QUANTITY", "check": "Number"}], "output": "Number", "colour": "#FF9800", "tooltip": "从指定I2C设备请求指定字节数的数据"}, {"type": "esp32_i2c_available", "message0": "I2C可读取字节数", "output": "Number", "colour": "#FF9800", "tooltip": "返回I2C接收缓冲区中可读取的字节数"}, {"type": "esp32_i2c_read", "message0": "读取I2C数据", "output": "Number", "colour": "#FF9800", "tooltip": "从I2C接收缓冲区读取一个字节数据"}, {"type": "esp32_i2c_slave_begin", "message0": "初始化I2C从模式 地址%1 SDA引脚%2 SCL引脚%3", "args0": [{"type": "input_value", "name": "ADDRESS", "check": "Number"}, {"type": "input_value", "name": "SDA_PIN", "check": "Number"}, {"type": "input_value", "name": "SCL_PIN", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#9C27B0", "tooltip": "初始化ESP32的I2C从模式，设置从设备地址和引脚"}, {"type": "esp32_i2c_on_receive", "message0": "当接收到数据时 %1 %2", "args0": [{"type": "input_dummy"}, {"type": "input_statement", "name": "CALLBACK"}], "previousStatement": null, "nextStatement": null, "colour": "#9C27B0", "tooltip": "设置I2C从设备接收数据时的回调函数"}, {"type": "esp32_i2c_on_request", "message0": "当主设备请求数据时 %1 %2", "args0": [{"type": "input_dummy"}, {"type": "input_statement", "name": "CALLBACK"}], "previousStatement": null, "nextStatement": null, "colour": "#9C27B0", "tooltip": "设置I2C从设备响应主设备请求时的回调函数"}, {"type": "esp32_i2c_slave_print", "message0": "从设备发送字符串%1", "args0": [{"type": "input_value", "name": "DATA", "check": "String"}], "previousStatement": null, "nextStatement": null, "colour": "#9C27B0", "tooltip": "I2C从设备发送字符串数据给主设备"}, {"type": "esp32_i2c_write_to_device", "message0": "向设备%1写入数据%2", "args0": [{"type": "field_dropdown", "name": "ADDRESS", "options": [["0x3C (OLED显示屏)", "0x3C"], ["0x48 (ADS1115)", "0x48"], ["0x68 (MPU6050)", "0x68"], ["0x76 (BMP280)", "0x76"], ["0x77 (BMP280备用)", "0x77"], ["自定义地址", "CUSTOM"]]}, {"type": "input_value", "name": "DATA", "check": ["String", "Number"]}], "previousStatement": null, "nextStatement": null, "colour": "#FF9800", "tooltip": "向指定I2C设备写入数据的简化版本"}, {"type": "esp32_i2c_read_from_device", "message0": "从设备%1读取%2字节数据", "args0": [{"type": "field_dropdown", "name": "ADDRESS", "options": [["0x3C (OLED显示屏)", "0x3C"], ["0x48 (ADS1115)", "0x48"], ["0x68 (MPU6050)", "0x68"], ["0x76 (BMP280)", "0x76"], ["0x77 (BMP280备用)", "0x77"], ["自定义地址", "CUSTOM"]]}, {"type": "input_value", "name": "QUANTITY", "check": "Number"}], "output": "Array", "colour": "#FF9800", "tooltip": "从指定I2C设备读取指定字节数的数据"}]