{"kind": "category", "name": "SGP30气体传感器", "contents": [{"kind": "label", "text": "初始化和基本测量"}, {"kind": "block", "type": "sgp30_init"}, {"kind": "block", "type": "sgp30_measure"}, {"kind": "sep", "gap": "8"}, {"kind": "label", "text": "获取测量值"}, {"kind": "block", "type": "sgp30_get_tvoc"}, {"kind": "block", "type": "sgp30_get_eco2"}, {"kind": "sep", "gap": "8"}, {"kind": "label", "text": "原始数据测量"}, {"kind": "block", "type": "sgp30_measure_raw"}, {"kind": "block", "type": "sgp30_get_raw_h2"}, {"kind": "block", "type": "sgp30_get_raw_ethanol"}, {"kind": "sep", "gap": "8"}, {"kind": "label", "text": "高级功能"}, {"kind": "block", "type": "sgp30_set_humidity", "inputs": {"TEMPERATURE": {"shadow": {"type": "math_number", "fields": {"NUM": 22.0}}}, "HUMIDITY": {"shadow": {"type": "math_number", "fields": {"NUM": 45.0}}}}}, {"kind": "block", "type": "sgp30_get_baseline"}, {"kind": "block", "type": "sgp30_set_baseline", "inputs": {"ECO2_BASE": {"shadow": {"type": "math_number", "fields": {"NUM": 36456}}}, "TVOC_BASE": {"shadow": {"type": "math_number", "fields": {"NUM": 36673}}}}}, {"kind": "block", "type": "sgp30_get_serial"}]}