[{"type": "taojingchi_init", "message0": "初始化淘晶驰串口屏 RX引脚%1 TX引脚%2 波特率%3", "args0": [{"type": "input_value", "name": "RXPIN", "check": "Number"}, {"type": "input_value", "name": "TXPIN", "check": "Number"}, {"type": "field_dropdown", "name": "BAUD", "options": [["4800", "4800"], ["9600", "9600"], ["57600", "57600"], ["115200", "115200"]]}], "previousStatement": null, "nextStatement": null, "colour": "#AA278D", "inputsInline": true}, {"type": "taojingchi_backlight", "message0": "设置串口屏背光亮度%1 (0~100)", "args0": [{"type": "input_value", "name": "BRIGHTNESS", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#AA278D", "inputsInline": true}, {"type": "taojingchi_display_page", "message0": "切换到页面%1 (0~16)", "args0": [{"type": "input_value", "name": "PAGE", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#AA278D", "inputsInline": true}, {"type": "taojingchi_set_var", "message0": "设置变量%1的值为%2", "args0": [{"type": "field_dropdown", "name": "VARNAME", "options": [["data01", "data01"], ["data02", "data02"], ["data03", "data03"], ["data04", "data04"], ["data05", "data05"], ["data06", "data06"], ["data07", "data07"], ["data08", "data08"], ["data09", "data09"], ["data10", "data10"], ["data11", "data11"], ["data12", "data12"], ["data13", "data13"], ["data14", "data14"], ["data15", "data15"], ["data16", "data16"]]}, {"type": "input_value", "name": "VALUE", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#AA278D", "inputsInline": true}, {"type": "taojingchi_display_image", "message0": "页面%1的图片p%2显示图片ID%3", "args0": [{"type": "input_value", "name": "PAGE", "check": "Number"}, {"type": "input_value", "name": "IMG", "check": "Number"}, {"type": "input_value", "name": "ID", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#AA278D", "inputsInline": true}, {"type": "taojing<PERSON>_send_command", "message0": "发送命令%1", "args0": [{"type": "input_value", "name": "COMMAND", "check": "String"}], "previousStatement": null, "nextStatement": null, "colour": "#AA278D", "inputsInline": true}, {"type": "taojingchi_send_data", "message0": "发送数据%1=%2", "args0": [{"type": "input_value", "name": "COMMAND", "check": "String"}, {"type": "input_value", "name": "VALUE", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#AA278D", "inputsInline": true}]