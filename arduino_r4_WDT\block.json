[{"type": "wdt_begin", "message0": "初始化看门狗定时器 超时时间 %1 毫秒", "args0": [{"type": "field_number", "name": "TIMEOUT", "value": 2000, "min": 100, "max": 128000}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00979D"}, {"type": "wdt_refresh", "message0": "刷新看门狗定时器", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00979D"}, {"type": "wdt_gettimeout", "message0": "获取看门狗定时器超时时间", "inputsInline": true, "output": "Number", "colour": "#00979D"}]