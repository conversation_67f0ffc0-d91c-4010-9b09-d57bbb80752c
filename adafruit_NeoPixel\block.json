[{"type": "neopixel_init", "message0": "初始化NeoPixel灯带 引脚 %1 LED数量 %2 LED类型 %3", "args0": [{"type": "field_dropdown", "name": "PIN", "options": "${board.digitalPins}"}, {"type": "field_number", "name": "NUM", "value": 16, "min": 1, "max": 1000}, {"type": "field_dropdown", "name": "TYPE", "options": [["RGB (GRB)", "NEO_GRB + NEO_KHZ800"], ["RGB (RGB)", "NEO_RGB + NEO_KHZ800"], ["RGBW", "NEO_RGBW + NEO_KHZ800"], ["RGB (400 KHz)", "NEO_GRB + NEO_KHZ400"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 65, "tooltip": "初始化NeoPixel LED灯带"}, {"type": "neopixel_begin", "message0": "开始NeoPixel", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 65, "tooltip": "开始NeoPixel通信"}, {"type": "neopixel_show", "message0": "显示NeoPixel", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 65, "tooltip": "更新NeoPixel显示"}, {"type": "neopixel_set_brightness", "message0": "设置NeoPixel亮度 %1", "args0": [{"type": "field_number", "name": "BRIGHTNESS", "value": 50, "min": 0, "max": 255}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 65, "tooltip": "设置NeoPixel灯带亮度(0-255)"}, {"type": "neopixel_set_pixel_color", "message0": "设置LED %1 颜色 红 %2 绿 %3 蓝 %4", "args0": [{"type": "field_number", "name": "PIXEL", "value": 0, "min": 0}, {"type": "field_number", "name": "RED", "value": 255, "min": 0, "max": 255}, {"type": "field_number", "name": "GREEN", "value": 0, "min": 0, "max": 255}, {"type": "field_number", "name": "BLUE", "value": 0, "min": 0, "max": 255}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 65, "tooltip": "设置单个LED颜色"}, {"type": "neopixel_fill", "message0": "填充所有LED 颜色 红 %1 绿 %2 蓝 %3", "args0": [{"type": "field_number", "name": "RED", "value": 255, "min": 0, "max": 255}, {"type": "field_number", "name": "GREEN", "value": 0, "min": 0, "max": 255}, {"type": "field_number", "name": "BLUE", "value": 0, "min": 0, "max": 255}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 65, "tooltip": "用同一颜色填充所有LED"}, {"type": "neopixel_clear", "message0": "清除所有NeoPixel", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 65, "tooltip": "关闭所有LED"}, {"type": "neopixel_rainbow", "message0": "显示彩虹效果 延迟(ms) %1", "args0": [{"type": "field_number", "name": "DELAY", "value": 20, "min": 0}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 65, "tooltip": "在LED灯带上显示彩虹效果"}, {"type": "neopixel_color_wipe", "message0": "逐个LED填充 颜色 红 %1 绿 %2 蓝 %3 延迟(ms) %4", "args0": [{"type": "field_number", "name": "RED", "value": 255, "min": 0, "max": 255}, {"type": "field_number", "name": "GREEN", "value": 0, "min": 0, "max": 255}, {"type": "field_number", "name": "BLUE", "value": 0, "min": 0, "max": 255}, {"type": "field_number", "name": "DELAY", "value": 50, "min": 0}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 65, "tooltip": "逐个LED填充颜色"}, {"type": "neopixel_theater_chase", "message0": "追逐灯效果 颜色 红 %1 绿 %2 蓝 %3 延迟(ms) %4", "args0": [{"type": "field_number", "name": "RED", "value": 255, "min": 0, "max": 255}, {"type": "field_number", "name": "GREEN", "value": 0, "min": 0, "max": 255}, {"type": "field_number", "name": "BLUE", "value": 0, "min": 0, "max": 255}, {"type": "field_number", "name": "DELAY", "value": 50, "min": 0}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 65, "tooltip": "显示追逐灯效果"}, {"type": "neopixel_theater_chase_rainbow", "message0": "彩虹追逐灯效果 延迟(ms) %1", "args0": [{"type": "field_number", "name": "DELAY", "value": 50, "min": 0}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": 65, "tooltip": "显示彩虹追逐灯效果"}]