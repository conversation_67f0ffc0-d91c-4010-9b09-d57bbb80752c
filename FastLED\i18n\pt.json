{"toolbox_name": "FastLED", "fastled_init": {"message0": "Inicializar fita RGB Pino %1 Tipo %2 Número de LEDs %3", "args0": [null, {"options": [["WS2812B", "WS2812B"], ["WS2812", "WS2812"], ["WS2811", "WS2811"], ["NEOPIXEL", "NEOPIXEL"], ["WS2801", "WS2801"], ["LPD8806", "LPD8806"], ["APA102", "APA102"]]}, null]}, "fastled_set_pixel": {"message0": "Definir fita RGB Pino %1 Índice %2 Cor %3"}, "fastled_show": {"message0": "Atualizar fita RGB"}, "fastled_clear": {"message0": "Limpar fita RGB Pino %1"}, "fastled_brightness": {"message0": "Definir brilho %1"}, "fastled_rgb": {"message0": "Cor RGB R %1 G %2 B %3"}, "fastled_preset_color": {"message0": "Cor %1"}, "fastled_fill_solid": {"message0": "Preencher todos os LEDs Pino %1 Cor %2"}, "fastled_hsv": {"message0": "Cor HSV H %1 S %2 V %3"}, "fastled_rainbow": {"message0": "Efeito arco-íris <PERSON> %1 Matiz inicial %2 Incremento %3"}, "fastled_fire_effect": {"message0": "Efeito fogo Pino %1 Intensidade %2 Velocidade de resfriamento %3"}, "fastled_meteor": {"message0": "Efeito meteoro Pino %1 Cor %2 Tamanho do meteoro %3 Desvanecimento da cauda %4 Velocidade %5"}, "fastled_palette_cycle": {"message0": "Efeito ciclo de paleta Pino %1 Paleta %2 Velocidade %3", "args0": [null, {"options": [["Arco-íris", "RainbowColors_p"], ["<PERSON><PERSON>", "LavaColors_p"], ["Nuvens", "CloudColors_p"], ["Oceano", "OceanColors_p"], ["Flores<PERSON>", "ForestColors_p"], ["Festa", "PartyColors_p"], ["<PERSON><PERSON>", "HeatColors_p"]]}, null]}, "fastled_breathing": {"message0": "Efeito de respiração Pino %1 Cor %2 Velocidade %3"}, "fastled_twinkle": {"message0": "Efeito cintilante Pino %1 Número de brilhos %2 Fundo %3 Cor do brilho %4"}}