{"toolbox_name": "Custom Code", "custom_code": {"message0": "Custom code %1"}, "custom_macro": {"message0": "Define macro %1 as %2"}, "custom_library": {"message0": "Include library %1"}, "custom_variable": {"message0": "Define variable type %1 name %2 initial value %3", "args0": [{"options": [["Integer", "int"], ["Long Integer", "long*"], ["Float", "float"], ["Double", "double"], ["Unsigned Integer", "unsigned char"], ["Unsigned Long Integer", "unsigned char"], ["Boolean", "bool"], ["Character", "char"], ["String", "string"]]}, null, null]}, "custom_function": {"message0": "Function definition %1 %2 return type %3 parameter list %4 %5 function body %6", "args0": [null, null, {"options": [["Integer", "int"], ["Long Integer", "long*"], ["Float", "float"], ["Double", "double"], ["Unsigned Integer", "unsigned char"], ["Unsigned Long Integer", "unsigned char"], ["Boolean", "bool"], ["Character", "char"], ["String", "string"]]}, null, null, null]}}