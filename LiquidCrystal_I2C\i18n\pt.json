{"toolbox_name": "LCD1602 I2C", "lcd_i2c_init": {"message0": "Inicializar display LCD I2C endereço %1 colunas %2 linhas %3", "args0": [[["0x27", "0x27"], ["0x26", "0x26"], ["0x25", "0x25"], ["0x24", "0x24"], ["0x23", "0x23"], ["0x22", "0x22"], ["0x21", "0x21"], ["0x20", "0x20"]], null, null]}, "lcd_i2c_clear": {"message0": "Limpar display LCD"}, "lcd_i2c_set_cursor": {"message0": "Mover cursor do LCD para coluna %1 linha %2"}, "lcd_i2c_print": {"message0": "Exibir no LCD %1"}, "lcd_i2c_print_position": {"message0": "Exibir no LCD na coluna %1 linha %2 %3"}, "lcd_i2c_backlight_on": {"message0": "Ligar luz de fundo do LCD"}, "lcd_i2c_backlight_off": {"message0": "Desligar luz de fundo do LCD"}, "lcd_i2c_custom_char": {"message0": "Caractere personalizado %1 número %2", "args0": [null, ["0", "0"], ["1", "1"], ["2", "2"], ["3", "3"], ["4", "4"], ["5", "5"], ["6", "6"], ["7", "7"]]}}