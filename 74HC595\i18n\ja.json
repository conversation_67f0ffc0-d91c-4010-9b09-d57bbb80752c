{"toolbox_name": "シフトレジスタ", "74hc595_create": {"message0": "74HC595 %1 を初期化 数量%2 data:%3  clock:%4 latch:%5", "args0": [null, null, {"options": "${board.digitalPins}"}, {"options": "${board.digitalPins}"}, {"options": "${board.digitalPins}"}]}, "74hc595_set": {"message0": "74HC595 %1 のピン %2 を %3 に設定", "args0": [null, {"options": "${board.digitalPins}"}, {"options": [["HIGH", "HIGH"], ["LOW", "LOW"]]}]}, "74hc595_setAll": {"message0": "74HC595 %1 の全ピンを%2に設定", "args0": [null, {"options": [["HIGH", "High"], ["LOW", "Low"]]}]}, "74hc595_setAllBin": {"message0": "74HC595%1 出力レベルを配列%2[]で設定", "args0": [null, null]}, "74hc595_getstate": {"message0": "74HC595%1 の出力%2番ピンの状態を取得", "args0": [null, null]}}