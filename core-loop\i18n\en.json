{"toolbox_name": "Loop", "arduino_setup": {"message0": "▶️Setup %1"}, "arduino_loop": {"message0": "🔁Loop %1"}, "controls_repeat_ext": {"message0": "Repeat %1 times", "message1": "Do %1"}, "controls_repeat": {"message0": "Repeat %1 times", "message1": "Do %1"}, "controls_whileUntil": {"message0": "%1 %2", "args0": [{"options": [["Repeat while condition is met", "WHILE"], ["Repeat until condition is met", "UNTIL"]]}], "message1": "Do %1"}, "controls_for": {"message0": "Set variable %1 from %2 to %3, increment by %4", "message1": "Run %1"}, "controls_flow_statements": {"message0": "%1", "args0": [{"options": [["Break loop", "BREAK"], ["Continue next iteration", "CONTINUE"]]}]}, "controls_whileForever": {"message0": "🔁 Forever loop %1"}}