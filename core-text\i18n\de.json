{"toolbox_name": "Text", "string_add_string": {"message0": "String(%1) + String(%2)"}, "string_charAt": {"message0": "%1's %2. <PERSON><PERSON><PERSON><PERSON>"}, "string_length": {"message0": "Anzahl der Zeichen von %1"}, "string_indexOf": {"message0": "Enthält %1 %2?"}, "string_substring": {"message0": "%1 <PERSON><PERSON><PERSON> von %2 %3 bis %4 %5 erhalten", "args0": [null, {"options": [["dem", "0"], ["dem letzten", "1"]]}, null, {"options": [["dem", "0"], ["dem letzten", "1"]]}, null]}, "string_find_str": {"message0": "Finde %1 in %2 an der %3 Position", "args0": [null, null, {"options": [["<PERSON><PERSON><PERSON>", "indexOf"], ["Letzte", "lastIndexOf"]]}]}, "string_to": {"message0": "Zeichenkette %1 nach %2 konvertieren", "args0": [null, {"options": [["<PERSON><PERSON><PERSON><PERSON>", "toInt"], ["<PERSON><PERSON><PERSON><PERSON>", "toFloat"]]}]}, "number_to": {"message0": "Zahl %1 in ASCII-Zeichenkette umwandeln"}, "toascii": {"message0": "Zeichen %1 in ASCII-Wert umwandeln"}, "number_to_string": {"message0": "Zahl %1 in Zeichenkette umwandeln"}, "text": {"message0": "%1"}, "text_join": {"message0": ""}, "text_create_join_container": {"message0": "%{BKY_TEXT_CREATE_JOIN_TITLE_JOIN} %1 %2"}, "text_create_join_item": {"message0": "%{BKY_TEXT_CREATE_JOIN_ITEM_TITLE_ITEM}"}, "text_append": {"message0": "Text %2 nach %1 anhängen"}, "text_length": {"message0": "<PERSON><PERSON><PERSON> von %1"}, "text_isEmpty": {"message0": "%1 ist leer"}, "text_indexOf": {"message0": "Im Text %1 %2 %3", "args0": [null, {"options": [["erstes Vorkommen des Textes finden", "FIRST"], ["letztes Vorkommen des Textes finden", "LAST"]]}, null]}, "text_charAt": {"message0": "Im Text %1 %2", "args0": [null, {"options": [["Zeichen # abrufen", "FROM_START"], ["Zeichen vom Ende # abrufen", "FROM_END"], ["er<PERSON><PERSON> Zeichen abrufen", "FIRST"], ["letztes Zeichen abrufen", "LAST"], ["zufälliges Zeichen abrufen", "RANDOM"]]}]}, "tt_getSubstring": {"message0": "Teilstring aus Text %1 erhalten", "message1": "%1 %2", "args1": [{"options": [["ab <PERSON><PERSON><PERSON> #", "FROM_START"], ["ab # von hinten", "FROM_END"], ["ab erstem Zeichen", "FIRST"]]}, null], "message2": "%1 %2", "args2": [{"options": [["bis Zeichen #", "FROM_START"], ["bis # von hinten", "FROM_END"], ["bis letztem Zeichen", "LAST"]]}, null]}, "text_changeCase": {"message0": "%1 %2", "args0": [{"options": [["in Großbuchstaben umwandeln", "UPPERCASE"], ["in Kleinbuchstaben umwandeln", "LOWERCASE"], ["in Titel Großbuchstaben umwandeln", "TITLECASE"]]}, null]}, "text_trim": {"message0": "%1 trim %2", "args0": [{"options": [["<PERSON><PERSON><PERSON><PERSON> von beiden Seiten entfernen", "BOTH"], ["<PERSON><PERSON><PERSON><PERSON> von links entfernen", "LEFT"], ["<PERSON><PERSON><PERSON><PERSON> von rechts entfernen", "RIGHT"]]}, null]}, "text_count": {"message0": "Vorkommen von %1 in %2 zählen"}, "text_replace": {"message0": "Ersetze %2 durch %3 in %1"}, "text_reverse": {"message0": "Text %1 umkehren"}}