{"name": "@aily-project/lib-arduino-freertos", "nickname": "Arduino freeRTOS", "version": "1.0.0", "description": "适用于Arduino UNO R4的freeRTOS支持库", "main": "block.json", "keywords": ["a<PERSON><PERSON><PERSON>", "freertos", "rtos", "blockly", "multitasking", "real-time"], "author": "aily-project", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ailyProject/aily-blockly-libraries.git"}, "dependencies": {}, "devDependencies": {}, "peerDependencies": {}, "files": ["block.json", "generator.js", "toolbox.json", "readme.md"], "compatibility": {"core": ["renesas_uno:minima", "renesas_uno:unor4wifi"], "voltage": [3.3, 5]}}