{"toolbox_name": "LCD1602 I2C", "lcd_i2c_init": {"message0": "Инициализация LCD I2C дисплея адрес %1 столбцов %2 строк %3", "args0": [[["0x27", "0x27"], ["0x26", "0x26"], ["0x25", "0x25"], ["0x24", "0x24"], ["0x23", "0x23"], ["0x22", "0x22"], ["0x21", "0x21"], ["0x20", "0x20"]], null, null]}, "lcd_i2c_clear": {"message0": "Очистить LCD дисплей"}, "lcd_i2c_set_cursor": {"message0": "Установить курсор LCD в столбец %1 строку %2"}, "lcd_i2c_print": {"message0": "Вывести на LCD %1"}, "lcd_i2c_print_position": {"message0": "LCD в столбец %1 строку %2 вывести %3"}, "lcd_i2c_backlight_on": {"message0": "Включить подсветку LCD"}, "lcd_i2c_backlight_off": {"message0": "Выключить подсветку LCD"}, "lcd_i2c_custom_char": {"message0": "Пользовательский символ %1 номер %2", "args0": [null, ["0", "0"], ["1", "1"], ["2", "2"], ["3", "3"], ["4", "4"], ["5", "5"], ["6", "6"], ["7", "7"]]}}