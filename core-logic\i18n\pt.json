{"toolbox_name": "Lógica", "controls_if": {"message0": "🔀Se %1", "message1": "Então faça %1"}, "controls_ifelse": {"message0": "🔀Se %1", "message1": "Faça %1", "message2": "Senão %1"}, "logic_compare": {"message0": "%1 %2 %3", "args0": [null, {"options": [["==", "EQ"], ["!=", "NEQ"], ["<", "LT"], [">", "GT"], [">=", "GTE"], ["<=", "LTE"]]}, null]}, "logic_operation": {"message0": "%1 %2 %3", "args0": [null, {"options": [["E", "AND"], ["OU", "OR"]]}, null]}, "logic_negate": {"message0": "NÃO %1"}, "logic_boolean": {"message0": "%1", "args0": [{"options": [["<PERSON><PERSON><PERSON><PERSON>", "true"], ["<PERSON><PERSON><PERSON>", "false"]]}]}, "logic_ternary": {"message0": "Afirmar %1", "message1": "Se verdadeiro %1", "message2": "Se falso %1"}}