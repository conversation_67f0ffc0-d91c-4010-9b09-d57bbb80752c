{"name": "@aily-project/lib-esp32_encoder", "nickname": "ESP32旋转编码器库", "author": "<PERSON>Jumper", "description": "ESP32旋转编码器驱动库适用于esp32，支持I2C通信，提供旋转编码器数据获取功能。", "version": "0.0.1", "compatibility": {"core": ["esp32:esp", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "ESP32Encoder", "旋转编码器", "编码器", "传感器"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "openjumper"}