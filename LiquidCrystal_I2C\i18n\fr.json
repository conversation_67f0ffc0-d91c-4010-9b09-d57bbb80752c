{"toolbox_name": "LCD1602 I2C", "lcd_i2c_init": {"message0": "Initialiser l'afficheur LCD I2C Adresse %1 Colonnes %2 Lignes %3", "args0": [[["0x27", "0x27"], ["0x26", "0x26"], ["0x25", "0x25"], ["0x24", "0x24"], ["0x23", "0x23"], ["0x22", "0x22"], ["0x21", "0x21"], ["0x20", "0x20"]], null, null]}, "lcd_i2c_clear": {"message0": "Effacer l'affichage LCD"}, "lcd_i2c_set_cursor": {"message0": "Positionner le curseur LCD à la colonne %1 ligne %2"}, "lcd_i2c_print": {"message0": "Afficher sur LCD %1"}, "lcd_i2c_print_position": {"message0": "Afficher sur LCD à la colonne %1 ligne %2 : %3"}, "lcd_i2c_backlight_on": {"message0": "Allumer le rétroéclairage LCD"}, "lcd_i2c_backlight_off": {"message0": "Éteindre le rétroéclairage LCD"}, "lcd_i2c_custom_char": {"message0": "Caractère personnalisé %1 numéro %2", "args0": [null, ["0", "0"], ["1", "1"], ["2", "2"], ["3", "3"], ["4", "4"], ["5", "5"], ["6", "6"], ["7", "7"]]}}