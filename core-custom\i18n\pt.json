{"toolbox_name": "<PERSON><PERSON><PERSON>", "custom_code": {"message0": "Código personalizado %1"}, "custom_macro": {"message0": "Definir macro %1 como %2"}, "custom_library": {"message0": "Incluir biblioteca %1"}, "custom_variable": {"message0": "Definir variável Tipo %1 Nome %2 Valor inicial %3", "args0": [{"options": [["Inteiro", "int"], ["<PERSON><PERSON>", "long*"], ["Ponto flutuante", "float"], ["<PERSON><PERSON><PERSON> pre<PERSON>", "double"], ["<PERSON>teiro sem sinal", "unsigned char"], ["<PERSON>o sem sinal", "unsigned char"], ["<PERSON><PERSON><PERSON>", "bool"], ["Caractere", "char"], ["String", "string"]]}, null, null]}, "custom_function": {"message0": "Definir função %1 %2 Tipo de retorno %3 Lista de parâmetros %4 %5 Corpo da função %6", "args0": [null, null, {"options": [["Inteiro", "int"], ["<PERSON><PERSON>", "long*"], ["Ponto flutuante", "float"], ["<PERSON><PERSON><PERSON> pre<PERSON>", "double"], ["<PERSON>teiro sem sinal", "unsigned char"], ["<PERSON>o sem sinal", "unsigned char"], ["<PERSON><PERSON><PERSON>", "bool"], ["Caractere", "char"], ["String", "string"]]}, null, null, null]}}