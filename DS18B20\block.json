[{"type": "ds18b20_init_pin", "message0": "初始化引脚 %1 的 DS18B20 传感器", "args0": [{"type": "input_value", "name": "PIN", "check": "Number"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#FF6B35"}, {"type": "ds18b20_read_temperature_c_pin", "message0": "读取引脚 %1 DS18B20 温度 (℃)", "args0": [{"type": "input_value", "name": "PIN", "check": "Number"}], "inputsInline": true, "output": "Number", "colour": "#FF6B35"}, {"type": "ds18b20_read_temperature_f_pin", "message0": "读取引脚 %1 DS18B20 温度 (℉)", "args0": [{"type": "input_value", "name": "PIN", "check": "Number"}], "inputsInline": true, "output": "Number", "colour": "#FF6B35"}, {"type": "ds18b20_get_device_count_pin", "message0": "获取引脚 %1 总线上 DS18B20 设备数量", "args0": [{"type": "input_value", "name": "PIN", "check": "Number"}], "inputsInline": true, "output": "Number", "colour": "#FF6B35"}, {"type": "ds18b20_read_temperature_by_index_pin", "message0": "读取引脚 %1 第 %2 个 DS18B20 温度 (℃)", "args0": [{"type": "input_value", "name": "PIN", "check": "Number"}, {"type": "input_value", "name": "INDEX", "check": "Number"}], "inputsInline": true, "output": "Number", "colour": "#FF6B35"}]