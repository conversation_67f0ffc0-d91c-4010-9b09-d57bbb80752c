[{"type": "tm16xx_init", "message0": "初始化%1数码管 DIO引脚%2 CLK引脚%3 STB引脚%4 数字位数%5", "args0": [{"type": "field_dropdown", "name": "CHIP_TYPE", "options": [["TM1637", "TM1637"], ["TM1638", "TM1638"], ["TM1640", "TM1640"], ["TM1650", "TM1650"], ["TM1668", "TM1668"]]}, {"type": "field_dropdown", "name": "DIO_PIN", "options": "${board.digitalPins}"}, {"type": "field_dropdown", "name": "CLK_PIN", "options": "${board.digitalPins}"}, {"type": "field_dropdown", "name": "STB_PIN", "options": "${board.digitalPins}"}, {"type": "field_number", "name": "DIGITS", "value": 8, "min": 1, "max": 16}], "previousStatement": null, "nextStatement": null, "colour": "#FF6600", "inputsInline": true, "extensions": ["tm16xx_init_extension"]}, {"type": "tm16xx_simple_init", "message0": "初始化%1数码管 DIO引脚%2 CLK引脚%3", "args0": [{"type": "field_dropdown", "name": "CHIP_TYPE", "options": [["TM1637", "TM1637"], ["TM1640", "TM1640"], ["TM1650", "TM1650"]]}, {"type": "field_dropdown", "name": "DIO_PIN", "options": "${board.digitalPins}"}, {"type": "field_dropdown", "name": "CLK_PIN", "options": "${board.digitalPins}"}], "previousStatement": null, "nextStatement": null, "colour": "#FF6600", "inputsInline": true}, {"type": "tm16xx_display_string", "message0": "显示文本%1", "args0": [{"type": "input_value", "name": "TEXT", "check": "String"}], "previousStatement": null, "nextStatement": null, "colour": "#FF6600", "inputsInline": true}, {"type": "tm16xx_display_number", "message0": "显示数字%1 小数点位置%2", "args0": [{"type": "input_value", "name": "NUMBER", "check": "Number"}, {"type": "field_dropdown", "name": "DOT_POSITION", "options": [["无", "0"], ["第1位", "1"], ["第2位", "2"], ["第3位", "3"], ["第4位", "4"], ["第5位", "5"], ["第6位", "6"], ["第7位", "7"], ["第8位", "8"]]}], "previousStatement": null, "nextStatement": null, "colour": "#FF6600", "inputsInline": true}, {"type": "tm16xx_clear_display", "message0": "清空显示", "previousStatement": null, "nextStatement": null, "colour": "#FF6600"}, {"type": "tm16xx_set_brightness", "message0": "设置亮度%1", "args0": [{"type": "field_dropdown", "name": "BRIGHTNESS", "options": [["最暗(0)", "0"], ["1", "1"], ["2", "2"], ["3", "3"], ["4", "4"], ["5", "5"], ["6", "6"], ["最亮(7)", "7"]]}], "previousStatement": null, "nextStatement": null, "colour": "#FF6600", "inputsInline": true}, {"type": "tm16xx_set_segment", "message0": "设置第%1位显示段码%2", "args0": [{"type": "field_number", "name": "POSITION", "value": 0, "min": 0, "max": 15}, {"type": "input_value", "name": "SEGMENTS", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#FF6600", "inputsInline": true}, {"type": "tm16xx_get_buttons", "message0": "获取按键状态", "output": "Number", "colour": "#FF6600"}, {"type": "tm16xx_is_button_pressed", "message0": "按键%1被按下", "args0": [{"type": "field_number", "name": "BUTTON", "value": 1, "min": 1, "max": 8}], "output": "Boolean", "colour": "#FF6600", "inputsInline": true}, {"type": "tm16xx_display_time", "message0": "显示时间 时%1 分%2 显示冒号%3", "args0": [{"type": "input_value", "name": "HOUR", "check": "Number"}, {"type": "input_value", "name": "MINUTE", "check": "Number"}, {"type": "field_checkbox", "name": "SHOW_COLON", "checked": true}], "previousStatement": null, "nextStatement": null, "colour": "#FF6600", "inputsInline": true}]