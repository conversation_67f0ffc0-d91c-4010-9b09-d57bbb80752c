[{"type": "tcs34725_init", "message0": "TCS34725 %1 颜色识别初始化", "args0": [{"type": "field_variable", "name": "TCS34725NAME", "variable": "tcs", "variableTypes": ["tcs1"], "defaultType": "tcs1"}], "previousStatement": null, "nextStatement": null, "colour": "#4C97FF", "inputsInline": false}, {"type": "tcs34725_led_ctrl", "message0": "颜色识别 %1 LED灯 %2", "args0": [{"type": "field_variable", "name": "TCS34725NAME", "variable": "tcs", "variableTypes": ["tcs1"], "defaultType": "tcs1"}, {"type": "field_dropdown", "name": "TCSLEDSTATE", "options": [["打开", "false"], ["关闭", "true"]]}], "previousStatement": null, "nextStatement": null, "colour": "#4C97FF", "inputsInline": true}, {"type": "tcs34725_get_rgb", "message0": "颜色识别 %1 获取 RGB", "args0": [{"type": "field_variable", "name": "TCS34725NAME", "variable": "tcs", "variableTypes": ["tcs1"], "defaultType": "tcs1"}], "previousStatement": null, "nextStatement": null, "colour": "#4C97FF", "inputsInline": true}, {"type": "tcs34725_rgb_value", "message0": "颜色识别RGB值 %1", "args0": [{"type": "field_dropdown", "name": "TCSRGBVALUE", "options": [["红", "red"], ["绿", "green"], ["蓝", "blue"]]}], "output": "Any", "colour": "#4C97FF", "inputsInline": true}]