[{"type": "ahtx0_begin", "message0": "初始化AHT温湿度传感器", "previousStatement": null, "nextStatement": null, "colour": "#FF6600"}, {"type": "ahtx0_read", "message0": "读取AHT温湿度数据", "previousStatement": null, "nextStatement": null, "colour": "#FF6600"}, {"type": "ahtx0_get_temperature", "message0": "温度", "output": "Number", "colour": "#FF6600"}, {"type": "ahtx0_get_humidity", "message0": "湿度", "output": "Number", "colour": "#FF6600"}]