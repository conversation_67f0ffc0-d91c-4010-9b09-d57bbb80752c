[{"type": "bmi270_init_i2c", "message0": "BMI270 I2C初始化 使用 %1 地址 %2", "args0": [{"type": "field_dropdown", "name": "WIRE_OPTION", "options": [["Wire1 (SDA:8, SCL:9)", "WIRE1"], ["Wire2 (SDA:4, SCL:5)", "WIRE2"]]}, {"type": "field_input", "name": "ADDRESS", "text": "BMI2_I2C_PRIM_ADDR"}], "previousStatement": null, "nextStatement": null, "colour": "#FF5722"}, {"type": "bmi270_get_sensor_data", "message0": "获取BMI270传感器数据", "previousStatement": null, "nextStatement": null, "colour": "#FF5722"}, {"type": "bmi270_get_accel_data", "message0": "获取加速度数据 %1", "args0": [{"type": "field_dropdown", "name": "AXIS", "options": [["X", "X"], ["Y", "Y"], ["Z", "Z"]]}], "output": "Number", "colour": "#FF5722"}, {"type": "bmi270_get_gyro_data", "message0": "获取陀螺仪数据 %1", "args0": [{"type": "field_dropdown", "name": "AXIS", "options": [["X", "X"], ["Y", "Y"], ["Z", "Z"]]}], "output": "Number", "colour": "#FF5722"}, {"type": "bmi270_perform_accel_offset_calibration", "message0": "执行加速度偏差校准（轴 %1）", "args0": [{"type": "field_dropdown", "name": "AXIS", "options": [["X", "X"], ["Y", "Y"], ["Z", "Z"]]}], "previousStatement": null, "nextStatement": null, "colour": "#FF5722"}, {"type": "bmi270_perform_gyro_offset_calibration", "message0": "执行陀螺仪偏差校准", "previousStatement": null, "nextStatement": null, "colour": "#FF5722"}]