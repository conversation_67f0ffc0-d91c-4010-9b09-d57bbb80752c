{"toolbox_name": "串口", "serial_begin": {"message0": "初始化串口%1 設定波特率為%2"}, "serial_available": {"message0": "串口%1緩衝區有數據"}, "serial_read": {"message0": "讀取串口%1數據 %2", "args0": [null, {"options": [["讀取", "read"], ["窺視", "peek"], ["解析整數", "parseInt"], ["解析浮點數", "parseFloat"]]}]}, "serial_print": {"message0": "串口%1輸出%2"}, "serial_println": {"message0": "串口%1輸出%2並換行"}, "serial_write": {"message0": "串口%1輸出原始數據%2"}, "serial_read_string": {"message0": "讀取串口%1字符串"}}