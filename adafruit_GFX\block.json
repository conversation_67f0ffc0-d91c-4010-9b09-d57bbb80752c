[{"type": "tft_init", "message0": "初始化TFT屏幕 型号 %1", "args0": [{"type": "field_dropdown", "name": "MODEL", "options": [["ST7735", "ST7735"], ["ST7789", "ST7789"], ["ST7796S", "ST7796S"]]}], "message1": "引脚配置 CS %1 DC %2 MOSI %3 SCLK %4 RST %5", "args1": [{"type": "input_value", "name": "CS", "check": "Number"}, {"type": "input_value", "name": "DC", "check": "Number"}, {"type": "input_value", "name": "MOSI", "check": "Number"}, {"type": "input_value", "name": "SCLK", "check": "Number"}, {"type": "input_value", "name": "RST", "check": "Number"}], "inputsInline": false, "previousStatement": null, "nextStatement": null, "colour": "#FFA500", "tooltip": "初始化TFT屏幕，可自定义引脚配置：CS(片选), DC(数据/命令), MOSI(数据), SCLK(时钟), RST(复位,-1表示不使用)", "helpUrl": ""}, {"type": "tft_set_rotation", "message0": "设置TFT旋转 %1", "args0": [{"type": "field_dropdown", "name": "ROTATION", "options": [["0度", "0"], ["90度", "1"], ["180度", "2"], ["270度", "3"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#FFA500"}, {"type": "tft_invert_display", "message0": "设置TFT反色显示 %1", "args0": [{"type": "field_dropdown", "name": "INVERT", "options": [["开启", "true"], ["关闭", "false"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#FFA500", "tooltip": "开启或关闭TFT屏幕的反色显示模式"}, {"type": "tft_fill_screen", "message0": "填充屏幕颜色 %1", "args0": [{"type": "input_value", "name": "COLOR"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#FFA500"}, {"type": "tft_clear_screen", "message0": "清空屏幕", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#FFA500", "tooltip": "清空屏幕显示内容，相当于用黑色填充整个屏幕"}, {"type": "tft_preset_color", "message0": "颜色 %1", "args0": [{"type": "field_colour_hsv_sliders", "name": "COLOR", "colour": "#ffffff"}], "output": "Colour", "colour": "#2A82DA"}, {"type": "tft_set_text_color", "message0": "设置文字颜色 %1 背景颜色 %2", "args0": [{"type": "input_value", "name": "COLOR", "check": "Colour"}, {"type": "input_value", "name": "BG_COLOR", "check": "Colour"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#FFA500", "tooltip": "设置文字颜色和背景颜色，背景颜色可选"}, {"type": "tft_set_text_size", "message0": "设置文字大小 %1", "args0": [{"type": "input_value", "name": "SIZE"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#FFA500"}, {"type": "tft_print", "message0": "在 行 %1 列 %2 打印文本 %3", "args0": [{"type": "input_value", "name": "ROW", "check": "Number"}, {"type": "input_value", "name": "COLUMN", "check": "Number"}, {"type": "input_value", "name": "TEXT"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#FFA500"}, {"type": "tft_draw_pixel", "message0": "画点 X: %1 Y: %2 颜色 %3", "args0": [{"type": "input_value", "name": "X"}, {"type": "input_value", "name": "Y"}, {"type": "input_value", "name": "COLOR"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#5C68A6"}, {"type": "tft_draw_line", "message0": "画线 起点 X: %1 Y: %2 终点 X: %3 Y: %4 颜色 %5", "args0": [{"type": "input_value", "name": "X0"}, {"type": "input_value", "name": "Y0"}, {"type": "input_value", "name": "X1"}, {"type": "input_value", "name": "Y1"}, {"type": "input_value", "name": "COLOR"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#5C68A6"}, {"type": "tft_draw_fast_h_line", "message0": "画水平快线 起点 X: %1 Y: %2 长度: %3 颜色 %4", "args0": [{"type": "input_value", "name": "X"}, {"type": "input_value", "name": "Y"}, {"type": "input_value", "name": "W"}, {"type": "input_value", "name": "COLOR"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#5C68A6"}, {"type": "tft_draw_fast_v_line", "message0": "画垂直快线 起点 X: %1 Y: %2 长度: %3 颜色 %4", "args0": [{"type": "input_value", "name": "X"}, {"type": "input_value", "name": "Y"}, {"type": "input_value", "name": "H"}, {"type": "input_value", "name": "COLOR"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#5C68A6"}, {"type": "tft_draw_rect", "message0": "画矩形 左上角 X: %1 Y: %2 宽: %3 高: %4 颜色 %5", "args0": [{"type": "input_value", "name": "X"}, {"type": "input_value", "name": "Y"}, {"type": "input_value", "name": "W"}, {"type": "input_value", "name": "H"}, {"type": "input_value", "name": "COLOR"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#5C68A6"}, {"type": "tft_fill_rect", "message0": "填充矩形 左上角 X: %1 Y: %2 宽: %3 高: %4 颜色 %5", "args0": [{"type": "input_value", "name": "X"}, {"type": "input_value", "name": "Y"}, {"type": "input_value", "name": "W"}, {"type": "input_value", "name": "H"}, {"type": "input_value", "name": "COLOR"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#5C68A6"}, {"type": "tft_draw_round_rect", "message0": "画圆角矩形 X: %1 Y: %2 宽: %3 高: %4 圆角: %5 颜色 %6", "args0": [{"type": "input_value", "name": "X"}, {"type": "input_value", "name": "Y"}, {"type": "input_value", "name": "W"}, {"type": "input_value", "name": "H"}, {"type": "input_value", "name": "R"}, {"type": "input_value", "name": "COLOR"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#5C68A6"}, {"type": "tft_fill_round_rect", "message0": "填充圆角矩形 X: %1 Y: %2 宽: %3 高: %4 圆角: %5 颜色 %6", "args0": [{"type": "input_value", "name": "X"}, {"type": "input_value", "name": "Y"}, {"type": "input_value", "name": "W"}, {"type": "input_value", "name": "H"}, {"type": "input_value", "name": "R"}, {"type": "input_value", "name": "COLOR"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#5C68A6"}, {"type": "tft_draw_circle", "message0": "画圆 圆心 X: %1 Y: %2 半径: %3 颜色 %4", "args0": [{"type": "input_value", "name": "X"}, {"type": "input_value", "name": "Y"}, {"type": "input_value", "name": "R"}, {"type": "input_value", "name": "COLOR"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#5C68A6"}, {"type": "tft_fill_circle", "message0": "填充圆 圆心 X: %1 Y: %2 半径: %3 颜色 %4", "args0": [{"type": "input_value", "name": "X"}, {"type": "input_value", "name": "Y"}, {"type": "input_value", "name": "R"}, {"type": "input_value", "name": "COLOR"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#5C68A6"}, {"type": "tft_draw_triangle", "message0": "画三角形 点1 X: %1 Y: %2 点2 X: %3 Y: %4 点3 X: %5 Y: %6 颜色 %7", "args0": [{"type": "input_value", "name": "X0"}, {"type": "input_value", "name": "Y0"}, {"type": "input_value", "name": "X1"}, {"type": "input_value", "name": "Y1"}, {"type": "input_value", "name": "X2"}, {"type": "input_value", "name": "Y2"}, {"type": "input_value", "name": "COLOR"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#5C68A6"}, {"type": "tft_fill_triangle", "message0": "填充三角形 点1 X: %1 Y: %2 点2 X: %3 Y: %4 点3 X: %5 Y: %6 颜色 %7", "args0": [{"type": "input_value", "name": "X0"}, {"type": "input_value", "name": "Y0"}, {"type": "input_value", "name": "X1"}, {"type": "input_value", "name": "Y1"}, {"type": "input_value", "name": "X2"}, {"type": "input_value", "name": "Y2"}, {"type": "input_value", "name": "COLOR"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#5C68A6"}, {"type": "tft_color565", "message0": "颜色转换 R: %1 G: %2 B: %3", "args0": [{"type": "input_value", "name": "R"}, {"type": "input_value", "name": "G"}, {"type": "input_value", "name": "B"}], "output": "Number", "inputsInline": true, "colour": "#FFA500"}, {"type": "tft_create_canvas16", "message0": "创建16位画布 名称 %1 宽: %2 高: %3", "args0": [{"type": "field_variable", "name": "NAME", "variable": "canvas16"}, {"type": "input_value", "name": "WIDTH"}, {"type": "input_value", "name": "HEIGHT"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#8A2BE2"}, {"type": "tft_create_canvas1", "message0": "创建1位画布 名称 %1 宽: %2 高: %3", "args0": [{"type": "field_variable", "name": "NAME", "variable": "canvas1"}, {"type": "input_value", "name": "WIDTH"}, {"type": "input_value", "name": "HEIGHT"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#8A2BE2"}, {"type": "tft_get_buffer", "message0": "获取画布缓存 画布 %1", "args0": [{"type": "input_value", "name": "CANVAS"}], "output": null, "inputsInline": true, "colour": "#8A2BE2"}, {"type": "tft_bitmap_image", "message0": "图像数据 %1", "args0": [{"type": "field_input", "name": "IMAGE_DATA", "text": ""}], "output": "Image", "colour": "#FFA500", "tooltip": "创建一个图像数据，可以在TFT屏幕上显示（留空将使用默认图像）"}, {"type": "tft_image_file", "message0": "点击选择图片文件 %1", "args0": [{"type": "field_image_preview", "name": "IMAGE_PREVIEW", "defaultWidth": 100, "defaultHeight": 100, "previewSize": 150}], "message1": "在坐标X %1 Y %2", "args1": [{"type": "input_value", "name": "X", "check": "Number", "shadow": {"type": "math_number", "fields": {"NUM": 0}}}, {"type": "input_value", "name": "Y", "check": "Number", "shadow": {"type": "math_number", "fields": {"NUM": 0}}}], "message2": "调整尺寸 宽:%1 高:%2", "args2": [{"type": "input_value", "name": "WIDTH", "check": "Number", "shadow": {"type": "math_number", "fields": {"NUM": 32}}}, {"type": "input_value", "name": "HEIGHT", "check": "Number", "shadow": {"type": "math_number", "fields": {"NUM": 32}}}], "inputsInline": false, "previousStatement": null, "nextStatement": null, "colour": "#FFA500", "tooltip": "点击选择图片文件，支持预览和尺寸调整，在TFT屏幕指定位置显示"}, {"type": "tft_draw_image", "message0": "在 X: %1 Y: %2 位置显示图像 %3", "args0": [{"type": "input_value", "name": "X", "check": "Number"}, {"type": "input_value", "name": "Y", "check": "Number"}, {"type": "input_value", "name": "BITMAP", "check": "Image"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#FFA500", "tooltip": "在TFT屏幕上显示图像"}]