{"toolbox_name": "串口", "serial_begin": {"message0": "初始化串口%1 设置波特率为%2"}, "serial_available": {"message0": "串口%1缓冲区有数据"}, "serial_read": {"message0": "读取串口%1数据 %2", "args0": [null, {"options": [["读取", "read"], ["窥视", "peek"], ["解析整数", "parseInt"], ["解析浮点数", "parseFloat"]]}]}, "serial_print": {"message0": "串口%1输出%2"}, "serial_println": {"message0": "串口%1输出%2并换行"}, "serial_write": {"message0": "串口%1输出原始数据%2"}, "serial_read_string": {"message0": "读取串口%1字符串"}}