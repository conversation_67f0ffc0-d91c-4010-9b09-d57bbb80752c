[{"type": "sht31_init", "message0": "初始化SHT3x传感器 地址%1", "args0": [{"type": "field_dropdown", "name": "ADDRESS", "options": [["0x44 (默认)", "0x44"], ["0x45 (备用)", "0x45"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#4CAF50", "tooltip": "初始化SHT3x温湿度传感器，选择I2C地址"}, {"type": "sht31_heater_control", "message0": "SHT3x加热器%1", "args0": [{"type": "field_dropdown", "name": "STATE", "options": [["开启", "true"], ["关闭", "false"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#4CAF50", "tooltip": "控制SHT3x传感器内置加热器的开启/关闭"}, {"type": "sht31_is_heater_enabled", "message0": "SHT3x加热器是否开启", "output": "Boolean", "colour": "#4CAF50", "tooltip": "检查SHT3x传感器加热器是否处于开启状态"}, {"type": "sht31_reset", "message0": "SHT3x软件复位", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#4CAF50", "tooltip": "对SHT3x传感器执行软件复位"}, {"type": "sht31_simple_read", "message0": "SHT3x传感器读取%1", "args0": [{"type": "field_dropdown", "name": "TYPE", "options": [["温度(°C)", "temperature"], ["湿度(%)", "humidity"]]}], "output": "Number", "colour": "#4CAF50", "tooltip": "简化版读取SHT3x传感器数据，自动初始化"}]