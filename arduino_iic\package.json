{"name": "@aily-project/lib-core-i2c", "nickname": "ArduinoI2C", "author": "<PERSON><PERSON><PERSON><PERSON>", "description": "I2C总线库，用于检测并输出连接设备的地址，同时报告通信错误，支持Arduino UNO、MEGA、ESP8266、ESP32等开发板", "version": "0.0.1", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "iic", "i2c", "TWI", "Wire"], "scripts": {}, "dependencies": {}, "devDependencies": {}}