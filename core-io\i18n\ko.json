{"toolbox_name": "I/O 핀", "io_pinmode": {"message0": "핀 %1 모드를 %2로 설정"}, "io_digitalwrite": {"message0": "핀 %1에 디지털 신호 %2 출력"}, "io_digitalread": {"message0": "핀 %1에서 디지털 신호 읽기"}, "io_analogwrite": {"message0": "핀 %1에 PWM 신호 %2 출력"}, "io_analogread": {"message0": "핀 %1에서 아날로그 신호 읽기"}, "io_pin_digi": {"message0": "디지털 핀 %1"}, "io_pin_adc": {"message0": "아날로그 핀 %1"}, "io_pin_pwm": {"message0": "PWM 핀 %1"}, "io_mode": {"message0": "핀 모드 %1"}, "io_state": {"message0": "%1 레벨"}}