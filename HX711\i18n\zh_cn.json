{"toolbox_name": "HX711称重传感器", "hx711_create": {"message0": "创建HX711称重传感器 %1"}, "hx711_begin": {"message0": "初始化 %1 数据引脚 %2 时钟引脚 %3", "args0": [null, {"options": "${board.digitalPins}"}, {"options": "${board.digitalPins}"}]}, "hx711_tare": {"message0": "%1 去皮 (次数 %2)"}, "hx711_set_scale": {"message0": "%1 设置比例因子 %2"}, "hx711_get_units": {"message0": "%1 获取重量 (次数 %2)"}, "hx711_read": {"message0": "%1 读取原始数据"}, "hx711_read_average": {"message0": "%1 读取平均值 (次数 %2)"}, "hx711_power_down": {"message0": "%1 关闭电源"}, "hx711_power_up": {"message0": "%1 开启电源"}, "hx711_set_gain": {"message0": "%1 设置增益 %2", "args0": [null, {"options": [["128 (通道A)", "HX711_CHANNEL_A_GAIN_128"], ["64 (通道A)", "HX711_CHANNEL_A_GAIN_64"], ["32 (通道B)", "HX711_CHANNEL_B_GAIN_32"]]}]}, "hx711_calibrate_scale": {"message0": "%1 校准比例 已知重量 %2 次数 %3"}}