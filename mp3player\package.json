{"name": "@aily-project/lib-mp3player-gd3800", "nickname": "GD3800音频播放", "author": "<PERSON>Jumper", "description": "GD3800音频播放模块驱动库，支持串口通信，可用于播放MP3等格式的音频文件。", "version": "0.0.1", "compatibility": {"core": ["esp32:esp32", "esp32:esp32s3", "arduino:avr"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "GD3800", "模块", "mp3", "音乐"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "openjumper"}