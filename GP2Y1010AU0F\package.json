{"name": "@aily-project/lib-gp2y1010au0f", "nickname": "粉尘传感器驱动库", "author": "奈何col", "description": "读取GP2Y1010AU0F粉尘传感器的值，并计算和打印粉尘浓度", "version": "0.0.1", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "GP2Y1010AU0F", "dust sensor", "粉尘", "<PERSON><PERSON><PERSON><PERSON>", "ESP32", "ESP8266"], "scripts": {}, "dependencies": {}, "devDependencies": {}}