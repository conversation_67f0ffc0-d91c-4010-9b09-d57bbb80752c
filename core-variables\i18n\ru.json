{"toolbox_name": "Переменные", "variable_define": {"message0": "Объявить %1 как %2 и присвоить %3", "args0": [null, {"options": [["Целое число", "int"], ["Длинное целое число", "long"], ["Число с плавающей запятой", "float"], ["Двойной точности", "double"], ["Беззнаковое целое число", "unsigned int"], ["Беззнаковое длинное целое число", "unsigned long"], ["Логическое значение", "bool"], ["Символ", "char"], ["Строка", "string"]]}, null]}, "variables_set": {"message0": "Присвоить %1 %2"}}