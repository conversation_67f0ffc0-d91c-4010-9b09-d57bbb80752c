{"name": "@aily-project/lib-taojingchi", "nickname": "淘晶驰串口屏驱动库", "author": "aily Project", "description": "淘晶驰串口屏控制支持库，支持背光调节、页面切换、变量设置等功能", "version": "1.0.0", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp32:esp32c3", "esp32:esp32s3", "esp32:esp32s2", "renesas_uno:minima", "renesas_uno:unor4wifi"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "串口屏", "显示器", "display", "screen", "serial", "hmi", "nextion", "tft"], "scripts": {}, "dependencies": {}, "devDependencies": {}}