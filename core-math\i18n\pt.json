{"toolbox_name": "Matemática", "math_number": {"message0": "%1"}, "math_arithmetic": {"message0": "%1 %2 %3", "args0": [null, {"options": [["<PERSON><PERSON><PERSON><PERSON>", "ADD"], ["Subtrair", "MINUS"], ["Multiplicar", "MULTIPLY"], ["<PERSON><PERSON><PERSON>", "DIVIDE"], ["<PERSON><PERSON><PERSON><PERSON>", "MODULO"], ["Potência", "POWER"]]}, null]}, "math_single": {"message0": "%1 %2", "args0": [{"options": [["Raiz quadrada", "ROOT"], ["Valor absoluto", "ABS"], ["Negativo", "NEG"], ["ln", "LN"], ["log10", "LOG10"], ["e^", "EXP"], ["10^", "POW10"]]}, null]}, "math_trig": {"message0": "%1 %2", "args0": [{"options": [["<PERSON><PERSON>", "SIN"], ["<PERSON><PERSON><PERSON>", "COS"], ["Tangente", "TAN"], ["<PERSON><PERSON> seno", "ASIN"], ["<PERSON><PERSON>", "ACOS"], ["Arco tangente", "ATAN"]]}, null]}, "math_constant": {"message0": "%1", "args0": [{"options": [["π", "PI"], ["e", "E"], ["<PERSON><PERSON><PERSON>", "GOLDEN_RATIO"], ["√2", "SQRT2"], ["√1/2", "SQRT1_2"], ["∞", "INFINITY"]]}]}, "math_number_property": {"message0": "%1 %2", "args0": [null, {"options": [["é par", "EVEN"], ["é impar", "ODD"], ["é primo", "PRIME"], ["é inteiro", "WHOLE"], ["é positivo", "POSITIVE"], ["é negativo", "NEGATIVE"], ["é divisível por", "DIVISIBLE_BY"]]}]}, "math_change": {"message0": "alterar %1 para %2"}, "math_round": {"message0": "%1 %2", "args0": [{"options": [["Arredonda<PERSON>", "ROUND"], ["Arredondar para cima", "ROUNDUP"], ["Arredondar para baixo", "ROUNDDOWN"]]}, null]}, "math_on_list": {"message0": "%1 %2", "args0": [{"options": [["<PERSON><PERSON> da <PERSON>a", "SUM"], ["<PERSON><PERSON><PERSON>", "MIN"], ["<PERSON><PERSON><PERSON><PERSON> da lista", "MAX"], ["<PERSON><PERSON><PERSON>a", "AVERAGE"], ["Mediana da lista", "MEDIAN"], ["<PERSON><PERSON> da lista", "MODE"], ["<PERSON><PERSON> da <PERSON>a", "STD_DEV"], ["<PERSON><PERSON> ale<PERSON> da <PERSON>a", "RANDOM"]]}, null]}, "math_modulo": {"message0": "resto de %1 ÷ %2"}, "math_constrain": {"message0": "limitar %1 entre %2 e %3"}, "math_random_int": {"message0": "inteiro aleatório de %1 a %2"}, "math_random_float": {"message0": "fração aleatória"}, "math_atan2": {"message0": "atan2 do ponto (x: %1, y: %2)"}, "math_round_to_decimal": {"message0": "arredondar %1 para %2 casas decimais"}, "math_bitwise_not": {"message0": "~ %1"}, "map_to": {"message0": "Mapear %1 de [%2,%3] para [%4,%5]"}, "constrain": {"message0": "Restringir %1 entre (mínimo) %2 e (máximo) %3"}}