{"toolbox_name": "I/O 引腳", "io_pinmode": {"message0": "引腳 %1 模式設置為 %2"}, "io_digitalwrite": {"message0": "引腳 %1輸出數字信號 %2"}, "io_digitalread": {"message0": "讀取引腳 %1 的數字信號"}, "io_analogwrite": {"message0": "引腳 %1 輸出PWM信號%2"}, "io_analogread": {"message0": "讀取引腳 %1 的模擬信號"}, "io_pin_digi": {"message0": "數字引腳%1"}, "io_pin_adc": {"message0": "模擬引腳%1"}, "io_pin_pwm": {"message0": "PWM引腳%1"}, "io_mode": {"message0": "引腳模式%1"}, "io_state": {"message0": "%1電平"}}