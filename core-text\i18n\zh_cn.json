{"toolbox_name": "文字", "string_add_string": {"message0": "字符串(%1) + 字符串(%2)"}, "string_charAt": {"message0": "%1 的第 %2 个字符"}, "string_length": {"message0": "%1 的字符数"}, "string_indexOf": {"message0": "%1 包含 %2 ?"}, "string_substring": {"message0": "%1 取得 %2 %3 個字元到 %4 %5 個字元", "args0": [null, {"options": [["第", "0"], ["倒數第", "1"]]}, null, {"options": [["第", "0"], ["倒數第", "1"]]}, null]}, "string_find_str": {"message0": "查找 %1 在 %2 中 %3 出现位置", "args0": [null, null, {"options": [["首次", "indexOf"], ["最后一次", "lastIndexOf"]]}]}, "string_to": {"message0": "将字符串 %1 转换为 %2", "args0": [null, {"options": [["整数", "toInt"], ["小数", "toFloat"]]}]}, "number_to": {"message0": "将数字 %1 转换为ASCII字符串"}, "toascii": {"message0": "将字符 %1 转换为ASCII数值"}, "number_to_string": {"message0": "将数字 %1 转换为字符串"}, "text": {"message0": "%1"}, "text_join": {"message0": ""}, "text_create_join_container": {"message0": "%{BKY_TEXT_CREATE_JOIN_TITLE_JOIN} %1 %2"}, "text_create_join_item": {"message0": "%{BKY_TEXT_CREATE_JOIN_ITEM_TITLE_ITEM}"}, "text_append": {"message0": "在 %1 之后加上文本 %2"}, "text_length": {"message0": "%1 的长度"}, "text_isEmpty": {"message0": "%1 是空的"}, "text_indexOf": {"message0": "在文本 %1 里 %2 %3", "args0": [null, {"options": [["寻找第一次出现的文本", "FIRST"], ["寻找最后一次出现的文本", "LAST"]]}, null]}, "text_charAt": {"message0": "在文本 %1 里 %2", "args0": [null, {"options": [["获取第#个字符", "FROM_START"], ["获取倒数第#个字符", "FROM_END"], ["获取第一个字符", "FIRST"], ["获取最后一个字符", "LAST"], ["获取随机一个字符", "RANDOM"]]}]}, "tt_getSubstring": {"message0": "在文本 %1中获取子串", "message1": "%1 %2", "args1": [{"options": [["从第#个字符", "FROM_START"], ["从倒数第#个字符", "FROM_END"], ["从第一个字符", "FIRST"]]}, null], "message2": "%1 %2", "args2": [{"options": [["到第#个字符", "FROM_START"], ["到倒数第#个字符", "FROM_END"], ["到最后一个字符", "LAST"]]}, null]}, "text_changeCase": {"message0": "%1 %2", "args0": [{"options": [["转为大写", "UPPERCASE"], ["转为小写", "LOWERCASE"], ["转为首字母大写", "TITLECASE"]]}, null]}, "text_trim": {"message0": "%1 的 %2", "args0": [{"options": [["消除其两侧的空格", "BOTH"], ["消除其左侧的空格", "LEFT"], ["消除其右侧的空格", "RIGHT"]]}, null]}, "text_count": {"message0": "计算 %1 在 %2 中出现的次数"}, "text_replace": {"message0": "把 %1 中的 %2 替换为 %3"}, "text_reverse": {"message0": "倒转文本 %1"}}