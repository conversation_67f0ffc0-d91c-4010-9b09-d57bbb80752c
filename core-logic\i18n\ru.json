{"toolbox_name": "Логика", "controls_if": {"message0": "🔀Если %1", "message1": "То выполнить %1"}, "controls_ifelse": {"message0": "🔀Если %1", "message1": "Выполнить %1", "message2": "Иначе %1"}, "logic_compare": {"message0": "%1 %2 %3", "args0": [null, {"options": [["==", "EQ"], ["!=", "NEQ"], ["<", "LT"], [">", "GT"], [">=", "GTE"], ["<=", "LTE"]]}, null]}, "logic_operation": {"message0": "%1 %2 %3", "args0": [null, {"options": [["И", "AND"], ["ИЛИ", "OR"]]}, null]}, "logic_negate": {"message0": "НЕ %1"}, "logic_boolean": {"message0": "%1", "args0": [{"options": [["Истина", "true"], ["Ложь", "false"]]}]}, "logic_ternary": {"message0": "Проверка %1", "message1": "Если истинно %1", "message2": "Если ложно %1"}}