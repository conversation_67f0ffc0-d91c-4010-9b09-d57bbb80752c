{"name": "@aily-project/lib-dfplayer", "nickname": "DFPlayer", "author": "dfrobot", "description": "DFPlayer控制库，用于控制DFPlayer Mini模块，实现音频播放、暂停、音量调节及其它功能", "version": "0.0.1", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["dfplayer", "DFRobotDFPlayerMini", "a<PERSON><PERSON><PERSON>", "audio", "playback", "volume"], "scripts": {}, "dependencies": {}, "devDependencies": {}}