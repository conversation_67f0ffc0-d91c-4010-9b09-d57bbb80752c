{"name": "@aily-project/lib-esp32-spi", "nickname": "ESP32 SPI", "author": "esp32", "description": "基于SPI.h的SPI通信支持库，适用于Arduino UNO、MEGA、ESP8266、ESP32等开发板", "version": "0.0.1", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "spi", "SPIClass", "begin", "beginTransaction", "transfer", "pinSS"], "scripts": {}, "dependencies": {}, "devDependencies": {}}