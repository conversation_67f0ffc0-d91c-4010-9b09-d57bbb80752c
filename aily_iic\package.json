{"name": "@aily-project/lib-aily_iic", "nickname": "Aily I2C通信库", "author": "aily Project", "description": "基于Wire库封装的I2C通信支持库，适用于Arduino UNO、MEGA、ESP8266、ESP32等开发板", "version": "0.0.1", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp8266:esp8266"], "voltage": [3.3, 5]}, "keywords": ["aily", "iic", "i2c", "Wire", "i2c_begin", "i2c_write", "a<PERSON><PERSON><PERSON>"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "i3water"}