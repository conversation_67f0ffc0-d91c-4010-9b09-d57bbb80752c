{"name": "@aily-project/lib-bmp280", "nickname": "BMP280气压传感器", "author": "aily-project", "description": "用于BMP280温度、气压与海拔高度测量的传感器库，适用于Arduino、ESP32等开发板", "version": "0.0.1", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp32:esp32c3", "esp32:esp32s3"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "传感器", "气压", "温度", "高度", "BMP280"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "openjumper"}