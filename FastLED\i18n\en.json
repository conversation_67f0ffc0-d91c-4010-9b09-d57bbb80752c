{"toolbox_name": "FastLED", "fastled_init": {"message0": "Initialize RGB strip Pin %1 Type %2 Number of LEDs %3", "args0": [null, {"options": [["WS2812B", "WS2812B"], ["WS2812", "WS2812"], ["WS2811", "WS2811"], ["NEOPIXEL", "NEOPIXEL"], ["WS2801", "WS2801"], ["LPD8806", "LPD8806"], ["APA102", "APA102"]]}, null]}, "fastled_set_pixel": {"message0": "Set RGB strip Pin %1 Index %2 Color %3"}, "fastled_show": {"message0": "Update RGB strip"}, "fastled_clear": {"message0": "Clear RGB strip Pin %1"}, "fastled_brightness": {"message0": "Set brightness %1"}, "fastled_rgb": {"message0": "RGB Color R %1 G %2 B %3"}, "fastled_preset_color": {"message0": "Color %1"}, "fastled_fill_solid": {"message0": "Fill all LEDs Pin %1 Color %2"}, "fastled_hsv": {"message0": "HSV Color H %1 S %2 V %3"}, "fastled_rainbow": {"message0": "Rainbow effect Pin %1 Start Hue %2 Delta %3"}, "fastled_fire_effect": {"message0": "Fire effect Pin %1 Heat %2 Cooling Speed %3"}, "fastled_meteor": {"message0": "Meteor effect Pin %1 Color %2 Meteor Size %3 Tail Fade %4 Speed %5"}, "fastled_palette_cycle": {"message0": "Palette cycle effect Pin %1 Palette %2 Speed %3", "args0": [null, {"options": [["Rainbow", "RainbowColors_p"], ["<PERSON><PERSON>", "LavaColors_p"], ["Cloud", "CloudColors_p"], ["Ocean", "OceanColors_p"], ["Forest", "ForestColors_p"], ["Party", "PartyColors_p"], ["Heat", "HeatColors_p"]]}, null]}, "fastled_breathing": {"message0": "Breathing effect Pin %1 Color %2 Speed %3"}, "fastled_twinkle": {"message0": "Twinkle effect Pin %1 Twinkle Count %2 Background %3 Twinkle Color %4"}}