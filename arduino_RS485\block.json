[{"type": "rs485_end", "message0": "关闭RS485通信", "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "inputsInline": true}, {"type": "rs485_available", "message0": "RS485有数据可读", "output": "Boolean", "colour": "#FF6B35", "inputsInline": true}, {"type": "rs485_read", "message0": "读取RS485数据", "output": "Number", "colour": "#FF6B35", "inputsInline": true}, {"type": "rs485_peek", "message0": "查看RS485下一个字节", "output": "Number", "colour": "#FF6B35", "inputsInline": true}, {"type": "rs485_write", "message0": "RS485发送数据%1", "args0": [{"type": "input_value", "name": "DATA", "shadow": {"type": "math_number", "fields": {"NUM": 0}}}], "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "inputsInline": true}, {"type": "rs485_print", "message0": "RS485打印%1", "args0": [{"type": "input_value", "name": "DATA", "shadow": {"type": "text", "fields": {"TEXT": "Hello"}}}], "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "inputsInline": true}, {"type": "rs485_println", "message0": "RS485打印并换行%1", "args0": [{"type": "input_value", "name": "DATA", "shadow": {"type": "text", "fields": {"TEXT": "Hello"}}}], "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "inputsInline": true}, {"type": "rs485_flush", "message0": "等待RS485发送完成", "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "inputsInline": true}, {"type": "rs485_begin_transmission", "message0": "开始RS485发送", "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "inputsInline": true}, {"type": "rs485_end_transmission", "message0": "结束RS485发送", "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "inputsInline": true}, {"type": "rs485_receive", "message0": "启用RS485接收", "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "inputsInline": true}, {"type": "rs485_no_receive", "message0": "禁用RS485接收", "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "inputsInline": true}, {"type": "rs485_send_break", "message0": "发送RS485中断信号 持续时间%1毫秒", "args0": [{"type": "field_number", "name": "DURATION", "value": 100, "min": 1}], "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "inputsInline": true}, {"type": "rs485_send_break_microseconds", "message0": "发送RS485中断信号 持续时间%1微秒", "args0": [{"type": "field_number", "name": "DURATION", "value": 1000, "min": 1}], "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "inputsInline": true}, {"type": "rs485_set_pins", "message0": "设置RS485引脚 发送引脚%1 发送使能引脚%2 接收使能引脚%3", "args0": [{"type": "field_dropdown", "name": "TX_PIN", "options": "${board.digitalPins}"}, {"type": "field_dropdown", "name": "DE_PIN", "options": "${board.digitalPins}"}, {"type": "field_dropdown", "name": "RE_PIN", "options": "${board.digitalPins}"}], "previousStatement": null, "nextStatement": null, "colour": "#FF6B35", "inputsInline": false}, {"type": "rs485_simple_send", "message0": "RS485简单发送%1", "args0": [{"type": "input_value", "name": "DATA", "shadow": {"type": "text", "fields": {"TEXT": "Hello"}}}], "previousStatement": null, "nextStatement": null, "colour": "#FF9500", "inputsInline": true}, {"type": "rs485_simple_receive", "message0": "RS485接收到数据时", "message1": "执行%1", "args1": [{"type": "input_statement", "name": "DO"}], "previousStatement": null, "nextStatement": null, "colour": "#FF9500", "inputsInline": true}, {"type": "rs485_received_data", "message0": "RS485接收的数据", "output": "String", "colour": "#FF9500", "inputsInline": true}, {"type": "rs485_begin", "message0": "快速设置RS485 串口%1 波特率%2 发送引脚%3 发送使能引脚%4 接收使能引脚%5", "args0": [{"type": "field_dropdown", "name": "SERIAL", "options": "${board.serialPort}"}, {"type": "field_dropdown", "name": "BAUDRATE", "options": "${board.serialSpeed}"}, {"type": "field_dropdown", "name": "TX_PIN", "options": "${board.digitalPins}"}, {"type": "field_dropdown", "name": "DE_PIN", "options": "${board.digitalPins}"}, {"type": "field_dropdown", "name": "RE_PIN", "options": "${board.digitalPins}"}], "previousStatement": null, "nextStatement": null, "colour": "#FFB347", "inputsInline": false}, {"type": "rs485_master_send", "message0": "RS485主机发送数据%1到从机地址%2", "args0": [{"type": "input_value", "name": "DATA", "shadow": {"type": "text", "fields": {"TEXT": "Hello"}}}, {"type": "field_number", "name": "SLAVE_ADDR", "value": 1, "min": 1, "max": 255}], "previousStatement": null, "nextStatement": null, "colour": "#FFB347", "inputsInline": true}, {"type": "rs485_slave_receive", "message0": "RS485从机地址%1接收到数据时", "message1": "执行%1", "args0": [{"type": "field_number", "name": "SLAVE_ADDR", "value": 1, "min": 1, "max": 255}], "args1": [{"type": "input_statement", "name": "DO"}], "previousStatement": null, "nextStatement": null, "colour": "#FFB347", "inputsInline": true}]