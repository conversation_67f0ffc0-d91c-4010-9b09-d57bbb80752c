{"toolbox_name": "DFPlayer", "dfplayer_begin": {"message0": "Inicializar módulo DFPlayer %1 RX pino %2 TX pino %3"}, "dfplayer_play": {"message0": "DFPlayer %1 tocar arquivo número %2"}, "dfplayer_pause": {"message0": "DFPlayer %1 pausar reprodução"}, "dfplayer_start": {"message0": "DFPlayer %1 continuar reprodução"}, "dfplayer_stop": {"message0": "DFPlayer %1 parar reprodução"}, "dfplayer_next": {"message0": "DFPlayer %1 tocar próxima faixa"}, "dfplayer_previous": {"message0": "DFPlayer %1 tocar faixa anterior"}, "dfplayer_volume": {"message0": "DFPlayer %1 definir volume para %2"}, "dfplayer_volume_up": {"message0": "DFPlayer %1 aumentar volume"}, "dfplayer_volume_down": {"message0": "DFPlayer %1 diminuir volume"}, "dfplayer_eq": {"message0": "DFPlayer %1 definir equalizador %2", "options": [["Normal", "0"], ["Pop", "1"], ["Rock", "2"], ["Jazz", "3"], ["Clássico", "4"], ["Bass", "5"]]}, "dfplayer_output_device": {"message0": "DFPlayer %1 definir dispositivo de saída como %2", "options": [["DISPOSITIVO1", "1"], ["DISPOSITIVO2", "2"]]}, "dfplayer_loop": {"message0": "DFPlayer %1 tocar em loop arquivo número %2"}, "dfplayer_play_folder": {"message0": "DFPlayer %1 tocar arquivo %3 na pasta %2"}, "dfplayer_enable_loop_all": {"message0": "DFPlayer %1 ativar loop total"}, "dfplayer_disable_loop_all": {"message0": "DFPlayer %1 desativar loop total"}, "dfplayer_play_mp3_folder": {"message0": "DFPlayer %1 tocar arquivo %2 na pasta MP3"}, "dfplayer_advertise": {"message0": "DFPlayer %1 tocar anúncio %2"}, "dfplayer_stop_advertise": {"message0": "DFPlayer %1 parar an<PERSON><PERSON>"}, "dfplayer_play_large_folder": {"message0": "DFPlayer %1 tocar arquivo %3 na grande pasta %2"}, "dfplayer_loop_folder": {"message0": "DFPlayer %1 tocar em loop pasta %2"}, "dfplayer_random_all": {"message0": "DFPlayer %1 tocar todos os arquivos aleatoriamente"}, "dfplayer_enable_loop": {"message0": "DFPlayer %1 ativar loop"}, "dfplayer_disable_loop": {"message0": "DFPlayer %1 desativar loop"}, "dfplayer_read_state": {"message0": "DFPlayer %1 ler estado"}, "dfplayer_read_volume": {"message0": "DFPlayer %1 ler volume"}, "dfplayer_read_eq": {"message0": "DFPlayer %1 ler equalizador"}, "dfplayer_read_file_counts": {"message0": "DFPlayer %1 ler número de arquivos"}, "dfplayer_read_current_file_number": {"message0": "DFPlayer %1 ler número do arquivo atual"}, "dfplayer_read_file_counts_in_folder": {"message0": "DFPlayer %1 ler número de arquivos na pasta %2"}, "dfplayer_available": {"message0": "DFPlayer %1 verificar se há mensagens disponíveis"}, "dfplayer_read_type": {"message0": "DFPlayer %1 ler tipo de mensagem"}, "dfplayer_read": {"message0": "DFPlayer %1 ler parâmetros da mensagem"}, "dfplayer_simple_play": {"message0": "Reprodução simples: inicializar DFPlayer (RX: %1, TX: %2) e tocar arquivo %3"}}