[{"type": "led_matrix_init", "message0": "初始化LED矩阵", "previousStatement": null, "nextStatement": null, "colour": "#2F89BC", "tooltip": "初始化LED矩阵显示"}, {"type": "led_matrix_clear", "message0": "清除LED矩阵显示", "previousStatement": null, "nextStatement": null, "colour": "#2F89BC", "tooltip": "清除LED矩阵当前显示内容"}, {"type": "led_matrix_display_text", "message0": "LED矩阵显示文本 %1 滚动方向 %2 速度 %3", "args0": [{"type": "input_value", "name": "TEXT", "check": "String"}, {"type": "field_dropdown", "name": "DIRECTION", "options": [["左滚动", "SCROLL_LEFT"], ["右滚动", "SCROLL_RIGHT"]]}, {"type": "input_value", "name": "SPEED", "check": "Number"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#2F89BC", "tooltip": "在LED矩阵上显示滚动文本"}, {"type": "led_matrix_display_frame", "message0": "LED矩阵显示图案 %1", "args0": [{"type": "input_value", "name": "FRAME", "check": "Array"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#2F89BC", "tooltip": "在LED矩阵上显示预设图案"}, {"type": "led_matrix_preset_pattern", "message0": "预设图案 %1", "args0": [{"type": "field_led_pattern_selector", "name": "PATTERN", "patterns": [{"name": "蓝牙", "hex": ["0x10428", "0xa4517ff0", "0x50088104"]}, {"name": "启动器", "hex": ["0x4015", "0x2482082", "0x81100e0"]}, {"name": "芯片", "hex": ["0x1503f", "0x81103181", "0x103f8150"]}, {"name": "云WiFi", "hex": ["0x18464841", "0x26549558", "0x54652188"]}, {"name": "危险", "hex": ["0x400a015", "0x1502082", "0x484047fc"]}, {"name": "基础笑脸", "hex": ["0x19819", "0x80000001", "0xf8000000"]}, {"name": "开心笑脸", "hex": ["0x19819", "0x80000001", "0x81f8000"]}, {"name": "伤心脸", "hex": ["0x19819", "0x80000001", "0xf8108000"]}, {"name": "大心形", "hex": ["0x3184a444", "0x44042081", "0x100a0040"]}, {"name": "小心形", "hex": ["0xa", "0x1501100", "0xa0040000"]}, {"name": "点赞", "hex": ["0x1003005", "0x39c2842", "0x842843f8"]}, {"name": "音符", "hex": ["0x1f81f810", "0x81081081", "0x18318300"]}, {"name": "电阻", "hex": ["0x7f", "0xed437fe0", "0x0"]}, {"name": "UNO", "hex": ["0x7fc404e0", "0x4e024024", "0xf24027fc"]}], "patternWidth": 12, "patternHeight": 8, "gridCols": 3, "previewSize": 96, "blockDisplaySize": 64, "colours": {"empty": "#2a2a2a", "filled": "#ffffff", "border": "#2F89BC", "background": "#2a2a2a", "selected": "#ff4444"}}], "output": "Array", "colour": "#2F89BC", "tooltip": "选择预设的LED矩阵图案"}, {"type": "led_matrix_preset_animation", "message0": "LED矩阵显示预设动画 %1", "args0": [{"type": "field_led_pattern_selector", "name": "PATTERN", "patterns": [{"name": "启动动画", "hex": ["0x0", "0x800", "0x0"]}, {"name": "俄罗斯方块介绍", "hex": ["0xe0000000", "0x0", "0x0"]}, {"name": "ATmega芯片", "hex": ["0x0", "0xffeffe0", "0x0"]}, {"name": "LED水平闪烁", "hex": ["0x403c047", "0x84405fc3", "0xc0040000"]}, {"name": "LED垂直闪烁", "hex": ["0x1c022032", "0x3207f01", "0x40140100"]}, {"name": "箭头指南针", "hex": ["0x4008", "0x1780800", "0x40000000"]}, {"name": "音频波形", "hex": ["0x80080", "0x808808a", "0x2aa2aaaa"]}, {"name": "电池", "hex": ["0xffe80280", "0x38018038", "0x2ffe000"]}, {"name": "弹跳球", "hex": ["0x0", "0x20050050", "0x2000000"]}, {"name": "虫子", "hex": ["0x8037fc20", "0x8a0b60c2", "0xa8a0b7fc"]}, {"name": "检查", "hex": ["0x0", "0x2000", "0x0"]}, {"name": "云", "hex": ["0x18025842", "0x44428018", "0x14023fc"]}, {"name": "下载", "hex": ["0x4004004", "0x1500a04", "0x444047fc"]}, {"name": "DVD标志", "hex": ["0x0", "0x1000000", "0x0"]}, {"name": "心跳线", "hex": ["0x0", "0xe000", "0x0"]}, {"name": "心跳", "hex": ["0xffffffff", "0xffffffff", "0xffffffff"]}, {"name": "无限循环加载", "hex": ["0x82", "0x8408807", "0x0"]}, {"name": "时钟加载", "hex": ["0x4000004", "0x2480000", "0x40000"]}, {"name": "加载", "hex": ["0x6011000", "0x2080001", "0x10040000"]}, {"name": "锁", "hex": ["0xc012012", "0x3f02102", "0x102103f0"]}, {"name": "通知", "hex": ["0x400e011", "0x1102084", "0x47fc040"]}, {"name": "开源", "hex": ["0x0", "0x10010010", "0x0"]}, {"name": "旋转硬币", "hex": ["0xe011", "0x2082082", "0x81100e0"]}, {"name": "俄罗斯方块", "hex": ["0xe0000000", "0x0", "0x0"]}, {"name": "WiFi搜索", "hex": ["0x0", "0x0", "0x40"]}, {"name": "沙漏", "hex": ["0xe07f99fe", "0x1fc1fc1f", "0xe1f99e07"]}], "patternWidth": 12, "patternHeight": 8, "gridCols": 3, "previewSize": 96, "blockDisplaySize": 64, "colours": {"empty": "#2a2a2a", "filled": "#ffffff", "border": "#2F89BC", "background": "#2a2a2a", "selected": "#ff4444"}}], "previousStatement": null, "nextStatement": null, "colour": "#2F89BC", "tooltip": "选择预设的LED矩阵图案"}, {"type": "led_matrix_display_frame_set", "message0": "LED矩阵显示图案%1", "args0": [{"type": "field_led_matrix", "name": "MATRIX", "width": 12, "height": 8, "fieldHeight": 128, "colours": {"empty": "#2a2a2a", "filled": "#ffffff", "border": "#2F89BC"}}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#2F89BC", "tooltip": "在LED矩阵上显示自定义图案"}, {"type": "led_matrix_display_animation", "message0": "LED矩阵显示动画序列 %1ms/帧 %2", "args0": [{"type": "field_input", "name": "DELAY", "text": "100"}, {"type": "input_value", "name": "ADD0", "check": "Array"}], "message1": "%1", "args1": [{"type": "input_value", "name": "ADD1", "check": "Array"}], "inputsInline": false, "previousStatement": null, "nextStatement": null, "colour": "#2F89BC", "helpUrl": "", "tooltip": "显示LED矩阵动画序列，将多个图案组合成动画", "mutator": "led_matrix_animation_mutator", "extensions": ["led_matrix_animation_extension"]}, {"type": "led_matrix_custom_pattern", "message0": "%1", "args0": [{"type": "field_led_matrix", "name": "MATRIX", "width": 12, "height": 8, "fieldHeight": 128, "colours": {"empty": "#2a2a2a", "filled": "#ffffff", "border": "#2F89BC"}}], "output": "Array", "colour": "#2F89BC"}]