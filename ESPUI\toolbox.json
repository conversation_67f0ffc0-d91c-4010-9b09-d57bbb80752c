{"kind": "category", "name": "ESPUI网页界面", "icon": "fa-light fa-window", "contents": [{"kind": "block", "type": "espui_begin", "inputs": {"TITLE": {"shadow": {"type": "text", "fields": {"TEXT": "我的设备"}}}, "USERNAME": {"shadow": {"type": "text", "fields": {"TEXT": "admin"}}}, "PASSWORD": {"shadow": {"type": "text", "fields": {"TEXT": "admin"}}}}}, {"kind": "label", "text": "UI控件创建"}, {"kind": "block", "type": "espui_label", "inputs": {"TEXT": {"shadow": {"type": "text", "fields": {"TEXT": "状态显示"}}}}}, {"kind": "block", "type": "espui_button", "inputs": {"TEXT": {"shadow": {"type": "text", "fields": {"TEXT": "点击按钮"}}}}}, {"kind": "block", "type": "espui_switcher", "inputs": {"TEXT": {"shadow": {"type": "text", "fields": {"TEXT": "开关控制"}}}}}, {"kind": "block", "type": "espui_slider", "inputs": {"TEXT": {"shadow": {"type": "text", "fields": {"TEXT": "数值调节"}}}, "MIN": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}, "MAX": {"shadow": {"type": "math_number", "fields": {"NUM": 100}}}, "VALUE": {"shadow": {"type": "math_number", "fields": {"NUM": 50}}}}}, {"kind": "block", "type": "espui_text", "inputs": {"TEXT": {"shadow": {"type": "text", "fields": {"TEXT": "文本输入"}}}, "VALUE": {"shadow": {"type": "text", "fields": {"TEXT": ""}}}}}, {"kind": "block", "type": "espui_number", "inputs": {"TEXT": {"shadow": {"type": "text", "fields": {"TEXT": "数字输入"}}}, "MIN": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}, "MAX": {"shadow": {"type": "math_number", "fields": {"NUM": 100}}}, "VALUE": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}}}, {"kind": "block", "type": "espui_graph", "inputs": {"TEXT": {"shadow": {"type": "text", "fields": {"TEXT": "数据图表"}}}}}, {"kind": "block", "type": "espui_gauge", "inputs": {"TEXT": {"shadow": {"type": "text", "fields": {"TEXT": "仪表盘"}}}, "MIN": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}, "MAX": {"shadow": {"type": "math_number", "fields": {"NUM": 100}}}, "VALUE": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}}}, {"kind": "label", "text": "控件操作"}, {"kind": "block", "type": "espui_update_control", "inputs": {"CONTROL_ID": {"shadow": {"type": "math_number", "fields": {"NUM": 1}}}, "VALUE": {"shadow": {"type": "text", "fields": {"TEXT": "新值"}}}}}, {"kind": "block", "type": "espui_update_label", "inputs": {"CONTROL_ID": {"shadow": {"type": "math_number", "fields": {"NUM": 1}}}, "LABEL": {"shadow": {"type": "text", "fields": {"TEXT": "新标签"}}}}}, {"kind": "label", "text": "事件处理"}, {"kind": "block", "type": "espui_on_event", "inputs": {"CONTROL_ID": {"shadow": {"type": "math_number", "fields": {"NUM": 1}}}}}, {"kind": "label", "text": "控件操作扩展"}, {"kind": "block", "type": "espui_get_control_value", "inputs": {"CONTROL_ID": {"shadow": {"type": "math_number", "fields": {"NUM": 1}}}}}, {"kind": "block", "type": "espui_add_graph_point", "inputs": {"CONTROL_ID": {"shadow": {"type": "math_number", "fields": {"NUM": 1}}}, "VALUE": {"shadow": {"type": "math_number", "fields": {"NUM": 0}}}}}, {"kind": "block", "type": "espui_clear_graph", "inputs": {"CONTROL_ID": {"shadow": {"type": "math_number", "fields": {"NUM": 1}}}}}, {"kind": "label", "text": "WiFi网络配置"}, {"kind": "block", "type": "espui_wifi_setup", "inputs": {"SSID": {"shadow": {"type": "text", "fields": {"TEXT": "MyWiFi"}}}, "PASSWORD": {"shadow": {"type": "text", "fields": {"TEXT": "password"}}}}}, {"kind": "block", "type": "espui_wifi_status"}, {"kind": "block", "type": "espui_get_ip"}]}