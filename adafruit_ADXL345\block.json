[{"type": "adxl345_init", "message0": "初始化ADXL345加速度传感器 ID %1", "args0": [{"type": "field_number", "name": "SENSOR_ID", "value": 12345}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#FF9800"}, {"type": "adxl345_read_x", "message0": "读取ADXL345 X轴加速度 (m/s²)", "output": "Number", "colour": "#FF9800"}, {"type": "adxl345_read_y", "message0": "读取ADXL345 Y轴加速度 (m/s²)", "output": "Number", "colour": "#FF9800"}, {"type": "adxl345_read_z", "message0": "读取ADXL345 Z轴加速度 (m/s²)", "output": "Number", "colour": "#FF9800"}, {"type": "adxl345_read_xyz", "message0": "读取ADXL345全部轴向数据到变量 X %1 Y %2 Z %3", "args0": [{"type": "field_variable", "name": "VAR_X", "variable": "accel_x"}, {"type": "field_variable", "name": "VAR_Y", "variable": "accel_y"}, {"type": "field_variable", "name": "VAR_Z", "variable": "accel_z"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#FF9800"}, {"type": "adxl345_set_range", "message0": "设置ADXL345测量范围 %1", "args0": [{"type": "field_dropdown", "name": "RANGE", "options": [["±2g", "ADXL345_RANGE_2_G"], ["±4g", "ADXL345_RANGE_4_G"], ["±8g", "ADXL345_RANGE_8_G"], ["±16g", "ADXL345_RANGE_16_G"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#FF9800"}, {"type": "adxl345_set_data_rate", "message0": "设置ADXL345数据采样率 %1", "args0": [{"type": "field_dropdown", "name": "DATA_RATE", "options": [["0.10 Hz", "ADXL345_DATARATE_0_10_HZ"], ["0.20 Hz", "ADXL345_DATARATE_0_20_HZ"], ["0.39 Hz", "ADXL345_DATARATE_0_39_HZ"], ["0.78 Hz", "ADXL345_DATARATE_0_78_HZ"], ["1.56 Hz", "ADXL345_DATARATE_1_56_HZ"], ["3.13 Hz", "ADXL345_DATARATE_3_13_HZ"], ["6.25 Hz", "ADXL345_DATARATE_6_25HZ"], ["12.5 Hz", "ADXL345_DATARATE_12_5_HZ"], ["25 Hz", "ADXL345_DATARATE_25_HZ"], ["50 Hz", "ADXL345_DATARATE_50_HZ"], ["100 Hz", "ADXL345_DATARATE_100_HZ"], ["200 Hz", "ADXL345_DATARATE_200_HZ"], ["400 Hz", "ADXL345_DATARATE_400_HZ"], ["800 Hz", "ADXL345_DATARATE_800_HZ"], ["1600 Hz", "ADXL345_DATARATE_1600_HZ"], ["3200 Hz", "ADXL345_DATARATE_3200_HZ"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#FF9800"}, {"type": "adxl345_get_range", "message0": "获取ADXL345当前测量范围", "output": "Number", "colour": "#FF9800"}, {"type": "adxl345_get_data_rate", "message0": "获取ADXL345当前数据采样率", "output": "Number", "colour": "#FF9800"}, {"type": "adxl345_display_sensor_details", "message0": "显示ADXL345传感器详细信息", "previousStatement": null, "nextStatement": null, "colour": "#FF9800"}, {"type": "adxl345_check_connection", "message0": "检查ADXL345传感器连接状态", "output": "Boolean", "colour": "#FF9800"}]