{"toolbox_name": "Math", "math_number": {"message0": "%1"}, "math_arithmetic": {"message0": "%1 %2 %3", "args0": [null, {"options": [["Add", "ADD"], ["Subtract", "MINUS"], ["Multiply", "MULTIPLY"], ["Divide", "DIVIDE"], ["<PERSON><PERSON><PERSON>", "MODULO"], ["Power", "POWER"]]}, null]}, "math_single": {"message0": "%1 %2", "args0": [{"options": [["Square root", "ROOT"], ["Absolute", "ABS"], ["Negative", "NEG"], ["ln", "LN"], ["log10", "LOG10"], ["e^", "EXP"], ["10^", "POW10"]]}, null]}, "math_trig": {"message0": "%1 %2", "args0": [{"options": [["<PERSON><PERSON>", "SIN"], ["<PERSON><PERSON>e", "COS"], ["Tangent", "TAN"], ["Arcsine", "ASIN"], ["Arccosine", "ACOS"], ["Arctangent", "ATAN"]]}, null]}, "math_constant": {"message0": "%1", "args0": [{"options": [["π", "PI"], ["e", "E"], ["Golden ratio", "GOLDEN_RATIO"], ["sqrt(2)", "SQRT2"], ["sqrt(1/2)", "SQRT1_2"], ["∞", "INFINITY"]]}]}, "math_number_property": {"message0": "%1 %2", "args0": [null, {"options": [["is even", "EVEN"], ["is odd", "ODD"], ["is prime", "PRIME"], ["is whole", "WHOLE"], ["is positive", "POSITIVE"], ["is negative", "NEGATIVE"], ["is divisible by", "DIVISIBLE_BY"]]}]}, "math_change": {"message0": "change %1 by %2"}, "math_round": {"message0": "%1 %2", "args0": [{"options": [["Round", "ROUND"], ["Round up", "ROUNDUP"], ["Round down", "ROUNDDOWN"]]}, null]}, "math_on_list": {"message0": "%1 %2", "args0": [{"options": [["Sum of list", "SUM"], ["Min of list", "MIN"], ["Max of list", "MAX"], ["Average of list", "AVERAGE"], ["Median of list", "MEDIAN"], ["Mode of list", "MODE"], ["Standard deviation of list", "STD_DEV"], ["Random item from list", "RANDOM"]]}, null]}, "math_modulo": {"message0": "remainder of %1 ÷ %2"}, "math_constrain": {"message0": "constrain %1 between %2 and %3"}, "math_random_int": {"message0": "random integer from %1 to %2"}, "math_random_float": {"message0": "random fraction"}, "math_atan2": {"message0": "atan2 of point (x: %1, y: %2)"}, "math_round_to_decimal": {"message0": "round %1 to %2 decimal places"}, "math_bitwise_not": {"message0": "~ %1"}, "map_to": {"message0": "Map %1 from[%2,%3] to [%4,%5]"}, "constrain": {"message0": "Constrain %1 between (min) %2 and (max) %3"}}