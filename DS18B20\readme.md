# DS18B20 Dallas数字温度传感器库

这是一个用于Arduino Blockly的DS18B20数字温度传感器库，基于Dallas Temperature和OneWire库开发。

## 支持的传感器

- DS18B20
- DS18S20  
- DS1820
- DS1822
- DS1825
- MAX31820
- MAX31850

## 功能特性

- 支持单个和多个传感器
- 支持多引脚独立传感器连接
- 摄氏度和华氏度温度读取
- 可配置温度分辨率（9-12位）
- 自动检测设备数量
- 支持寄生供电模式检测
- 简化的单引脚温度读取

## 使用方法

### 基本使用（单引脚）

1. **初始化传感器**：使用"初始化 DS18B20 传感器"块设置数据引脚
2. **读取温度**：使用"读取 DS18B20 温度"块获取温度值

### 多引脚独立传感器

当你有多个引脚分别连接DS18B20传感器时：

1. **初始化多个引脚**：
   - 使用"初始化引脚 X 的 DS18B20 传感器"分别初始化每个引脚
2. **读取特定引脚温度**：
   - 使用"读取引脚 X DS18B20 温度"获取指定引脚的温度

### 简化使用

对于单个传感器，可以直接使用"引脚 X DS18B20 读取温度"块，无需单独初始化。

### 多传感器使用（同一总线）

当一个引脚连接多个传感器时：

1. 使用"获取总线上 DS18B20 设备数量"或"获取引脚 X 总线上 DS18B20 设备数量"获取传感器数量
2. 使用"读取第 X 个 DS18B20 温度"或"读取引脚 X 第 Y 个 DS18B20 温度"按索引读取特定传感器

### 高级设置

- 使用"设置 DS18B20 分辨率"调整温度测量精度
- 使用"DS18B20 是否为寄生供电模式"检测供电模式

## 硬件连接

- VCC: 3.3V 或 5V
- GND: 接地
- DATA: 连接到数字引脚（需要4.7kΩ上拉电阻）

## 依赖库

本库需要以下Arduino库：
- OneWire
- DallasTemperature

## 注意事项

1. 数据线需要连接4.7kΩ上拉电阻到VCC
2. 多个传感器可以连接到同一数据线（总线模式）
3. 传感器有唯一的64位地址，可以在同一总线上区分
4. 寄生供电模式下，传感器从数据线获取电源
- **DS18S20 和 DS1820**: 只支持9位分辨率，固定精度
- **DS18B20、DS1822、DS1825、MAX31820、MAX31850**: 支持9-12位可调分辨率

### 代码优化
- 根据选择的传感器型号，自动设置合适的默认分辨率
- 为DS18S20/DS1820自动限制分辨率选项为9位
- 生成的代码包含传感器型号注释，便于维护

### 错误检测
- 简化读取模式包含传感器断开检测
- 针对不同传感器型号提供专门的错误提示

## 特性
- 支持单个和多个传感器连接
- 温度读取精度可调(9-12位)
- 支持摄氏度和华氏度温度读取
- 自动设备检测和计数
- 连接状态检查
- 简化的单传感器读取模式

## 积木块说明

### 简化积木块
- **DS18B20 引脚 X 读取温度 (℃)**: 最简单的使用方式，自动处理初始化和读取过程

### 高级积木块
- **初始化 DS18B20 传感器 连接到引脚 X**: 初始化指定引脚的传感器总线
- **DS18B20 引脚 X 开始温度转换**: 启动指定引脚传感器的温度转换过程
- **DS18B20 引脚 X 读取温度 (℃) 传感器序号 Y**: 读取指定引脚上指定序号传感器的摄氏度温度
- **DS18B20 引脚 X 读取温度 (℉) 传感器序号 Y**: 读取指定引脚上指定序号传感器的华氏度温度
- **DS18B20 引脚 X 获取设备数量**: 获取指定引脚总线上连接的传感器数量
- **DS18B20 引脚 X 传感器 Y 是否连接**: 检查指定引脚上指定序号的传感器是否正常连接
- **DS18B20 引脚 X 设置分辨率 Y 位**: 设置指定引脚传感器的温度转换精度(9-12位)

## 使用方法

### 简单使用(推荐新手)
对于单个传感器的简单应用，直接使用"DS18B20 引脚 X 读取温度 (℃)"积木块即可：

```
永远循环:
  变量 温度 = DS18B20 引脚 2 读取温度 (℃)
  串口输出 温度
  延时 1000 毫秒
```

### 高级使用
对于多传感器或需要更多控制的应用：

```
永远循环:
  DS18B20 引脚 2 开始温度转换
  延时 750 毫秒
  
  变量 设备数量 = DS18B20 引脚 2 获取设备数量
  串口输出 "发现传感器数量: " + 设备数量
  
  如果 DS18B20 引脚 2 传感器 0 是否连接:
    变量 温度1 = DS18B20 引脚 2 读取温度 (℃) 传感器序号 0
    串口输出 "传感器1温度: " + 温度1 + "℃"
  
  如果 DS18B20 引脚 2 传感器 1 是否连接:
    变量 温度2 = DS18B20 引脚 2 读取温度 (℃) 传感器序号 1
    串口输出 "传感器2温度: " + 温度2 + "℃"
  
  延时 2000 毫秒
```

你也可以在不同引脚上连接不同的DS18B20传感器：

```
DS18B20 引脚 2 设置分辨率 12 位
DS18B20 引脚 3 设置分辨率 12 位

永远循环:
  变量 温度A = DS18B20 引脚 2 读取温度 (℃)
  变量 温度B = DS18B20 引脚 3 读取温度 (℃)
  
  串口输出 "传感器A: " + 温度A + "℃"
  串口输出 "传感器B: " + 温度B + "℃"
  
  延时 2000 毫秒
```

## 硬件连接
- VCC: 3.3V 或 5V
- GND: 地线
- DQ (数据线): 连接到Arduino数字引脚
- 需要在DQ和VCC之间连接4.7kΩ上拉电阻

## 注意事项
1. DS18B20是单总线设备，多个传感器可以连接到同一条数据线上
2. 每个传感器都有唯一的64位地址
3. 温度转换需要时间，12位精度需要750ms
4. 传感器断开时返回值为-127℃
5. 推荐使用4.7kΩ上拉电阻以确保可靠通信

## 技术参数
- 温度范围: -55℃ ~ +125℃
- 精度: ±0.5℃ (在-10℃ ~ +85℃范围内)
- 分辨率: 9-12位可调
- 供电电压: 3.0V ~ 5.5V
- 接口: 单总线(OneWire)协议

## 依赖库
本库依赖以下Arduino库：
- OneWire库 (用于单总线通信) - 需要在Arduino IDE中安装
- DallasTemperature库 (Dallas温度传感器驱动) - 已包含在src目录中

### 安装OneWire库
在Arduino IDE中：
1. 打开 工具 -> 管理库
2. 搜索 "OneWire" 
3. 安装 Paul Stoffregen 的 OneWire 库

或者从GitHub下载：https://github.com/PaulStoffregen/OneWire

## 使用示例

### 示例1：基本温度读取
```
初始化 DS18B20 传感器 连接到引脚 2
读取 DS18B20 温度 (℃)
```

### 示例2：简化温度读取
```
引脚 2 DS18B20 读取温度 (℃)
```

### 示例3：多引脚独立传感器
```
初始化引脚 2 的 DS18B20 传感器
初始化引脚 3 的 DS18B20 传感器
初始化引脚 4 的 DS18B20 传感器

重复执行 {
    串口打印 "引脚2温度: " + 读取引脚 2 DS18B20 温度 (℃) + "°C"
    串口打印 "引脚3温度: " + 读取引脚 3 DS18B20 温度 (℃) + "°C"
    串口打印 "引脚4温度: " + 读取引脚 4 DS18B20 温度 (℃) + "°C"
    等待 1000 毫秒
}
```

### 示例4：同一总线多传感器
```
初始化引脚 2 的 DS18B20 传感器
设置 DS18B20 分辨率为 12 位

重复执行 {
    变量 设备数量 = 获取引脚 2 总线上 DS18B20 设备数量
    串口打印 "发现设备数量: " + 设备数量
    
    计数 i 从 0 到 设备数量-1 {
        串口打印 "传感器" + i + "温度: " + 读取引脚 2 第 i 个 DS18B20 温度 (℃) + "°C"
    }
    等待 2000 毫秒
}
```

### 示例5：混合模式（多引脚+多传感器）
```
初始化引脚 2 的 DS18B20 传感器  // 引脚2连接3个传感器
初始化引脚 3 的 DS18B20 传感器  // 引脚3连接1个传感器

重复执行 {
    // 读取引脚2上的多个传感器
    串口打印 "=== 引脚2传感器组 ==="
    计数 i 从 0 到 2 {
        串口打印 "传感器" + i + ": " + 读取引脚 2 第 i 个 DS18B20 温度 (℃) + "°C"
    }
    
    // 读取引脚3上的单个传感器
    串口打印 "=== 引脚3传感器 ==="
    串口打印 "温度: " + 读取引脚 3 DS18B20 温度 (℃) + "°C"
    
    等待 2000 毫秒
}
```

## 生成的Arduino代码示例

使用基本温度读取块将生成如下Arduino代码：

```cpp
#include <OneWire.h>
#include <DallasTemperature.h>

OneWire oneWire(2);
DallasTemperature sensors(&oneWire);

void setup() {
  Serial.begin(9600);
  sensors.begin();
}

void loop() {
  sensors.requestTemperatures();
  float temp = sensors.getTempCByIndex(0);
  Serial.print("Temperature: ");
  Serial.print(temp);
  Serial.println("°C");
  delay(1000);
}
```
