{"name": "@aily-project/lib-esp32-i2s", "nickname": "ESP32 I2S音频库", "author": "aily Project", "description": "支持ESP32 I2S数字麦克风录音和功放播放的音频库，包含WAV文件录制播放、音调生成、旋律播放等功能", "version": "1.0.0", "compatibility": {"core": ["esp32:esp32", "esp32:esp32s3"], "voltage": [3.3]}, "keywords": ["esp32-i2s", "音频功放", "WAV文件", "音频录制", "音频播放", "音调生成", "旋律播放", "I2S", "音频处理", "音频库", "音频输入", "音频输出", "音频采集", "音频播放器", "数字麦克风", "I2S麦克风", "麦克风", "audio", "microphone", "speaker"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "openjumper"}