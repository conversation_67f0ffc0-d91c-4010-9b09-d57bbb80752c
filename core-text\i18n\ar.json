{"toolbox_name": "نص", "string_add_string": {"message0": "نص(%1) + نص(%2)"}, "string_charAt": {"message0": "الحرف %2 من %1"}, "string_length": {"message0": "عدد الأحرف في %1"}, "string_indexOf": {"message0": "هل يحتوي %1 على %2؟"}, "string_substring": {"message0": "%1 احصل على الأحرف من %2 %3 إلى %4 %5", "args0": [null, {"options": [["الأول", "0"], ["الأخير", "1"]]}, null, {"options": [["الأول", "0"], ["الأخير", "1"]]}, null]}, "string_find_str": {"message0": "ابحث عن %1 في %2 عند الموضع %3", "args0": [null, null, {"options": [["الأول", "indexOf"], ["الأخير", "lastIndexOf"]]}]}, "string_to": {"message0": "تحويل النص %1 إلى %2", "args0": [null, {"options": [["<PERSON><PERSON><PERSON> صحيح", "toInt"], ["<PERSON><PERSON><PERSON> عشري", "toFloat"]]}]}, "number_to": {"message0": "تحويل الرقم %1 إلى سلسلة نصية ASCII"}, "toascii": {"message0": "تحويل الحرف %1 إلى قيمة ASCII"}, "number_to_string": {"message0": "تحويل الرقم %1 إلى سلسلة نصية"}, "text": {"message0": "%1"}, "text_join": {"message0": ""}, "text_create_join_container": {"message0": "%{BKY_TEXT_CREATE_JOIN_TITLE_JOIN} %1 %2"}, "text_create_join_item": {"message0": "%{BKY_TEXT_CREATE_JOIN_ITEM_TITLE_ITEM}"}, "text_append": {"message0": "أضف النص %2 بعد %1"}, "text_length": {"message0": "طول %1"}, "text_isEmpty": {"message0": "%1 فارغ"}, "text_indexOf": {"message0": "في النص %1 %2 %3", "args0": [null, {"options": [["البحث عن أول حدوث للنص", "FIRST"], ["البحث عن آخر حدوث للنص", "LAST"]]}, null]}, "text_charAt": {"message0": "في النص %1 %2", "args0": [null, {"options": [["احصل على الحرف #", "FROM_START"], ["احصل على الحرف من النهاية #", "FROM_END"], ["احصل على أول حرف", "FIRST"], ["احصل على آخر حرف", "LAST"], ["احصل على حرف عشوائي", "RANDOM"]]}]}, "tt_getSubstring": {"message0": "احصل على سلسلة فرعية في النص %1", "message1": "%1 %2", "args1": [{"options": [["من الحرف رقم #", "FROM_START"], ["من الحرف رقم # من النهاية", "FROM_END"], ["من أول حرف", "FIRST"]]}, null], "message2": "%1 %2", "args2": [{"options": [["إلى الحرف رقم #", "FROM_START"], ["إلى الحرف رقم # من النهاية", "FROM_END"], ["إلى آخر حرف", "LAST"]]}, null]}, "text_changeCase": {"message0": "%1 %2", "args0": [{"options": [["تغيير إلى الأحرف الكبيرة", "UPPERCASE"], ["تغيير إلى الأحرف الصغيرة", "LOWERCASE"], ["تغيير إلى حالة العنوان", "TITLECASE"]]}, null]}, "text_trim": {"message0": "%1 تشذيب %2", "args0": [{"options": [["قم بتشذيب المسافات من كلا الجانبين", "BOTH"], ["قم بتشذيب المسافات من اليسار", "LEFT"], ["قم بتشذيب المسافات من اليمين", "RIGHT"]]}, null]}, "text_count": {"message0": "احسب عدد مرات ظهور %1 في %2"}, "text_replace": {"message0": "استبدل %2 بـ %3 في %1"}, "text_reverse": {"message0": "اعكس النص %1"}}