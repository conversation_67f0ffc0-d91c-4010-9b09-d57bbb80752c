{"toolbox_name": "<PERSON><PERSON><PERSON><PERSON>", "arduino_setup": {"message0": "▶️Setup %1"}, "arduino_loop": {"message0": "🔁Schleife %1"}, "controls_repeat_ext": {"message0": "%1 mal wiederholen", "message1": "Mache %1"}, "controls_repeat": {"message0": "%1 mal wiederholen", "message1": "Mache %1"}, "controls_whileUntil": {"message0": "%1 %2", "args0": [{"options": [["Wiederholen während Bedingung erfüllt ist", "WHILE"], ["Wiederholen bis Bedingung erfüllt ist", "UNTIL"]]}], "message1": "Mache %1"}, "controls_for": {"message0": "Variable %1 von %2 bis %3, erhöhe um %4", "message1": "Führe aus %1"}, "controls_flow_statements": {"message0": "%1", "args0": [{"options": [["<PERSON><PERSON><PERSON><PERSON>", "BREAK"], ["Zur nächsten Iteration fortfahren", "CONTINUE"]]}]}, "controls_whileForever": {"message0": "🔁 Endlosschleife %1"}}