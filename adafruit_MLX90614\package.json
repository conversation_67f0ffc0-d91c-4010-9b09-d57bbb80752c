{"name": "@aily-project/lib-mlx90614", "nickname": "MLX90614红外测温库", "author": "Adafruit", "description": "MLX90614红外非接触式温度传感器驱动库，可测量物体和环境温度", "version": "1.0.0", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "MLX90614", "temperature", "infrared", "sensor", "read_object_temp", "read_ambient_temp", "read_emissivity", "write_emissivity"], "scripts": {}, "dependencies": {}, "devDependencies": {}}