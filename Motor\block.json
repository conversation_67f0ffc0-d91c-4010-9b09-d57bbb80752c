[{"type": "afmotor_dc_init", "message0": "初始化DC电机 %1 频率 %2", "args0": [{"type": "field_dropdown", "name": "MOTOR", "options": [["M1", "1"], ["M2", "2"], ["M3", "3"], ["M4", "4"]]}, {"type": "field_dropdown", "name": "FREQ", "options": [["默认", "DC_MOTOR_PWM_RATE"], ["64KHz", "MOTOR12_64KHZ"], ["8KHz", "MOTOR12_8KHZ"], ["2KHz", "MOTOR12_2KHZ"], ["1KHz", "MOTOR12_1KHZ"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#D1495B", "tooltip": "初始化Adafruit电机驱动板上的DC电机"}, {"type": "afmotor_dc_run", "message0": "DC电机 %1 运行方向 %2 速度 %3", "args0": [{"type": "field_dropdown", "name": "MOTOR", "options": [["M1", "1"], ["M2", "2"], ["M3", "3"], ["M4", "4"]]}, {"type": "field_dropdown", "name": "DIRECTION", "options": [["前进", "FORWARD"], ["后退", "BACKWARD"], ["刹车", "BRAKE"], ["释放", "RELEASE"]]}, {"type": "input_value", "name": "SPEED", "check": "Number"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#D1495B", "tooltip": "控制DC电机的方向和速度"}, {"type": "afmotor_stepper_init", "message0": "初始化步进电机 %1 每转步数 %2", "args0": [{"type": "field_dropdown", "name": "STEPPER", "options": [["1(M1+M2)", "1"], ["2(M3+M4)", "2"]]}, {"type": "input_value", "name": "STEPS", "check": "Number"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#30BCED", "tooltip": "初始化步进电机，指定每转的步数"}, {"type": "afmotor_stepper_setspeed", "message0": "步进电机 %1 设置速度为 %2 RPM", "args0": [{"type": "field_dropdown", "name": "STEPPER", "options": [["1(M1+M2)", "1"], ["2(M3+M4)", "2"]]}, {"type": "input_value", "name": "RPM", "check": "Number"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#30BCED", "tooltip": "设置步进电机的转速(RPM)"}, {"type": "afmotor_stepper_step", "message0": "步进电机 %1 移动 %2 步 方向 %3 模式 %4", "args0": [{"type": "field_dropdown", "name": "STEPPER", "options": [["1(M1+M2)", "1"], ["2(M3+M4)", "2"]]}, {"type": "input_value", "name": "STEPS", "check": "Number"}, {"type": "field_dropdown", "name": "DIRECTION", "options": [["前进", "FORWARD"], ["后退", "BACKWARD"]]}, {"type": "field_dropdown", "name": "STYLE", "options": [["单相", "SINGLE"], ["双相", "DOUBLE"], ["交错", "INTERLEAVE"], ["微步进", "MICROSTEP"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#30BCED", "tooltip": "控制步进电机移动指定步数"}, {"type": "afmotor_stepper_onestep", "message0": "步进电机 %1 单步移动 方向 %2 模式 %3", "args0": [{"type": "field_dropdown", "name": "STEPPER", "options": [["1(M1+M2)", "1"], ["2(M3+M4)", "2"]]}, {"type": "field_dropdown", "name": "DIRECTION", "options": [["前进", "FORWARD"], ["后退", "BACKWARD"]]}, {"type": "field_dropdown", "name": "STYLE", "options": [["单相", "SINGLE"], ["双相", "DOUBLE"], ["交错", "INTERLEAVE"], ["微步进", "MICROSTEP"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#30BCED", "tooltip": "控制步进电机单步移动"}, {"type": "afmotor_stepper_release", "message0": "步进电机 %1 释放", "args0": [{"type": "field_dropdown", "name": "STEPPER", "options": [["1(M1+M2)", "1"], ["2(M3+M4)", "2"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#30BCED", "tooltip": "释放步进电机，停止供电"}]