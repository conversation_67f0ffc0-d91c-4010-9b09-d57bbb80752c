{"toolbox_name": "Texte", "string_add_string": {"message0": "Chaîne(%1) + <PERSON><PERSON><PERSON>(%2)"}, "string_charAt": {"message0": "Caractère %2 de %1"}, "string_length": {"message0": "Nombre de caractères dans %1"}, "string_indexOf": {"message0": "%1 contient-il %2?"}, "string_substring": {"message0": "%1 obtenir les caractères de %2 %3 à %4 %5", "args0": [null, {"options": [["le", "0"], ["l'avant-dernier", "1"]]}, null, {"options": [["le", "0"], ["l'avant-dernier", "1"]]}, null]}, "string_find_str": {"message0": "Trouver %1 dans %2 à la position %3", "args0": [null, null, {"options": [["Première", "indexOf"], ["<PERSON><PERSON><PERSON>", "lastIndexOf"]]}]}, "string_to": {"message0": "Convertir chaîne %1 en %2", "args0": [null, {"options": [["entier", "toInt"], ["flottant", "toFloat"]]}]}, "number_to": {"message0": "Convertir nombre %1 en chaîne ASCII"}, "toascii": {"message0": "Convertir caractère %1 en valeur ASCII"}, "number_to_string": {"message0": "Convertir nombre %1 en chaîne"}, "text": {"message0": "%1"}, "text_join": {"message0": ""}, "text_create_join_container": {"message0": "%{BKY_TEXT_CREATE_JOIN_TITLE_JOIN} %1 %2"}, "text_create_join_item": {"message0": "%{BKY_TEXT_CREATE_JOIN_ITEM_TITLE_ITEM}"}, "text_append": {"message0": "Ajouter texte %2 après %1"}, "text_length": {"message0": "Longueur de %1"}, "text_isEmpty": {"message0": "%1 est vide"}, "text_indexOf": {"message0": "Dans le texte %1 %2 %3", "args0": [null, {"options": [["trouver première occurrence de texte", "FIRST"], ["trouver dernière occurrence de texte", "LAST"]]}, null]}, "text_charAt": {"message0": "Dans le texte %1 %2", "args0": [null, {"options": [["Obtenir car<PERSON> #", "FROM_START"], ["Obtenir caractère à partir de fin #", "FROM_END"], ["Obtenir premier caractère", "FIRST"], ["<PERSON><PERSON><PERSON><PERSON>", "LAST"], ["Obtenir caractère aléatoire", "RANDOM"]]}]}, "tt_getSubstring": {"message0": "obtenir la sous-chaîne dans le texte %1", "message1": "%1 %2", "args1": [{"options": [["à partir du caractère #", "FROM_START"], ["à partir du # depuis la fin", "FROM_END"], ["à partir du premier caractère", "FIRST"]]}, null], "message2": "%1 %2", "args2": [{"options": [["jusqu'au caractère #", "FROM_START"], ["jusqu'au # depuis la fin", "FROM_END"], ["jusqu'au dernier caractère", "LAST"]]}, null]}, "text_changeCase": {"message0": "%1 %2", "args0": [{"options": [["changer en majuscule", "UPPERCASE"], ["changer en minuscule", "LOWERCASE"], ["changer en majuscule", "TITLECASE"]]}, null]}, "text_trim": {"message0": "%1 trim %2", "args0": [{"options": [["vider espaces des deux côtés", "BOTH"], ["vider espaces du côté gauche", "LEFT"], ["vider espaces du côté droit", "RIGHT"]]}, null]}, "text_count": {"message0": "Compter le nombre d'occurrences de %1 dans %2"}, "text_replace": {"message0": "Remplacer %2 par %3 dans %1"}, "text_reverse": {"message0": "Inverser le texte %1"}}