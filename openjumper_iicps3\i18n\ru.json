{"toolbox_name": "IICPS3", "openjumper_iicps3_init": {"message0": "Инициализация модуля контроллера IICPS3 %1"}, "openjumper_iicps3_run": {"message0": "%1 начать анализ данных"}, "openjumper_iicps3_butstate": {"message0": "%1IIC-контроллер %2 состояние кнопки", "args0": [null, {"options": [["Левая рука - вверх", "up"], ["Левая рука - вниз", "down"], ["Левая рука - влево", "left"], ["Левая рука - вправо", "right"], ["Правая рука - вверх", "triangle"], ["Правая рука - вниз", "cross"], ["Правая рука - влево", "square"], ["Правая рука - вправо", "circle"], ["Левый передний - 1", "l1"], ["Левый передний - 2", "l2"], ["<PERSON>евый стик", "l3"], ["Правый передний - 1", "r1"], ["Правый передний - 2", "r2"], ["Правый стик", "r3"], ["Выбор", "select"], ["Старт", "start"]]}]}, "openjumper_iicps3_xy": {"message0": "%1IIC-контроллер %2 данные стика", "args0": [null, {"options": [["Левый стик - ось X", "lx"], ["Левый стик - ось Y", "ly"], ["Правый стик - ось X", "rx"], ["Правый стик - ось Y", "ry"]]}]}}