{"toolbox_name": "시프트 레지스터", "74hc595_create": {"message0": "74HC595 %1 초기화 수량%2 data:%3  clock:%4 latch:%5", "args0": [null, null, {"options": "${board.digitalPins}"}, {"options": "${board.digitalPins}"}, {"options": "${board.digitalPins}"}]}, "74hc595_set": {"message0": "74HC595 %1 핀 %2 을(를) %3(으)로 설정", "args0": [null, {"options": "${board.digitalPins}"}, {"options": [["HIGH", "HIGH"], ["LOW", "LOW"]]}]}, "74hc595_setAll": {"message0": "74HC595 %1 모든 핀을 %2(으)로 설정", "args0": [null, {"options": [["HIGH", "High"], ["LOW", "Low"]]}]}, "74hc595_setAllBin": {"message0": "74HC595%1 출력 레벨 배열 %2[] 사용 설정", "args0": [null, null]}, "74hc595_getstate": {"message0": "74HC595%1 출력 %2번 핀 상태 가져오기", "args0": [null, null]}}