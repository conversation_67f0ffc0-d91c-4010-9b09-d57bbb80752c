{"author": "aily Project", "bundleDependencies": false, "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "dependencies": {}, "deprecated": false, "description": "LM35温度传感器库，提供简单易用的温度读取功能", "devDependencies": {}, "keywords": ["aily", "blockly", "temperature", "lm35", "sensor", "analog", "lm35_setup", "lm35_read"], "name": "@aily-project/lib-lm35", "nickname": "LM35温度传感器库", "scripts": {}, "version": "0.0.1", "tested": true, "tester": "K2L"}