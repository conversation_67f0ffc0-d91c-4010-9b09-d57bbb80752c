{"kind": "category", "name": "ESP32 SD", "contents": [{"kind": "block", "type": "sd_init", "fields": {"SPI_TYPE": "HSPI", "SPI_VAR_NAME": "spi"}, "inputs": {"SCK_PIN": {"shadow": {"type": "math_number", "fields": {"NUM": 3}}}, "MISO_PIN": {"shadow": {"type": "math_number", "fields": {"NUM": 35}}}, "MOSI_PIN": {"shadow": {"type": "math_number", "fields": {"NUM": 14}}}, "CS_PIN": {"shadow": {"type": "math_number", "fields": {"NUM": 46}}}}}, {"kind": "block", "type": "sd_card_type"}, {"kind": "block", "type": "sd_card_size"}, {"kind": "block", "type": "sd_total_bytes"}, {"kind": "block", "type": "sd_used_bytes"}, {"kind": "block", "type": "sd_read_file"}, {"kind": "block", "type": "sd_write_file", "fields": {"MODE": "WRITE"}, "inputs": {"DATA": {"shadow": {"type": "text", "fields": {"TEXT": "Hello, world!"}}}, "PATH": {"shadow": {"type": "text", "fields": {"TEXT": "/data.txt"}}}}}, {"kind": "block", "type": "sd_mkdir"}, {"kind": "block", "type": "sd_rmdir"}, {"kind": "block", "type": "sd_remove"}, {"kind": "block", "type": "sd_rename"}]}