{"name": "@aily-project/lib-pid", "nickname": "PID控制器库", "author": "aily Project", "description": "优化的PID控制器库，提供快速设置、参数预设、温度控制、电机调速等功能，支持Arduino各种开发板", "version": "0.1.0", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "pid", "control", "proportional", "integral", "derivative", "pid_init", "pid_compute", "pid_quick_setup", "pid_temperature_control", "pid_motor_speed_control", "pid_control_loop", "pid_set_mode", "pid_set_tunings", "pid_set_output_limits", "pid_adaptive_control", "pid_get_input", "pid_get_output", "pid_set_setpoint", "pid_set_input", "pid_is_at_setpoint", "pid_get_error", "temperature", "motor", "speed", "position", "level", "preset", "automatic", "tuning", "quick", "simplified"], "scripts": {}, "dependencies": {}, "devDependencies": {}}