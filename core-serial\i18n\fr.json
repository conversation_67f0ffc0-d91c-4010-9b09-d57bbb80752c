{"toolbox_name": "Série", "serial_begin": {"message0": "Initialiser la Série%1 avec un débit en bauds de %2"}, "serial_available": {"message0": "Série%1 le tampon a des données"}, "serial_read": {"message0": "Lire les données de la Série%1 %2", "args0": [null, {"options": [["lire", "read"], ["jeter un coup d'oeil", "peek"], ["analyser l'entier", "parseInt"], ["analyser le flottant", "parseFloat"]]}]}, "serial_print": {"message0": "La Série%1 sort %2"}, "serial_println": {"message0": "La Série%1 sort %2 avec saut de ligne"}, "serial_write": {"message0": "La Série%1 sort des données brutes %2"}, "serial_read_string": {"message0": "Lire la chaîne de la Série%1"}}