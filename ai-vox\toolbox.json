{"kind": "category", "name": "AI Vox", "icon": "fa-light fa-message-bot", "contents": [{"kind": "block", "type": "aivox_init_wifi", "inputs": {"wifi_ssid": {"shadow": {"type": "text", "fields": {"TEXT": "test"}}}, "wifi_pwd": {"shadow": {"type": "text", "fields": {"TEXT": "12345678"}}}}}, {"kind": "block", "type": "aivox_init_std"}, {"kind": "block", "type": "aivox_init_lcd"}, {"kind": "block", "type": "aivox_lcd_show_status", "inputs": {"ai_vox_status": {"shadow": {"type": "text", "fields": {"TEXT": "test"}}}}}, {"kind": "block", "type": "aivox_lcd_show_chat_message", "inputs": {"ai_vox_chat_message": {"shadow": {"type": "text", "fields": {"TEXT": "test"}}}}}, {"kind": "block", "type": "aivox_lcd_show_emotion", "inputs": {"ai_vox_emotion": {"shadow": {"type": "text", "fields": {"TEXT": "test"}}}}}, {"kind": "block", "type": "aivox_register_led_driver_status", "inputs": {"driver_name": {"shadow": {"type": "text", "fields": {"TEXT": "test"}}}, "driver_num": {"shadow": {"type": "math_number", "fields": {"NUM": 1}}}, "driver_properties": {"shadow": {"type": "text", "fields": {"TEXT": "test"}}}, "driver_open": {"shadow": {"type": "text", "fields": {"TEXT": "test"}}}, "driver_close": {"shadow": {"type": "text", "fields": {"TEXT": "test"}}}}}, {"kind": "block", "type": "aivox_register_servo_driver_status", "inputs": {"driver_name": {"shadow": {"type": "text", "fields": {"TEXT": "test"}}}, "driver_num": {"shadow": {"type": "math_number", "fields": {"NUM": 1}}}, "driver_properties": {"shadow": {"type": "text", "fields": {"TEXT": "test"}}}}}, {"kind": "block", "type": "aivox_register_ultrasonic_sensor_driver_status", "inputs": {"driver_name": {"shadow": {"type": "text", "fields": {"TEXT": "test"}}}, "driver_properties": {"shadow": {"type": "text", "fields": {"TEXT": "test"}}}}}, {"kind": "block", "type": "aivox_register_dht11_sensor_driver_status", "inputs": {"dht11_temp_name": {"shadow": {"type": "text", "fields": {"TEXT": "test"}}}, "temp_properties": {"shadow": {"type": "text", "fields": {"TEXT": "test"}}}, "dht11_humidity_name": {"shadow": {"type": "text", "fields": {"TEXT": "test"}}}, "humidity_properties": {"shadow": {"type": "text", "fields": {"TEXT": "test"}}}}}, {"kind": "block", "type": "aivox_register_analog_sensor_driver_status", "inputs": {"aivox_analog_name": {"shadow": {"type": "text", "fields": {"TEXT": "ultrasonic"}}}, "aivox_analog_desc": {"shadow": {"type": "text", "fields": {"TEXT": "abc"}}}, "aivox_analog_status": {"shadow": {"type": "text", "fields": {"TEXT": "abc"}}}}}, {"kind": "block", "type": "aivox_loop"}, {"kind": "block", "type": "aivox_event_is_activation"}, {"kind": "block", "type": "aivox_get_activation_message"}, {"kind": "block", "type": "aivox_get_activation_code"}, {"kind": "block", "type": "aivox_event_is_state_change"}, {"kind": "block", "type": "aivox_get_new_state"}, {"kind": "block", "type": "aivox_get_old_state"}, {"kind": "block", "type": "aivox_state_enum"}, {"kind": "block", "type": "aivox_event_is_emotion"}, {"kind": "block", "type": "aivox_get_emotion"}, {"kind": "block", "type": "aivox_event_is_chat_message"}, {"kind": "block", "type": "aivox_get_chat_role"}, {"kind": "block", "type": "aivox_chat_role_enum"}, {"kind": "block", "type": "aivox_get_chat_content"}, {"kind": "block", "type": "aivox_event_is_iot_message"}, {"kind": "block", "type": "aivox_get_iot_message_event_name"}, {"kind": "block", "type": "aivox_get_iot_led_message_event_fuction"}, {"kind": "block", "type": "aivox_get_iot_servo_message_event_fuction"}, {"kind": "block", "type": "aivox_get_iot_servo_message"}, {"kind": "block", "type": "aivox_update_led_iot_state", "inputs": {"aivox_drive": {"shadow": {"type": "text", "fields": {"TEXT": "LED"}}}, "aivox_drive_state": {"shadow": {"type": "logic_boolean", "fields": {"BOOL": "true"}}}}}, {"kind": "block", "type": "aivox_update_servo_iot_state", "inputs": {"aivox_drive": {"shadow": {"type": "text", "fields": {"TEXT": "LED"}}}, "aivox_drive_state": {"shadow": {"type": "math_number", "fields": {"NUM": 90}}}}}, {"kind": "block", "type": "aivox_update_all_servo_iot_state", "inputs": {"aivox_servo_num": {"shadow": {"type": "math_number", "fields": {"NUM": 2}}}, "aivox_drive_state": {"shadow": {"type": "math_number", "fields": {"NUM": 90}}}}}, {"kind": "block", "type": "aivox_update_ultrasonic_iot_state", "inputs": {"aivox_ultrasonic": {"shadow": {"type": "text", "fields": {"TEXT": "ultrasonic"}}}, "aivox_ultrasonic_distance": {"shadow": {"type": "math_number", "fields": {"NUM": 12345678}}}}}, {"kind": "block", "type": "aivox_update_dht11_iot_state", "inputs": {"aivox_dht11": {"shadow": {"type": "text", "fields": {"TEXT": "LED"}}}, "aivox_dnt11_value": {"shadow": {"type": "text", "fields": {"TEXT": "12345678"}}}}}, {"kind": "block", "type": "aivox_update_analog_sensor_iot_state", "inputs": {"aivox_analog_sensor_name": {"shadow": {"type": "text", "fields": {"TEXT": "LED"}}}, "aivox_analog_sensor_value": {"shadow": {"type": "text", "fields": {"TEXT": "12345678"}}}}}]}