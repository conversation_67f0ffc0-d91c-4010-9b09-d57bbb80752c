[{"type": "fastled_init", "message0": "初始化RGB灯带 引脚%1 类型%2 LED数量%3", "args0": [{"type": "field_dropdown", "name": "DATA_PIN", "options": "${board.digitalPins}"}, {"type": "field_dropdown", "name": "TYPE", "options": [["WS2812B", "WS2812B"], ["WS2812", "WS2812"], ["WS2811", "WS2811"], ["NEOPIXEL", "NEOPIXEL"], ["WS2801", "WS2801"], ["LPD8806", "LPD8806"], ["APA102", "APA102"]]}, {"type": "field_number", "name": "NUM_LEDS", "value": 30, "min": 1, "max": 1000}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#2A82DA"}, {"type": "fastled_set_pixel", "message0": "设置RGB灯带 引脚%1 序号 %2 颜色 %3", "args0": [{"type": "field_dropdown", "name": "DATA_PIN", "options": "${board.digitalPins}"}, {"type": "input_value", "name": "PIXEL", "check": "Number"}, {"type": "input_value", "name": "COLOR", "check": "Colour"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#2A82DA"}, {"type": "fastled_show", "message0": "更新RGB灯带", "previousStatement": null, "nextStatement": null, "colour": "#2A82DA"}, {"type": "fastled_clear", "message0": "清除RGB灯带 引脚%1", "args0": [{"type": "field_dropdown", "name": "DATA_PIN", "options": "${board.digitalPins}"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#2A82DA"}, {"type": "fastled_brightness", "message0": "设置亮度 %1", "args0": [{"type": "input_value", "name": "BRIGHTNESS", "check": "Number"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#2A82DA"}, {"type": "fastled_rgb", "message0": "RGB颜色 R %1 G %2 B %3", "args0": [{"type": "input_value", "name": "RED", "check": "Number"}, {"type": "input_value", "name": "GREEN", "check": "Number"}, {"type": "input_value", "name": "BLUE", "check": "Number"}], "inputsInline": true, "output": "Colour", "colour": "#2A82DA"}, {"type": "fastled_preset_color", "message0": "颜色 %1", "args0": [{"type": "field_colour_hsv_sliders", "name": "COLOR", "colour": "#ffffff"}], "output": "Colour", "colour": "#2A82DA"}, {"type": "fastled_fill_solid", "message0": "填充所有 LED 引脚%1 颜色 %2", "args0": [{"type": "field_dropdown", "name": "DATA_PIN", "options": "${board.digitalPins}"}, {"type": "input_value", "name": "COLOR", "check": "Colour"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#2A82DA"}, {"type": "fastled_hsv", "message0": "HSV颜色 H %1 S %2 V %3", "args0": [{"type": "input_value", "name": "HUE", "check": "Number"}, {"type": "input_value", "name": "SATURATION", "check": "Number"}, {"type": "input_value", "name": "VALUE", "check": "Number"}], "inputsInline": true, "output": "Colour", "colour": "#2A82DA"}, {"type": "fastled_rainbow", "message0": "彩虹效果 引脚%1 起始色调 %2 增量 %3", "args0": [{"type": "field_dropdown", "name": "DATA_PIN", "options": "${board.digitalPins}"}, {"type": "input_value", "name": "INITIAL_HUE", "check": "Number"}, {"type": "input_value", "name": "DELTA_HUE", "check": "Number"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#2A82DA"}, {"type": "fastled_fire_effect", "message0": "火焰效果 引脚%1 热度 %2 冷却速度 %3", "args0": [{"type": "field_dropdown", "name": "DATA_PIN", "options": "${board.digitalPins}"}, {"type": "input_value", "name": "HEAT", "check": "Number"}, {"type": "input_value", "name": "COOLING", "check": "Number"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#2A82DA"}, {"type": "fastled_meteor", "message0": "流星效果 引脚%1 颜色 %2 流星大小 %3 尾部淡出 %4 速度 %5", "args0": [{"type": "field_dropdown", "name": "DATA_PIN", "options": "${board.digitalPins}"}, {"type": "input_value", "name": "COLOR", "check": "Colour"}, {"type": "input_value", "name": "SIZE", "check": "Number"}, {"type": "input_value", "name": "DECAY", "check": "Number"}, {"type": "input_value", "name": "SPEED", "check": "Number"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#2A82DA"}, {"type": "fastled_palette_cycle", "message0": "调色板循环效果 引脚%1 调色板 %2 速度 %3", "args0": [{"type": "field_dropdown", "name": "DATA_PIN", "options": "${board.digitalPins}"}, {"type": "field_dropdown", "name": "PALETTE", "options": [["彩虹", "RainbowColors_p"], ["熔岩", "LavaColors_p"], ["云彩", "CloudColors_p"], ["海洋", "OceanColors_p"], ["森林", "ForestColors_p"], ["派对", "PartyColors_p"], ["热量", "HeatColors_p"]]}, {"type": "input_value", "name": "SPEED", "check": "Number"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#2A82DA"}, {"type": "fastled_breathing", "message0": "呼吸灯效果 引脚%1 颜色 %2 速度 %3", "args0": [{"type": "field_dropdown", "name": "DATA_PIN", "options": "${board.digitalPins}"}, {"type": "input_value", "name": "COLOR", "check": "Colour"}, {"type": "input_value", "name": "SPEED", "check": "Number"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#2A82DA"}, {"type": "fastled_twinkle", "message0": "闪烁效果 引脚%1 闪烁数量 %2 背景 %3 闪烁颜色 %4", "args0": [{"type": "field_dropdown", "name": "DATA_PIN", "options": "${board.digitalPins}"}, {"type": "input_value", "name": "COUNT", "check": "Number"}, {"type": "input_value", "name": "BACKGROUND", "check": "Colour"}, {"type": "input_value", "name": "COLOR", "check": "Colour"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#2A82DA"}]