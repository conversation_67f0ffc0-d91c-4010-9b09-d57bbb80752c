[{"inputsInline": true, "message0": "控制引脚%1舵机转动到%2", "type": "servo_write", "args0": [{"type": "field_dropdown", "name": "PIN", "options": "${board.digitalPins}"}, {"type": "input_value", "name": "ANGLE", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#03a9f4"}, {"inputsInline": true, "message0": "读取引脚%1舵机的角度", "type": "servo_read", "args0": [{"type": "field_dropdown", "name": "PIN", "options": "${board.digitalPins}"}], "output": "Number", "colour": "#03a9f4"}, {"type": "servo_angle", "message0": "%1", "args0": [{"type": "field_angle180", "name": "ANGLE", "value": 0, "minorTick": 5}], "output": "Number", "colour": "#03a9f4"}]