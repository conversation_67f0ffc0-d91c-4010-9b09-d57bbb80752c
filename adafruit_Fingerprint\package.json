{"name": "@aily-project/lib-fingerprint", "nickname": "指纹识别库", "author": "Adafruit", "description": "基于Adafruit_Fingerprint库的指纹识别支持库，支持Arduino UNO、MEGA、ESP8266、ESP32等开发板", "version": "0.0.1", "compatibility": {"core": ["arduino:avr", "esp32:esp32"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "fingerprint", "Adafruit_Fingerprint", "指纹识别"], "scripts": {}, "dependencies": {}, "devDependencies": {}}