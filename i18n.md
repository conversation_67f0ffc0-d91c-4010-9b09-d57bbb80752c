# 多语言支持  
这是库多语言规范说明，大部分情况下，你可以使用我们的AI服务自动生成对应的语言文件。  

## 多语言文件加载
多语言文件存放在库目录下的i18n目录中，如：
```
library-name  
 |- block.json             // aily blockly block文件
 |- generator.js           // aily blockly generator文件
 |- toolbox.json           // aily blockly toolbox文件
 |- package.json           // npm包管理文件
 |- src.7z                 // Arduino库源文件，请使用7z极限压缩后放在库中
 |- readme.md              // 说明文件，如果使用了开源库，请进行说明
 |- examples               // 库示例程序文件夹
 |- i18n                   // 多语言支持
     |- en.json            // 英文
     |- zh_cn.json         // 简体中文
     |- zh_tw.json         // 繁体中文
```

任意语言均可添加到库，但使用的前提是，软件本体中需要有对应的语言文件。
你可以[通过这里查看软件现已支持的语言](https://github.com/ailyProject/aily-blockly/tree/master/public/i18n)  
当软件语言为`简体中文`时，在库文件目录下，如果存在`./i18n/zh_cn.json`，软件则会加载语言文件，替换原block.json中的文本。
如不存在对应语言文件，则直接使用block.json中设定的文本。

## 多语言文件格式  
以I/O库为例说明，多语言文件结构如下：
```json
{
    "toolbox_name": "I/O 引脚",   // 此处为显示在toolbox中的文本，对应toolbox.json中的name
    "io_pinmode": {
        "message0": "引脚 %1 模式设置为 %2"   // block上显示的文本，对应type为io_pinmode的block中的message0内容
    },
    "io_digitalwrite": {
        "message0": "引脚 %1输出数字信号 %2"
    },
    "io_digitalread": {
        "message0": "读取引脚 %1 的数字信号"
    },
    "io_analogwrite": {
        "message0": "引脚 %1 输出PWM信号%2"
    },
    "io_analogread": {
        "message0": "读取引脚 %1 的模拟信号"
    },
    "io_pin_digi": {
        "message0": "数字引脚%1"
    },
    "io_pin_adc": {
        "message0": "模拟引脚%1"
    },
    "io_pin_pwm": {
        "message0": "PWM引脚%1"
    },
    "io_mode": {
        "message0": "引脚模式%1"
    },
    "io_state": {
        "message0": "%1电平"
    }
}
```

以变量库为例说明，多语言文件结构如下：
```json
{
    "toolbox_name": "变量",
    "variable_define": {
        "message0": "声明 %1 为 %2 并赋值 %3",
        "args0": [
            null,
            {
                "options": [
                    [
                        "整型",
                        "int"
                    ],
                    [
                        "长整型",
                        "long"
                    ],
                    [
                        "浮点型",
                        "float"
                    ],
                    [
                        "双精度浮点型",
                        "double"
                    ],
                    [
                        "无符号整型",
                        "unsigned int"
                    ],
                    [
                        "无符号长整型",
                        "unsigned long"
                    ],
                    [
                        "布尔型",
                        "bool"
                    ],
                    [
                        "字符型",
                        "char"
                    ],
                    [
                        "字符串型",
                        "string"
                    ]
                ]
            },
            null
        ]
    },
    "variables_set": {
        "message0": "将 %1 设为 %2"
    }
}
```

以循环库为例说明，多语言文件结构还有如下情况，需要将所有的message*都要提取到并翻译成别的语言
```json
{
    "toolbox_name": "循环",
    "variable_define": {
        "message0": "重复 %1 次",
        "args0": [
        {
            "type": "input_value",
            "name": "TIMES",
            "check": "Number"
        }
        ],
        "message1": "执行 %1",
        "args1": [
        {
            "type": "input_statement",
            "name": "DO"
        }
        ],
    }
}
```

多语言文件加载时，会自动替换message和args中的对应内容。
注意转换时args*中的options列表中每一项的第一个元素也是需要转换为对应的语言