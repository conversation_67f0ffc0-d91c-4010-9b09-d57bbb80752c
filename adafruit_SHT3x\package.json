{"name": "@aily-project/lib-adafruit-sht3x", "nickname": "SHT3x温湿度传感器", "author": "Adafruit", "description": "SHT30/SHT31/SHT35温湿度传感器控制库，I2C通讯，包含温度、湿度读取及加热器控制功能", "version": "1.0.0", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "sht31", "temperature", "humidity", "sensor", "i2c", "adafruit", "sht31_init", "sht31_read_temperature", "sht31_read_humidity", "sht31_heater_control"], "scripts": {}, "dependencies": {}, "devDependencies": {}}