{"name": "@aily-project/lib-adafruit-vl53l0x", "nickname": "VL53L0X激光测距传感器", "author": "<PERSON>Jumper", "description": "VL53L0X激光测距传感器驱动库，使用I2C通信，提供距离数据获取功能。", "version": "1.0.0", "compatibility": {"core": ["esp32:esp32", "arduino:avr"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "VL53L0X", "激光测距", "传感器"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "openjumper"}