{"name": "@aily-project/lib-mouse", "nickname": "USB模拟鼠标", "author": "<PERSON><PERSON><PERSON><PERSON>", "description": "将Arduino模拟成USB鼠标，可以实现鼠标点击、移动等功能", "version": "1.0.0", "compatibility": {"core": ["esp32:esp32s3", "esp32:esp32c3", "renesas_uno:minima", "renesas_uno:unor4wifi"], "voltage": []}, "keywords": ["aily", "blockly", "mouse", "usb", "hid"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "mango-0616"}