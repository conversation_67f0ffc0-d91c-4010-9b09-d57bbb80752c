{"name": "@aily-project/lib-arduino-rs485", "nickname": "RS485通信库", "author": "<PERSON><PERSON><PERSON><PERSON>", "description": "优化的RS485串行通信库，支持一键配置、自动初始化、主从通信等简化功能", "version": "0.1.0", "compatibility": {"core": ["arduino:avr", "arduino:samd", "renesas_uno:minima", "renesas_uno:unor4wifi"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "rs485", "serial", "communication", "max3157", "mkr485", "modbus", "industrial", "rs485_begin", "rs485_write", "rs485_read", "rs485_print", "rs485_receive", "rs485_transmission", "rs485_quick_setup", "rs485_simple_send", "rs485_simple_receive", "rs485_master_send", "rs485_slave_receive", "auto_init", "simplified"], "scripts": {}, "dependencies": {}, "devDependencies": {}}