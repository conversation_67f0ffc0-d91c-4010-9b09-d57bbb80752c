{"toolbox_name": "Benutzerdefinierter Code", "custom_code": {"message0": "Benutzerdefinierter Code %1"}, "custom_macro": {"message0": "Makrodefinition %1 als %2"}, "custom_library": {"message0": "Bibliothek einbinden %1"}, "custom_variable": {"message0": "Variable definieren Typ %1 Name %2 Anfangswert %3", "args0": [{"options": [["<PERSON><PERSON><PERSON><PERSON>", "int"], ["<PERSON>", "long*"], ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "float"], ["<PERSON><PERSON><PERSON>", "double"], ["Vorzeichenlose <PERSON>", "unsigned char"], ["Vorzeichenlose lange Ganzzahl", "unsigned char"], ["Boolescher Wert", "bool"], ["<PERSON><PERSON><PERSON>", "char"], ["Zeichenkette", "string"]]}, null, null]}, "custom_function": {"message0": "Funktionsdefinition %1 %2 Rückgabetyp %3 Parameterliste %4 %5 Funktionskörper %6", "args0": [null, null, {"options": [["<PERSON><PERSON><PERSON><PERSON>", "int"], ["<PERSON>", "long*"], ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "float"], ["<PERSON><PERSON><PERSON>", "double"], ["Vorzeichenlose <PERSON>", "unsigned char"], ["Vorzeichenlose lange Ganzzahl", "unsigned char"], ["Boolescher Wert", "bool"], ["<PERSON><PERSON><PERSON>", "char"], ["Zeichenkette", "string"]]}, null, null, null]}}