{"toolbox_name": "Сериал", "serial_begin": {"message0": "Инициализация Сериала%1 с скоростью передачи %2"}, "serial_available": {"message0": "Сериал%1 буфер имеет данные"}, "serial_read": {"message0": "Чтение данных Сериала%1 %2", "args0": [null, {"options": [["чтение", "read"], ["подглядеть", "peek"], ["анализировать целое число", "parseInt"], ["анализировать число с плавающей точкой", "parseFloat"]]}]}, "serial_print": {"message0": "Сериал%1 выводит %2"}, "serial_println": {"message0": "Сериал%1 выводит %2 и добавляет новую строку"}, "serial_write": {"message0": "Сериал%1 выводит необработанные данные %2"}, "serial_read_string": {"message0": "Чтение строки из Сериала%1"}}