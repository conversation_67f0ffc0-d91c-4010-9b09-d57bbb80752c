{"name": "@aily-project/lib-encoder", "nickname": "Arduino旋转编码器库", "author": "<PERSON>Jumper", "description": "Arduino旋转编码器驱动库，支持I2C通信，提供旋转编码器数据获取功能。", "version": "0.0.1", "compatibility": {"core": ["arduino:avr"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "<PERSON><PERSON><PERSON><PERSON>", "旋转编码器", "编码器", "传感器"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "openjumper"}