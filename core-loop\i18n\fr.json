{"toolbox_name": "<PERSON><PERSON><PERSON>", "arduino_setup": {"message0": "▶️Configurer %1"}, "arduino_loop": {"message0": "🔁Boucler %1"}, "controls_repeat_ext": {"message0": "Répéter %1 fois", "message1": "Faire %1"}, "controls_repeat": {"message0": "Répéter %1 fois", "message1": "Faire %1"}, "controls_whileUntil": {"message0": "%1 %2", "args0": [{"options": [["<PERSON><PERSON><PERSON><PERSON><PERSON> tant que la condition est remplie", "WHILE"], ["<PERSON><PERSON><PERSON><PERSON><PERSON> jusqu'à ce que la condition soit remplie", "UNTIL"]]}], "message1": "Faire %1"}, "controls_for": {"message0": "Variable %1 de %2 à %3, incrémenter de %4", "message1": "Exécuter %1"}, "controls_flow_statements": {"message0": "%1", "args0": [{"options": [["<PERSON><PERSON><PERSON> boucle", "BREAK"], ["Continuer à la prochaine itération", "CONTINUE"]]}]}, "controls_whileForever": {"message0": "🔁 Boucle infinie %1"}}