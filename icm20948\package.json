{"name": "@aily-project/lib-icm20948", "nickname": "ICM20948九轴传感器", "author": "aily Project", "description": "ICM20948九轴传感器支持库，支持加速度计、陀螺仪、磁力计和AHRS姿态解算", "version": "1.0.0", "compatibility": {"core": ["arduino:avr", "esp32:esp32", "esp32:esp32c3", "esp32:esp32s3", "renesas_uno:minima", "renesas_uno:unor4wifi"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "icm20948", "imu", "ahrs", "accelerometer", "gyroscope", "magnetometer", "sensor"], "scripts": {}, "dependencies": {}, "devDependencies": {}}