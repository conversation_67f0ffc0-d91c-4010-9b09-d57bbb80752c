[{"type": "ble_keyboard_begin", "message0": "初始化BLE键盘 %1", "args0": [{"type": "field_input", "name": "DEVICE_NAME", "text": "ESP32 Keyboard"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00BCD4"}, {"type": "ble_keyboard_end", "message0": "断开BLE键盘连接", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00BCD4"}, {"type": "ble_keyboard_is_connected", "message0": "BLE键盘是否已连接", "inputsInline": true, "output": "Boolean", "colour": "#00BCD4"}, {"type": "ble_keyboard_print", "message0": "BLE键盘输出字符串 %1", "args0": [{"type": "input_value", "name": "TEXT", "check": "String"}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00BCD4"}, {"type": "ble_keyboard_press", "message0": "BLE键盘按下键 %1", "args0": [{"type": "input_value", "name": "KEY", "check": ["String", "Number"]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00BCD4"}, {"type": "ble_keyboard_release", "message0": "BLE键盘释放键 %1", "args0": [{"type": "input_value", "name": "KEY", "check": ["String", "Number"]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00BCD4"}, {"type": "ble_keyboard_release_all", "message0": "BLE键盘释放所有按键", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00BCD4"}, {"type": "ble_keyboard_special_key", "message0": "BLE键盘特殊键 %1", "args0": [{"type": "field_dropdown", "name": "KEY", "options": [["Enter", "KEY_ENTER"], ["Esc", "KEY_ESC"], ["Backspace", "KEY_BACKSPACE"], ["Tab", "KEY_TAB"], ["空格", "KEY_SPACE"], ["左Ctrl", "KEY_LEFTCTRL"], ["左Shift", "KEY_LEFTSHIFT"], ["左Alt", "KEY_LEFTALT"], ["左GUI/Windows", "KEY_LEFTMETA"], ["右Ctrl", "KEY_RIGHTCTRL"], ["右Shift", "KEY_RIGHTSHIFT"], ["右Alt", "KEY_RIGHTALT"], ["右GUI/Windows", "KEY_RIGHTMETA"], ["向上箭头", "KEY_UP"], ["向下箭头", "KEY_DOWN"], ["向左箭头", "KEY_LEFT"], ["向右箭头", "KEY_RIGHT"], ["Insert", "KEY_INSERT"], ["Delete", "KEY_DELETE"], ["Home", "KEY_HOME"], ["End", "KEY_END"], ["Page Up", "KEY_PAGEUP"], ["Page Down", "KEY_PAGEDOWN"], ["F1", "KEY_F1"], ["F2", "KEY_F2"], ["F3", "KEY_F3"], ["F4", "KEY_F4"], ["F5", "KEY_F5"], ["F6", "KEY_F6"], ["F7", "KEY_F7"], ["F8", "KEY_F8"], ["F9", "KEY_F9"], ["F10", "KEY_F10"], ["F11", "KEY_F11"], ["F12", "KEY_F12"]]}], "output": "Number", "colour": "#00BCD4"}, {"type": "ble_keyboard_write", "message0": "BLE键盘快速输入 %1", "args0": [{"type": "input_value", "name": "KEY", "check": ["String", "Number"]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00BCD4"}, {"type": "ble_keyboard_media_key", "message0": "BLE键盘媒体键 %1", "args0": [{"type": "field_dropdown", "name": "MEDIA_KEY", "options": [["播放/暂停", "KEY_MEDIA_PLAYPAUSE"], ["停止", "KEY_MEDIA_STOP"], ["下一曲", "KEY_MEDIA_NEXTTRACK"], ["上一曲", "KEY_MEDIA_PREVIOUSTRACK"], ["静音", "KEY_MEDIA_MUTE"], ["音量加", "KEY_MEDIA_VOLUMEUP"], ["音量减", "KEY_MEDIA_VOLUMEDOWN"], ["快进", "KEY_MEDIA_FASTFORWARD"], ["快退", "KEY_MEDIA_REWIND"], ["弹出", "KEY_MEDIA_EJECT"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00BCD4"}, {"type": "ble_keyboard_consumer_key", "message0": "BLE键盘消费者键 %1", "args0": [{"type": "field_dropdown", "name": "CONSUMER_KEY", "options": [["计算器", "KEY_MEDIA_CALCULATOR"], ["主页", "KEY_MEDIA_WWWHOME"], ["邮件", "KEY_MEDIA_MAIL"], ["我的电脑", "KEY_MEDIA_MYCOMPUTER"], ["搜索", "KEY_MEDIA_WWWSEARCH"], ["返回", "KEY_MEDIA_WWWBACK"], ["停止", "KEY_MEDIA_WWWSTOP"], ["收藏夹", "KEY_MEDIA_WWWFAVORITES"], ["媒体选择", "KEY_MEDIA_MEDIASELECT"]]}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00BCD4"}, {"type": "ble_keyboard_set_battery_level", "message0": "BLE键盘设置电池电量 %1 %", "args0": [{"type": "field_number", "name": "LEVEL", "value": 100, "min": 0, "max": 100}], "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#00BCD4"}]