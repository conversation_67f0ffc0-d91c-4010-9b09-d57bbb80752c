{"toolbox_name": "循環", "arduino_setup": {"message0": "▶️初始化 %1"}, "arduino_loop": {"message0": "🔁循環執行 %1"}, "controls_repeat_ext": {"message0": "重複 %1 次", "message1": "執行 %1"}, "controls_repeat": {"message0": "重複 %1 次", "message1": "執行 %1"}, "controls_whileUntil": {"message0": "%1 %2", "args0": [{"options": [["當條件滿足時重複", "WHILE"], ["重複直到條件滿足", "UNTIL"]]}], "message1": "執行 %1"}, "controls_for": {"message0": "變量 %1 從 %2 到 %3 每次增加 %4", "message1": "運行 %1"}, "controls_flow_statements": {"message0": "%1", "args0": [{"options": [["跳出循環", "BREAK"], ["繼續下一輪循環", "CONTINUE"]]}]}, "controls_whileForever": {"message0": "🔁 永遠循環 %1"}}