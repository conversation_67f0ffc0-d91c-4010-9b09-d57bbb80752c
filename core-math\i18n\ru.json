{"toolbox_name": "Математика", "math_number": {"message0": "%1"}, "math_arithmetic": {"message0": "%1 %2 %3", "args0": [null, {"options": [["Сложение", "ADD"], ["Вычитание", "MINUS"], ["Умножение", "MULTIPLY"], ["Деление", "DIVIDE"], ["Остаток", "MODULO"], ["Степень", "POWER"]]}, null]}, "math_single": {"message0": "%1 %2", "args0": [{"options": [["Квадратный корень", "ROOT"], ["Абсолютное значение", "ABS"], ["Отрицательное", "NEG"], ["ln", "LN"], ["log10", "LOG10"], ["e^", "EXP"], ["10^", "POW10"]]}, null]}, "math_trig": {"message0": "%1 %2", "args0": [{"options": [["Син<PERSON>с", "SIN"], ["Ко<PERSON><PERSON><PERSON><PERSON><PERSON>", "COS"], ["Та<PERSON><PERSON><PERSON><PERSON><PERSON>", "TAN"], ["Аркси<PERSON><PERSON>с", "ASIN"], ["Арккосинус", "ACOS"], ["Арк<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ATAN"]]}, null]}, "math_constant": {"message0": "%1", "args0": [{"options": [["π", "PI"], ["e", "E"], ["Золотое сечение", "GOLDEN_RATIO"], ["√2", "SQRT2"], ["√1/2", "SQRT1_2"], ["∞", "INFINITY"]]}]}, "math_number_property": {"message0": "%1 %2", "args0": [null, {"options": [["четное число", "EVEN"], ["нечетное число", "ODD"], ["простое число", "PRIME"], ["целое число", "WHOLE"], ["положительное число", "POSITIVE"], ["отрицательное число", "NEGATIVE"], ["делится на", "DIVISIBLE_BY"]]}]}, "math_change": {"message0": "изменить %1 на %2"}, "math_round": {"message0": "%1 %2", "args0": [{"options": [["округлить", "ROUND"], ["округлить вверх", "ROUNDUP"], ["округлить вниз", "ROUNDDOWN"]]}, null]}, "math_on_list": {"message0": "%1 %2", "args0": [{"options": [["Сумма списка", "SUM"], ["Минимум списка", "MIN"], ["Максимум списка", "MAX"], ["Среднее списа", "AVERAGE"], ["Медиана списка", "MEDIAN"], ["Мода списка", "MODE"], ["Стандартное отклонение списка", "STD_DEV"], ["Случайный элемент списка", "RANDOM"]]}, null]}, "math_modulo": {"message0": "остаток от %1 ÷ %2"}, "math_constrain": {"message0": "ограничить %1 между %2 и %3"}, "math_random_int": {"message0": "случайное целое от %1 до %2"}, "math_random_float": {"message0": "случайная дробь"}, "math_atan2": {"message0": "арктангенс точки (x: %1, y: %2)"}, "math_round_to_decimal": {"message0": "округлить %1 до %2 десятичных знаков"}, "math_bitwise_not": {"message0": "~ %1"}, "map_to": {"message0": "Отобразить %1 с [%2,%3] на [%4,%5]"}, "constrain": {"message0": "Ограничить %1 между (мин) %2 и (макс) %3"}}