[{"inputsInline": true, "message0": "初始化串口%1 设置波特率为%2", "type": "serial_begin", "colour": "#48c2c4", "args0": [{"type": "field_dropdown", "name": "SERIAL", "options": "${board.serialPort}"}, {"type": "field_dropdown", "name": "SPEED", "options": "${board.serialSpeed}"}], "previousStatement": null, "nextStatement": null}, {"inputsInline": true, "message0": "串口%1缓冲区有数据", "type": "serial_available", "colour": "#48c2c4", "args0": [{"type": "field_dropdown", "name": "SERIAL", "options": "${board.serialPort}"}], "output": "Number"}, {"inputsInline": true, "message0": "读取串口%1数据 %2", "type": "serial_read", "colour": "#48c2c4", "args0": [{"type": "field_dropdown", "name": "SERIAL", "options": "${board.serialPort}"}, {"type": "field_dropdown", "name": "TYPE", "options": [["read", "read"], ["peek", "peek"], ["parseInt", "parseInt"], ["parseFloat", "parseFloat"]]}], "output": "Any"}, {"inputsInline": true, "message0": "串口%1输出%2", "type": "serial_print", "colour": "#48c2c4", "args0": [{"type": "field_dropdown", "name": "SERIAL", "options": "${board.serialPort}"}, {"type": "input_value", "name": "VAR"}], "previousStatement": null, "nextStatement": null}, {"inputsInline": true, "message0": "串口%1输出%2并换行", "type": "serial_println", "colour": "#48c2c4", "args0": [{"type": "field_dropdown", "name": "SERIAL", "options": "${board.serialPort}"}, {"type": "input_value", "name": "VAR"}], "previousStatement": null, "nextStatement": null}, {"inputsInline": true, "message0": "串口%1输出原始数据%2", "type": "serial_write", "colour": "#48c2c4", "args0": [{"type": "field_dropdown", "name": "SERIAL", "options": "${board.serialPort}"}, {"type": "input_value", "name": "DATA"}], "previousStatement": null, "nextStatement": null}, {"inputsInline": true, "message0": "读取串口%1字符串", "type": "serial_read_string", "colour": "#48c2c4", "args0": [{"type": "field_dropdown", "name": "SERIAL", "options": "${board.serialPort}"}], "output": "String"}]