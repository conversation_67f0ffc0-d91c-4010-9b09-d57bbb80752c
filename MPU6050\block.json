[{"type": "mpu6050_begin", "message0": "初始化MPU6050传感器", "previousStatement": null, "nextStatement": null, "colour": "#2C3E50", "tooltip": "初始化MPU6050传感器", "helpUrl": ""}, {"type": "mpu6050_get_accel", "message0": "获取加速度数据 %1 轴 (g)", "args0": [{"type": "field_dropdown", "name": "AXIS", "options": [["X", "x"], ["Y", "y"], ["Z", "z"]]}], "output": "Number", "colour": "#2C3E50", "tooltip": "获取X、Y或Z轴的加速度数据，单位为g", "helpUrl": ""}, {"type": "mpu6050_get_gyro", "message0": "获取陀螺仪数据 %1 轴 (°/s)", "args0": [{"type": "field_dropdown", "name": "AXIS", "options": [["X", "x"], ["Y", "y"], ["Z", "z"]]}], "output": "Number", "colour": "#2C3E50", "tooltip": "获取X、Y或Z轴的陀螺仪数据，单位为°/s", "helpUrl": ""}, {"type": "mpu6050_get_temp", "message0": "获取温度 (°C)", "output": "Number", "colour": "#2C3E50", "tooltip": "获取MPU6050温度数据，单位为°C", "helpUrl": ""}, {"type": "mpu6050_set_accel_range", "message0": "设置加速度计量程 %1", "args0": [{"type": "field_dropdown", "name": "RANGE", "options": [["±2g", "MPU6050_RANGE_2_G"], ["±4g", "MPU6050_RANGE_4_G"], ["±8g", "MPU6050_RANGE_8_G"], ["±16g", "MPU6050_RANGE_16_G"]]}], "previousStatement": null, "nextStatement": null, "colour": "#2C3E50", "tooltip": "设置MPU6050加速度计量程", "helpUrl": ""}, {"type": "mpu6050_set_gyro_range", "message0": "设置陀螺仪量程 %1", "args0": [{"type": "field_dropdown", "name": "RANGE", "options": [["±250°/s", "MPU6050_RANGE_250_DEG"], ["±500°/s", "MPU6050_RANGE_500_DEG"], ["±1000°/s", "MPU6050_RANGE_1000_DEG"], ["±2000°/s", "MPU6050_RANGE_2000_DEG"]]}], "previousStatement": null, "nextStatement": null, "colour": "#2C3E50", "tooltip": "设置MPU6050陀螺仪量程", "helpUrl": ""}, {"type": "mpu6050_set_filter_bandwidth", "message0": "设置滤波带宽 %1", "args0": [{"type": "field_dropdown", "name": "BANDWIDTH", "options": [["260Hz", "MPU6050_BAND_260_HZ"], ["184Hz", "MPU6050_BAND_184_HZ"], ["94Hz", "MPU6050_BAND_94_HZ"], ["44Hz", "MPU6050_BAND_44_HZ"], ["21Hz", "MPU6050_BAND_21_HZ"], ["10Hz", "MPU6050_BAND_10_HZ"], ["5Hz", "MPU6050_BAND_5_HZ"]]}], "previousStatement": null, "nextStatement": null, "colour": "#2C3E50", "tooltip": "设置MPU6050滤波带宽", "helpUrl": ""}]