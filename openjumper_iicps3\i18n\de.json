{"toolbox_name": "IICPS3", "openjumper_iicps3_init": {"message0": "IICPS3-Controller %1 Modul initialisieren"}, "openjumper_iicps3_run": {"message0": "%1 Datenanalyse starten"}, "openjumper_iicps3_butstate": {"message0": "%1IIC-Controller %2 Tastenstatus", "args0": [null, {"options": [["Links - <PERSON><PERSON>", "up"], ["Links - <PERSON><PERSON>", "down"], ["Links - Links", "left"], ["Links <PERSON> <PERSON><PERSON><PERSON>", "right"], ["Rechts - Oben", "triangle"], ["Rechts - Unten", "cross"], ["Rechts - Links", "square"], ["Rechts - Rechts", "circle"], ["Linke Front - 1", "l1"], ["Linke Front - 2", "l2"], ["Linker Joystick", "l3"], ["Rechte Front - 1", "r1"], ["Rechte Front - 2", "r2"], ["<PERSON><PERSON><PERSON>", "r3"], ["Auswählen", "select"], ["Start", "start"]]}]}, "openjumper_iicps3_xy": {"message0": "%1IIC-Controller %2 Joystick-Daten", "args0": [null, {"options": [["<PERSON><PERSON> Joystick - <PERSON>-<PERSON><PERSON><PERSON>", "lx"], ["<PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>", "ly"], ["<PERSON><PERSON>er Joystick - X-<PERSON><PERSON>e", "rx"], ["<PERSON><PERSON><PERSON> Joystick - Y-<PERSON><PERSON>e", "ry"]]}]}}