[{"type": "si7021_begin", "message0": "初始化Si7021传感器%1 使用 %2", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "sensor", "variableTypes": ["Si7021"], "defaultType": "Si7021"}, {"type": "field_dropdown", "name": "WIRE", "options": "${board.i2c}"}], "previousStatement": null, "nextStatement": null, "colour": "#4CAF50", "inputsInline": true}, {"type": "si7021_read_temperature", "message0": "读取Si7021传感器%1温度", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "sensor", "variableTypes": ["Si7021"], "defaultType": "Si7021"}], "output": "Number", "colour": "#FF9800", "inputsInline": true}, {"type": "si7021_read_humidity", "message0": "读取Si7021传感器%1湿度", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "sensor", "variableTypes": ["Si7021"], "defaultType": "Si7021"}], "output": "Number", "colour": "#2196F3", "inputsInline": true}, {"type": "si7021_heater_control", "message0": "Si7021传感器%1加热器%2", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "sensor", "variableTypes": ["Si7021"], "defaultType": "Si7021"}, {"type": "field_dropdown", "name": "STATE", "options": [["启用", "true"], ["禁用", "false"]]}], "previousStatement": null, "nextStatement": null, "colour": "#E91E63", "inputsInline": true}, {"type": "si7021_is_heater_enabled", "message0": "Si7021传感器%1加热器是否启用", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "sensor", "variableTypes": ["Si7021"], "defaultType": "Si7021"}], "output": "Boolean", "colour": "#E91E63", "inputsInline": true}, {"type": "si7021_set_heat_level", "message0": "设置Si7021传感器%1加热器等级%2", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "sensor", "variableTypes": ["Si7021"], "defaultType": "Si7021"}, {"type": "field_dropdown", "name": "LEVEL", "options": [["最低", "SI_HEATLEVEL_LOWEST"], ["低", "SI_HEATLEVEL_LOW"], ["中", "SI_HEATLEVEL_MEDIUM"], ["高", "SI_HEATLEVEL_HIGH"], ["更高", "SI_HEATLEVEL_HIGHER"], ["最高", "SI_HEATLEVEL_HIGHEST"]]}], "previousStatement": null, "nextStatement": null, "colour": "#E91E63", "inputsInline": true}, {"type": "si7021_get_model", "message0": "获取Si7021传感器%1型号", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "sensor", "variableTypes": ["Si7021"], "defaultType": "Si7021"}], "output": "String", "colour": "#9C27B0", "inputsInline": true}, {"type": "si7021_get_revision", "message0": "获取Si7021传感器%1版本", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "sensor", "variableTypes": ["Si7021"], "defaultType": "Si7021"}], "output": "Number", "colour": "#9C27B0", "inputsInline": true}, {"type": "si7021_reset", "message0": "重置Si7021传感器%1", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "sensor", "variableTypes": ["Si7021"], "defaultType": "Si7021"}], "previousStatement": null, "nextStatement": null, "colour": "#607D8B", "inputsInline": true}, {"type": "si7021_read_serial_number", "message0": "读取Si7021传感器%1序列号", "args0": [{"type": "field_variable", "name": "OBJECT", "variable": "sensor", "variableTypes": ["Si7021"], "defaultType": "Si7021"}], "previousStatement": null, "nextStatement": null, "colour": "#607D8B", "inputsInline": true}]