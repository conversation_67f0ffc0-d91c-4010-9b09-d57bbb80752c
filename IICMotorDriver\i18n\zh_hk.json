{"toolbox_name": "IIC馬達驅動", "iicmd_init": {"message0": "馬達驅動初始化"}, "iicmd_dirinit": {"message0": "馬達參考方向初始化 馬達1%1 馬達2%2 馬達3%3 馬達4%4", "args0": [{"options": [["正向", "DIRP"], ["反向", "DIRN"]]}, {"options": [["正向", "DIRP"], ["反向", "DIRN"]]}, {"options": [["正向", "DIRP"], ["反向", "DIRN"]]}, {"options": [["正向", "DIRP"], ["反向", "DIRN"]]}]}, "iicmd_stop": {"message0": "設置 %1 停止", "args0": [{"options": [["馬達1", "M1"], ["馬達2", "M2"], ["馬達3", "M3"], ["馬達4", "M4"], ["全部馬達", "MALL"]]}]}, "iicmd_runone": {"message0": "設置馬達 %1 轉速為%2", "args0": [{"options": [["馬達1", "M1"], ["馬達2", "M2"], ["馬達3", "M3"], ["馬達4", "M4"]]}, null]}, "iicmd_runall": {"message0": "設置全部馬達轉速 %1", "args0": [null]}, "iicmd_runall2": {"message0": "設置全部馬達速度 馬達1%1 馬達2%2 馬達3%3 馬達4%4", "args0": [null, null, null, null]}, "iicmd_digitout": {"message0": "控制端口%1 輸出 %2", "args0": [{"options": [["端口1", "S1"], ["端口2", "S2"], ["端口3", "S3"], ["端口4", "S4"]]}, {"options": [["高電平", "HIGH"], ["低電平", "LOW"]]}]}, "iicmd_servo": {"message0": "舵機端口%1 轉動到(0-180) %2°", "args0": [{"options": [["端口1", "S1"], ["端口2", "S2"], ["端口3", "S3"], ["端口4", "S4"]]}, null]}}