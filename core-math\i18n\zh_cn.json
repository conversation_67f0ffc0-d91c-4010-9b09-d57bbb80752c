{"toolbox_name": "数学", "math_number": {"message0": "%1"}, "math_arithmetic": {"message0": "%1 %2 %3", "args0": [null, {"options": [["加", "ADD"], ["减", "MINUS"], ["乘", "MULTIPLY"], ["除", "DIVIDE"], ["取余", "MODULO"], ["幂", "POWER"]]}, null]}, "math_single": {"message0": "%1 %2", "args0": [{"options": [["平方根", "ROOT"], ["绝对值", "ABS"], ["负", "NEG"], ["ln", "LN"], ["log10", "LOG10"], ["e^", "EXP"], ["10^", "POW10"]]}, null]}, "math_trig": {"message0": "%1 %2", "args0": [{"options": [["正弦", "SIN"], ["余弦", "COS"], ["正切", "TAN"], ["反正弦", "ASIN"], ["反余弦", "ACOS"], ["反正切", "ATAN"]]}, null]}, "math_constant": {"message0": "%1", "args0": [{"options": [["π", "PI"], ["e", "E"], ["黄金比例", "GOLDEN_RATIO"], ["sqrt(2)", "SQRT2"], ["sqrt(1/2)", "SQRT1_2"], ["∞", "INFINITY"]]}]}, "math_number_property": {"message0": "%1 %2", "args0": [null, {"options": [["是偶数", "EVEN"], ["是奇数", "ODD"], ["是质数", "PRIME"], ["是整数", "WHOLE"], ["是正数", "POSITIVE"], ["是负数", "NEGATIVE"], ["可整除", "DIVISIBLE_BY"]]}]}, "math_change": {"message0": "改变 %1 到 %2"}, "math_round": {"message0": "%1 %2", "args0": [{"options": [["四舍五入", "ROUND"], ["向上舍入", "ROUNDUP"], ["向下舍入", "ROUNDDOWN"]]}, null]}, "math_on_list": {"message0": "%1 %2", "args0": [{"options": [["列表的和", "SUM"], ["列表的最小数", "MIN"], ["列表的最大数", "MAX"], ["列表的平均值", "AVERAGE"], ["列表的中位数", "MEDIAN"], ["列表的众数", "MODE"], ["列表的标准差", "STD_DEV"], ["列表的随机项", "RANDOM"]]}, null]}, "math_modulo": {"message0": "%1 ÷ %2 的余数"}, "math_constrain": {"message0": "将 %1 限制在最小 %2 到最大 %3 之间"}, "math_random_int": {"message0": "从 %1 到 %2 范围内的随机整数"}, "math_random_float": {"message0": "随机小数"}, "math_atan2": {"message0": "点(x: %1, y: %2)的方位角"}, "math_round_to_decimal": {"message0": "保留 %2 位小数的 %1"}, "math_bitwise_not": {"message0": "~ %1"}, "map_to": {"message0": "映射 %1 从[%2,%3]到[%4,%5]"}}