{"toolbox_name": "FastLED", "fastled_init": {"message0": "Initialiser bande LED RVB broche %1 type %2 nombre de LED %3", "args0": [null, {"options": [["WS2812B", "WS2812B"], ["WS2812", "WS2812"], ["WS2811", "WS2811"], ["NEOPIXEL", "NEOPIXEL"], ["WS2801", "WS2801"], ["LPD8806", "LPD8806"], ["APA102", "APA102"]]}, null]}, "fastled_set_pixel": {"message0": "Définir bande LED RVB broche %1 numéro %2 couleur %3"}, "fastled_show": {"message0": "Mettre à jour la bande LED RVB"}, "fastled_clear": {"message0": "Effacer la bande LED RVB broche %1"}, "fastled_brightness": {"message0": "Définir la luminosité %1"}, "fastled_rgb": {"message0": "Couleur RVB R %1 V %2 B %3"}, "fastled_preset_color": {"message0": "Couleur %1"}, "fastled_fill_solid": {"message0": "Remplir toutes les LED broche %1 couleur %2"}, "fastled_hsv": {"message0": "Couleur HSV H %1 S %2 V %3"}, "fastled_rainbow": {"message0": "Effet arc-en-ciel broche %1 teinte de départ %2 incrément %3"}, "fastled_fire_effect": {"message0": "Effet feu broche %1 chaleur %2 vitesse de refroidissement %3"}, "fastled_meteor": {"message0": "Effet météore broche %1 couleur %2 taille du météore %3 fondu de la queue %4 vitesse %5"}, "fastled_palette_cycle": {"message0": "Effet cycle de palette broche %1 palette %2 vitesse %3", "args0": [null, {"options": [["Arc-en-ciel", "RainbowColors_p"], ["Lave", "LavaColors_p"], ["Nuages", "CloudColors_p"], ["<PERSON><PERSON><PERSON>", "OceanColors_p"], ["<PERSON><PERSON><PERSON>", "ForestColors_p"], ["<PERSON><PERSON><PERSON>", "PartyColors_p"], ["<PERSON><PERSON><PERSON>", "HeatColors_p"]]}, null]}, "fastled_breathing": {"message0": "Effet respiration broche %1 couleur %2 vitesse %3"}, "fastled_twinkle": {"message0": "Effet scintillement broche %1 nombre de scintillements %2 fond %3 couleur scintillement %4"}}