{"toolbox_name": "循环", "arduino_setup": {"message0": "▶️初始化 %1"}, "arduino_loop": {"message0": "🔁循环执行 %1"}, "controls_repeat_ext": {"message0": "重复 %1 次", "message1": "执行 %1"}, "controls_repeat": {"message0": "重复 %1 次", "message1": "执行 %1"}, "controls_whileUntil": {"message0": "%1 %2", "args0": [{"options": [["当条件满足时重复", "WHILE"], ["重复直到条件满足", "UNTIL"]]}], "message1": "执行 %1"}, "controls_for": {"message0": "变量 %1 从 %2 到 %3 每次增加 %4", "message1": "运行 %1"}, "controls_flow_statements": {"message0": "%1", "args0": [{"options": [["跳出循环", "BREAK"], ["继续下一轮循环", "CONTINUE"]]}]}, "controls_whileForever": {"message0": "🔁 永远循环 %1"}}