# Adafruit SHT31 温湿度传感器库

## 简介

这是一个用于 Adafruit SHT31 温湿度传感器的 Arduino Blockly 库。SHT31 是一款高精度的数字温湿度传感器，使用 I2C 接口进行通讯。

## 传感器特性

- **温度测量范围**: -40°C 到 +125°C
- **湿度测量范围**: 0% 到 100% RH
- **精度**: ±0.3°C (温度), ±2% RH (湿度)
- **接口**: I2C (地址: 0x44 或 0x45)
- **供电电压**: 2.4V - 5.5V
- **内置加热器**: 用于去除传感器表面的冷凝水

## 硬件连接

| SHT31引脚 | Arduino引脚 | 说明 |
|-----------|-------------|------|
| VIN | 3.3V 或 5V | 电源正极 |
| GND | GND | 电源负极 |
| SCL | A5 (UNO) / 22 (ESP32) | I2C时钟线 |
| SDA | A4 (UNO) / 21 (ESP32) | I2C数据线 |

## 积木块说明

### 基础功能

#### 初始化传感器
- **积木**: `初始化SHT31传感器 地址[0x44/0x45]`
- **功能**: 初始化 SHT31 传感器并设置 I2C 地址
- **参数**: I2C地址 (0x44默认, 0x45备用)

#### 读取温度
- **积木**: `SHT31读取温度(°C)`
- **功能**: 读取当前温度值
- **返回**: 温度值(摄氏度)

#### 读取湿度  
- **积木**: `SHT31读取湿度(%)`
- **功能**: 读取当前湿度值
- **返回**: 湿度值(百分比)

#### 同时读取温湿度
- **积木**: `SHT31读取 温度变量[temperature] 湿度变量[humidity]`
- **功能**: 同时读取温度和湿度值到指定变量
- **优点**: 减少 I2C 通讯次数，提高效率

### 简化功能

#### 简化读取
- **积木**: `SHT31传感器读取[温度(°C)/湿度(%)]`
- **功能**: 简化版读取，自动初始化传感器
- **适用**: 快速原型开发，无需手动初始化

### 高级功能

#### 加热器控制
- **积木**: `SHT31加热器[开启/关闭]`
- **功能**: 控制内置加热器开关
- **用途**: 去除传感器表面冷凝水，提高测量精度

#### 检查加热器状态
- **积木**: `SHT31加热器是否开启`
- **功能**: 检查加热器当前状态
- **返回**: 布尔值 (true/false)

#### 软件复位
- **积木**: `SHT31软件复位`
- **功能**: 对传感器执行软件复位
- **用途**: 重置传感器状态

## 使用示例

### 基础温湿度读取

```
初始化SHT31传感器 地址[0x44]
永远重复执行
    串口打印 连接文本["温度: "] [SHT31读取温度(°C)] ["°C"]
    串口打印 连接文本["湿度: "] [SHT31读取湿度(%)] ["%"]
    等待 1000 毫秒
```

### 简化使用示例

```
永远重复执行
    串口打印 连接文本["温度: "] [SHT31传感器读取[温度(°C)]] ["°C"]
    串口打印 连接文本["湿度: "] [SHT31传感器读取[湿度(%)]] ["%"]
    等待 1000 毫秒
```

### 同时读取示例

```
初始化SHT31传感器 地址[0x44]
永远重复执行
    SHT31读取 温度变量[temp] 湿度变量[hum]
    串口打印 连接文本["温度: "] [temp] ["°C, 湿度: "] [hum] ["%"]
    等待 1000 毫秒
```

## 注意事项

1. **I2C地址**: 默认地址为0x44，如有冲突可使用0x45
2. **电源电压**: 支持3.3V和5V系统
3. **响应时间**: 传感器需要约15ms完成一次测量
4. **加热器功能**: 加热器开启时温度会上升约3°C，用于除湿
5. **错误处理**: 如果读取失败，函数返回NaN，可用isnan()函数检查

## 开源库信息

本库基于 Adafruit SHT31 Arduino 库开发:
- **原始库**: [Adafruit_SHT31](https://github.com/adafruit/Adafruit_SHT31)
- **许可证**: BSD License
- **作者**: Adafruit Industries

## 版本历史

- **v1.0.0**: 初始版本，支持基础温湿度读取、加热器控制等功能
