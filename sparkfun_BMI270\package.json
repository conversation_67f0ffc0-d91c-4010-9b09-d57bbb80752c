{"name": "@aily-project/lib-sparkfun_BMI270", "nickname": "BMI270传感器驱动库", "author": "<PERSON>Jumper", "description": "BMI270传感器驱动库适用于esp32，支持I2C通信，提供加速度、陀螺仪数据获取功能。", "version": "0.0.1", "compatibility": {"core": ["esp32:esp32", "esp32:esp32s3"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "BMI270", "加速度", "陀螺仪", "校准", "传感器"], "scripts": {}, "dependencies": {}, "devDependencies": {}, "tested": true, "tester": "openjumper"}