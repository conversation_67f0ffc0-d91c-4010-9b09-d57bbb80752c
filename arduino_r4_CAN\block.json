[{"type": "can_begin", "message0": "初始化CAN总线 速率 %1", "args0": [{"type": "field_dropdown", "name": "BITRATE", "options": [["125Kbps", "CanBitRate::BR_125k"], ["250Kbps", "CanBitRate::BR_250k"], ["500Kbps", "CanBitRate::BR_500k"], ["1Mbps", "CanBitRate::BR_1m"]]}], "previousStatement": null, "nextStatement": null, "colour": "#2C97DF", "inputsInline": true}, {"type": "can_available", "message0": "CAN总线有新消息可读", "output": "Boolean", "colour": "#2C97DF", "inputsInline": true}, {"type": "can_read", "message0": "读取CAN消息到变量 %1", "args0": [{"type": "field_variable", "name": "VAR", "variable": "canMsg", "variableTypes": ["CANMessage"], "defaultType": "CANMessage"}], "previousStatement": null, "nextStatement": null, "colour": "#2C97DF", "inputsInline": true}, {"type": "can_create_message", "message0": "创建CAN消息 ID %1 数据 %2", "args0": [{"type": "input_value", "name": "ID", "check": "Number"}, {"type": "input_value", "name": "DATA", "check": "Array"}], "output": "CANMessage", "colour": "#2C97DF", "inputsInline": true}, {"type": "can_write", "message0": "发送CAN消息 %1", "args0": [{"type": "input_value", "name": "MSG", "check": "CANMessage"}], "previousStatement": null, "nextStatement": null, "colour": "#2C97DF", "inputsInline": true}, {"type": "can_get_message_id", "message0": "获取CAN消息 %1 的ID", "args0": [{"type": "field_variable", "name": "VAR", "variable": "canMsg", "variableTypes": ["CANMessage"], "defaultType": "CANMessage"}], "output": "Number", "colour": "#2C97DF", "inputsInline": true}, {"type": "can_get_message_data", "message0": "获取CAN消息 %1 的数据字节 %2", "args0": [{"type": "field_variable", "name": "VAR", "variable": "canMsg", "variableTypes": ["CANMessage"], "defaultType": "CANMessage"}, {"type": "input_value", "name": "INDEX", "check": "Number"}], "output": "Number", "colour": "#2C97DF", "inputsInline": true}, {"type": "can_set_filter_mask", "message0": "设置CAN过滤器掩码 %1 值 %2", "args0": [{"type": "field_dropdown", "name": "TYPE", "options": [["标准", "Standard"], ["扩展", "Extended"]]}, {"type": "input_value", "name": "MASK", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#2C97DF", "inputsInline": true}, {"type": "can_set_filter_id", "message0": "设置CAN过滤器ID %1 值 %2", "args0": [{"type": "field_dropdown", "name": "TYPE", "options": [["标准", "Standard"], ["扩展", "Extended"]]}, {"type": "input_value", "name": "ID", "check": "Number"}], "previousStatement": null, "nextStatement": null, "colour": "#2C97DF", "inputsInline": true}]