{"name": "@aily-project/lib-ds18b20", "nickname": "Dallas温度传感器", "author": "aily Project", "description": "Dallas数字温度传感器库，支持DS18B20、DS18S20、DS1820、DS1822、DS1825、MAX31820、MAX31850等Dallas/MAXIM温度传感器", "version": "1.0.0", "compatibility": {"core": [], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "sensor", "temperature", "DS18B20", "DS18S20", "DS1820", "DS1822", "DS1825", "MAX31820", "MAX31850", "Dallas", "OneWire", "温度", "传感器", "数字温度传感器", "ds18b20_init", "ds18b20_init_pin", "ds18b20_read_temperature_c", "ds18b20_read_temperature_f", "ds18b20_read_temperature_c_pin", "ds18b20_read_temperature_f_pin", "ds18b20_simple_read", "ds18b20_get_device_count", "ds18b20_get_device_count_pin", "ds18b20_read_temperature_by_index", "ds18b20_read_temperature_by_index_pin", "ds18b20_set_resolution", "ds18b20_is_parasite_power"], "scripts": {}, "devDependencies": {}}