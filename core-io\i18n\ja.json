{"toolbox_name": "I/O ピン", "io_pinmode": {"message0": "ピン %1 をモード %2 に設定"}, "io_digitalwrite": {"message0": "ピン %1 にデジタル信号 %2 を出力"}, "io_digitalread": {"message0": "ピン %1 からデジタル信号を読み取る"}, "io_analogwrite": {"message0": "ピン %1 にPWM信号 %2 を出力"}, "io_analogread": {"message0": "ピン %1 からアナログ信号を読み取る"}, "io_pin_digi": {"message0": "デジタルピン %1"}, "io_pin_adc": {"message0": "アナログピン %1"}, "io_pin_pwm": {"message0": "PWMピン %1"}, "io_mode": {"message0": "ピン モード %1"}, "io_state": {"message0": "%1 レベル"}}