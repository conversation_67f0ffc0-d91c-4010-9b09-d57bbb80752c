{"name": "@aily-project/lib-r4-rtc", "nickname": "R4实时时钟", "author": "<PERSON><PERSON><PERSON><PERSON>", "description": "适用于Arduino UNO R4的RTC库，提供精确的时间跟踪、定时回调和时间格式化功能", "version": "1.0.0", "compatibility": {"core": ["renesas_uno:minima", "renesas_uno:unor4wifi"], "voltage": [3.3, 5]}, "keywords": ["aily", "blockly", "rtc", "clock", "time", "calendar", "realtime"], "scripts": {}, "dependencies": {}, "devDependencies": {}}