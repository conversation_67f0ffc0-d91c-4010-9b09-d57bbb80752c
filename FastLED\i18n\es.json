{"toolbox_name": "FastLED", "fastled_init": {"message0": "Inicializar tira RGB Pin %1 Tipo %2 Número de LEDs %3", "args0": [null, {"options": [["WS2812B", "WS2812B"], ["WS2812", "WS2812"], ["WS2811", "WS2811"], ["NEOPIXEL", "NEOPIXEL"], ["WS2801", "WS2801"], ["LPD8806", "LPD8806"], ["APA102", "APA102"]]}, null]}, "fastled_set_pixel": {"message0": "Establecer tira RGB Pin %1 Índice %2 Color %3"}, "fastled_show": {"message0": "Actualizar tira RGB"}, "fastled_clear": {"message0": "Limpiar tira RGB Pin %1"}, "fastled_brightness": {"message0": "Establecer brillo %1"}, "fastled_rgb": {"message0": "Color RGB R %1 G %2 B %3"}, "fastled_preset_color": {"message0": "Color %1"}, "fastled_fill_solid": {"message0": "Rellenar todos los LEDs Pin %1 Color %2"}, "fastled_hsv": {"message0": "Color HSV H %1 S %2 V %3"}, "fastled_rainbow": {"message0": "Efecto arcoíris Pin %1 Tono inicial %2 Incremento %3"}, "fastled_fire_effect": {"message0": "Efecto fuego Pin %1 Intensidad %2 Velocidad de enfriamiento %3"}, "fastled_meteor": {"message0": "Efecto meteoro Pin %1 Color %2 Tamaño del meteoro %3 Desvanecimiento de la cola %4 Velocidad %5"}, "fastled_palette_cycle": {"message0": "Efecto ciclo de paleta Pin %1 Paleta %2 Velocidad %3", "args0": [null, {"options": [["<PERSON><PERSON><PERSON><PERSON>", "RainbowColors_p"], ["<PERSON><PERSON>", "LavaColors_p"], ["Nubes", "CloudColors_p"], ["<PERSON><PERSON>ano", "OceanColors_p"], ["Bosque", "ForestColors_p"], ["Fiesta", "PartyColors_p"], ["<PERSON><PERSON>", "HeatColors_p"]]}, null]}, "fastled_breathing": {"message0": "Efecto de respiración Pin %1 Color %2 Velocidad %3"}, "fastled_twinkle": {"message0": "Efecto centelleo Pin %1 Número de destellos %2 Fondo %3 Color de destello %4"}}