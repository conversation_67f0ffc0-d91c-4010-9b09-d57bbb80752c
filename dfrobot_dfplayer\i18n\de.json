{"toolbox_name": "DFPlayer", "dfplayer_begin": {"message0": "DFPlayer-Modul %1 RX-Pin %2 TX-Pin %3 initialisieren"}, "dfplayer_play": {"message0": "DFPlayer %1 spiele Datei Nummer %2"}, "dfplayer_pause": {"message0": "DFPlayer %1 Pause"}, "dfplayer_start": {"message0": "DFPlayer %1 fortsetzen"}, "dfplayer_stop": {"message0": "DFPlayer %1 stoppen"}, "dfplayer_next": {"message0": "DFPlayer %1 spiele das nächste Lied"}, "dfplayer_previous": {"message0": "DFPlayer %1 spiele das vorherige Lied"}, "dfplayer_volume": {"message0": "DFPlayer %1 Lautstärke auf %2 setzen"}, "dfplayer_volume_up": {"message0": "DFPlayer %1 Lautstärke erhöhen"}, "dfplayer_volume_down": {"message0": "DFPlayer %1 Lautstärke verringern"}, "dfplayer_eq": {"message0": "DFPlayer %1 Equalizer auf %2 setzen", "options": [["Normal", "0"], ["Pop", "1"], ["Rock", "2"], ["Jazz", "3"], ["Klassik", "4"], ["Bass", "5"]]}, "dfplayer_output_device": {"message0": "DFPlayer %1 Ausgabe-Gerät auf %2 setzen", "options": [["GERÄT1", "1"], ["GERÄT2", "2"]]}, "dfplayer_loop": {"message0": "DFPlayer %1 spiele Datei Nummer %2 in einer Schleife"}, "dfplayer_play_folder": {"message0": "DFPlayer %1 spiele Datei %3 aus dem Ordner %2"}, "dfplayer_enable_loop_all": {"message0": "DFPlayer %1 Schleife für alle aktivieren"}, "dfplayer_disable_loop_all": {"message0": "DFPlayer %1 Schleife für alle deaktivieren"}, "dfplayer_play_mp3_folder": {"message0": "DFPlayer %1 spiele Datei %2 aus dem MP3-Ordner"}, "dfplayer_advertise": {"message0": "DFPlayer %1 Werbung %2 abspielen"}, "dfplayer_stop_advertise": {"message0": "DFPlayer %1 Werbung stoppen"}, "dfplayer_play_large_folder": {"message0": "DFPlayer %1 spiele Datei %3 aus dem großen Ordner %2"}, "dfplayer_loop_folder": {"message0": "DFPlayer %1 spiele Ordner %2 in einer Schleife"}, "dfplayer_random_all": {"message0": "DFPlayer %1 spiele alle Dateien zufällig"}, "dfplayer_enable_loop": {"message0": "DFPlayer %1 Schleife aktivieren"}, "dfplayer_disable_loop": {"message0": "DFPlayer %1 Schleife deaktivieren"}, "dfplayer_read_state": {"message0": "DFPlayer %1 Status lesen"}, "dfplayer_read_volume": {"message0": "DFPlayer %1 Lautstärke lesen"}, "dfplayer_read_eq": {"message0": "DFPlayer %1 Equalizer lesen"}, "dfplayer_read_file_counts": {"message0": "DFPlayer %1 Anzahl der Dateien lesen"}, "dfplayer_read_current_file_number": {"message0": "DFPlayer %1 aktuelle Dateinummer lesen"}, "dfplayer_read_file_counts_in_folder": {"message0": "DFPlayer %1 Anzahl der Dateien im Ordner %2 lesen"}, "dfplayer_available": {"message0": "DFPlayer %1 prüfen, ob Nachrichten verfügbar sind"}, "dfplayer_read_type": {"message0": "DFPlayer %1 Nachrichtentyp lesen"}, "dfplayer_read": {"message0": "DFPlayer %1 Nachrichtenparameter lesen"}, "dfplayer_simple_play": {"message0": "Einfaches Abspielen: DFPlayer initialisieren (RX: %1, TX: %2) und Datei %3 abspielen"}}