{"kind": "category", "icon": "fa-light fa-engine", "name": "AFMotor", "colour": "#5C81A6", "contents": [{"kind": "label", "text": "直流电机控制"}, {"kind": "block", "type": "afmotor_dc_init"}, {"kind": "block", "type": "afmotor_dc_run", "inputs": {"SPEED": {"shadow": {"type": "math_number", "fields": {"NUM": 255}}}}}, {"kind": "sep"}, {"kind": "label", "text": "步进电机控制"}, {"kind": "block", "type": "afmotor_stepper_init", "inputs": {"STEPS": {"shadow": {"type": "math_number", "fields": {"NUM": 200}}}}}, {"kind": "block", "type": "afmotor_stepper_setspeed", "inputs": {"RPM": {"shadow": {"type": "math_number", "fields": {"NUM": 10}}}}}, {"kind": "block", "type": "afmotor_stepper_step", "inputs": {"STEPS": {"shadow": {"type": "math_number", "fields": {"NUM": 100}}}}}, {"kind": "block", "type": "afmotor_stepper_onestep"}, {"kind": "block", "type": "afmotor_stepper_release"}]}