{"toolbox_name": "DFPlayer", "dfplayer_begin": {"message0": "Initialiser le module DFPlayer %1 RX broche %2 TX broche %3"}, "dfplayer_play": {"message0": "DFPlayer %1 jouer le fichier numéro %2"}, "dfplayer_pause": {"message0": "DFPlayer %1 mettre en pause"}, "dfplayer_start": {"message0": "DFPlayer %1 continuer la lecture"}, "dfplayer_stop": {"message0": "DFPlayer %1 arrêter la lecture"}, "dfplayer_next": {"message0": "DFPlayer %1 jouer la piste suivante"}, "dfplayer_previous": {"message0": "DFPlayer %1 jouer la piste précédente"}, "dfplayer_volume": {"message0": "DFPlayer %1 régler le volume à %2"}, "dfplayer_volume_up": {"message0": "DFPlayer %1 augmenter le volume"}, "dfplayer_volume_down": {"message0": "DFPlayer %1 diminuer le volume"}, "dfplayer_eq": {"message0": "DFPlayer %1 régler l'égaliseur %2", "options": [["Normal", "0"], ["Pop", "1"], ["Rock", "2"], ["Jazz", "3"], ["Classique", "4"], ["<PERSON><PERSON>", "5"]]}, "dfplayer_output_device": {"message0": "DFPlayer %1 régler le périphérique de sortie à %2", "options": [["DISPOSITIF1", "1"], ["DISPOSITIF2", "2"]]}, "dfplayer_loop": {"message0": "DFPlayer %1 boucle jouer le fichier numéro %2"}, "dfplayer_play_folder": {"message0": "DFPlayer %1 jouer le fichier %3 dans le dossier %2"}, "dfplayer_enable_loop_all": {"message0": "DFPlayer %1 activer la boucle complète"}, "dfplayer_disable_loop_all": {"message0": "DFPlayer %1 désactiver la boucle complète"}, "dfplayer_play_mp3_folder": {"message0": "DFPlayer %1 jouer le fichier %2 dans le dossier MP3"}, "dfplayer_advertise": {"message0": "DFPlayer %1 jouer la publicité %2"}, "dfplayer_stop_advertise": {"message0": "DFPlayer %1 arrêter la publicité"}, "dfplayer_play_large_folder": {"message0": "DFPlayer %1 jouer le fichier %3 dans le grand dossier %2"}, "dfplayer_loop_folder": {"message0": "DFPlayer %1 boucle jouer le dossier %2"}, "dfplayer_random_all": {"message0": "DFPlayer %1 jouer tous les fichiers au hasard"}, "dfplayer_enable_loop": {"message0": "DFPlayer %1 activer la boucle"}, "dfplayer_disable_loop": {"message0": "DFPlayer %1 désactiver la boucle"}, "dfplayer_read_state": {"message0": "DFPlayer %1 lire l'état"}, "dfplayer_read_volume": {"message0": "DFPlayer %1 lire le volume"}, "dfplayer_read_eq": {"message0": "DFPlayer %1 lire l'égaliseur"}, "dfplayer_read_file_counts": {"message0": "DFPlayer %1 lire le nombre de fichiers"}, "dfplayer_read_current_file_number": {"message0": "DFPlayer %1 lire le numéro de fichier actuel"}, "dfplayer_read_file_counts_in_folder": {"message0": "DFPlayer %1 lire le nombre de fichiers dans le dossier %2"}, "dfplayer_available": {"message0": "DFPlayer %1 vérifier s'il y a des messages disponibles"}, "dfplayer_read_type": {"message0": "DFPlayer %1 lire le type de message"}, "dfplayer_read": {"message0": "DFPlayer %1 lire les paramètres du message"}, "dfplayer_simple_play": {"message0": "Lecture simple : initialiser DFPlayer (RX : %1, TX : %2) et jouer le fichier %3"}}