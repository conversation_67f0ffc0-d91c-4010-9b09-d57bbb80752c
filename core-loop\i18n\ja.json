{"toolbox_name": "ループ", "arduino_setup": {"message0": "▶️セットアップ %1"}, "arduino_loop": {"message0": "🔁ループ %1"}, "controls_repeat_ext": {"message0": "%1 回繰り返す", "message1": "%1 実行する"}, "controls_repeat": {"message0": "%1 回繰り返す", "message1": "%1 実行する"}, "controls_whileUntil": {"message0": "%1 %2", "args0": [{"options": [["条件が満たされている間繰り返す", "WHILE"], ["条件が満たされるまで繰り返す", "UNTIL"]]}], "message1": "%1 実行する"}, "controls_for": {"message0": "変数 %1 を %2 から %3 に、毎回 %4 増やす", "message1": "%1 実行する"}, "controls_flow_statements": {"message0": "%1", "args0": [{"options": [["ループを抜ける", "BREAK"], ["次のループに進む", "CONTINUE"]]}]}, "controls_whileForever": {"message0": "🔁 永久にループ %1"}}