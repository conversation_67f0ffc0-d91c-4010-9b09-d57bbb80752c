[{"type": "openjumper_asr_init", "message0": "语音识别软串口初始化 RX引脚：%1 TX引脚：%2", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#03a9f4", "args0": [{"type": "field_dropdown", "name": "RX_PIN", "options": "${board.digitalPins}"}, {"type": "field_dropdown", "name": "TX_PIN", "options": "${board.digitalPins}"}]}, {"type": "openjumper_asr_data", "message0": "语音识别数据解析", "inputsInline": true, "previousStatement": null, "nextStatement": null, "colour": "#03a9f4"}, {"type": "openjumper_asr_rincmd", "message0": "识别到语音 %1", "inputsInline": true, "colour": "#03a9f4", "args0": [{"type": "field_dropdown", "name": "ASR_CMD", "options": [["0.语音管家", "0"], ["1.打开空调", "1"], ["2.关闭空调", "2"], ["3.上下摆风", "3"], ["4.左右摆风", "4"], ["5.制冷模式", "5"], ["6.加热模式", "6"], ["7.调高温度", "7"], ["8.调低温度", "8"], ["9.半小时后关闭", "9"], ["10.一小时后关闭", "10"], ["11.两小时后关闭", "11"], ["12.三小时后关闭", "12"], ["13.十度", "13"], ["14.十一度", "14"], ["15.十二度", "15"], ["16.十三度", "16"], ["17.十四度", "17"], ["18.十五度", "18"], ["19.十六度", "19"], ["20.十七度", "20"], ["21.十八度", "21"], ["22.十九度", "22"], ["23.二十度", "23"], ["24.二十一度", "24"], ["25.二十二度", "25"], ["26.二十三度", "26"], ["27.二十四度", "27"], ["28.二十五度", "28"], ["29.二十六度", "29"], ["30.二十七度", "30"], ["31.二十八度", "31"], ["32.打开风扇", "32"], ["33.关闭风扇", "33"], ["34.风扇摇头", "34"], ["35.停止摇头", "35"], ["36.自然风", "36"], ["37.睡眠风", "37"], ["38.正常风", "38"], ["39.打开客厅灯", "39"], ["40.关闭客厅灯", "40"], ["41.打开卧室灯", "41"], ["42.关闭卧室灯", "42"], ["43.打开厨房灯", "43"], ["44.关闭厨房灯", "44"], ["45.打开台灯", "45"], ["46.关闭台灯", "46"], ["47.打开灭蚊器", "47"], ["48.关闭灭蚊器", "48"], ["49.打开加湿器", "49"], ["50.关闭加湿器", "50"], ["51.打开净化器", "51"], ["52.关闭净化器", "52"], ["53.打开氛围灯", "53"], ["54.关闭氛围灯", "54"], ["55.打开电视", "55"], ["56.关闭电视", "56"], ["57.打开插座", "57"], ["58.关闭插座", "58"], ["59.开门", "59"], ["60关门", "60"], ["61.打开热水器", "61"], ["62.关闭热水器", "62"], ["63.黄光", "63"], ["64.红光", "64"], ["65.蓝光", "65"], ["66.绿光", "66"], ["67.彩光", "67"], ["68.<PERSON>", "68"], ["69.暖光", "69"], ["70.亮一点", "70"], ["71.暗一点", "71"], ["72.启动小车", "72"], ["73.关闭小车", "73"], ["74.视觉模式", "74"], ["75.巡线模式", "75"], ["76.跟随模式", "76"], ["77.避障模式", "77"], ["78.网络模式", "78"], ["79.红外模式", "79"], ["80.无线模式", "80"], ["81.蓝牙模式", "81"], ["82.手柄模式", "82"], ["83.快一点", "83"], ["84.慢一点", "84"], ["85.打开小车灯", "85"], ["86.关闭小车灯", "86"], ["87.前进", "87"], ["88.后退", "88"], ["89.左转", "89"], ["90.右转", "90"], ["91.左前进", "91"], ["92.右前进", "92"], ["93.左后退", "93"], ["94.右后退", "94"], ["95.抬起云台", "95"], ["96.放下云台", "96"], ["97.抬高一点", "97"], ["103.放低一点", "98"], ["99.夹取物体", "99"], ["100.放下物体", "100"], ["101.夹紧一点", "101"], ["102.放松一点", "102"], ["103.打开窗帘", "103"], ["104.关闭窗帘", "104"], ["105.打开一点", "105"], ["106.关闭一点", "106"], ["107.观影模式", "107"], ["108.普通模式", "108"], ["109.娱乐模式", "109"], ["110.工作模式", "110"], ["111.我上班啦", "111"], ["112.我回来啦", "112"], ["113.早上好", "113"], ["114.晚安", "114"], ["115.打开晾衣架", "115"], ["116.收起晾衣架", "116"], ["117.打开投影仪", "117"], ["118.关闭投影仪", "117"]]}], "output": "Boolean"}, {"type": "openjumper_asr_state", "message0": "语音识别唤醒状态", "inputsInline": true, "colour": "#03a9f4", "output": "Boolean"}]